#wpbody-content .wrap {
	height: auto !important;
	overflow: initial !important;
	max-width: initial !important;
}

.about-wrap hr {
	border: inherit;
	border-top: 1px solid #ccc;
}
.about-wrap #footer-upgrade {
	display: none;
}
.about-wrap .redux-badge {
	position: absolute;
	top: 0;
	background: #00a2e3;
	padding: 20px;
	color: #efefef;
	margin: 5px 0 0 0;
	-webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
	box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}
.about-wrap .redux-badge::before {
	color: #fff;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	font-size: 80px;
	font-weight: normal;
	width: 165px;
	height: 165px;
	line-height: 165px;
	text-align: center;
	position: absolute;
	top: 0;
	margin: 0;
	vertical-align: middle;
}
.about-wrap .redux-badge .el {
	background: #00a2e3;
	display: block !important;
	font-size: 8em;
	color: #fff;
	margin-bottom: 30px;
}
.about-wrap .redux-badge span {
	font-weight: 600;
	font-size: 14px;
	text-align: center;
	position: absolute;
	bottom: 0;
	background: rgba(50, 50, 49, 0.47);
	left: 0;
	right: 0;
	padding: 8px;
}
.about-wrap .redux-badge small {
	clear: both;
	font-weight: 600;
	font-size: 14px;
	text-align: center;
	position: absolute;
	bottom: 0;
	background: transparent;
	left: 0;
	right: 0;
	padding: 8px;
}
.about-wrap div.icon {
	width: 0 !important;
	padding: 0;
	margin: 20px 0 !important;
}
.about-wrap figure figcaption::before, .about-wrap figure figcaption::after {
	position: absolute;
	top: 15px;
	right: 15px;
	bottom: 15px;
	left: 15px;
	content: "";
	opacity: 0;
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	transition: opacity 0.35s, -webkit-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	transition: opacity 0.35s, transform 0.35s, -webkit-transform 0.35s;
}
.about-wrap figure figcaption::before {
	border-top: 1px solid #fff;
	border-bottom: 1px solid #fff;
	-webkit-transform: scale(0, 1);
	        transform: scale(0, 1);
}
.about-wrap figure figcaption::after {
	border-right: 1px solid #fff;
	border-left: 1px solid #fff;
	-webkit-transform: scale(1, 0);
	        transform: scale(1, 0);
}
.about-wrap figure p {
	padding: 10px;
	text-transform: none;
	opacity: 0;
	margin-top: 0 !important;
	-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;
	transition: opacity 0.35s, -webkit-transform 0.35s;
	transition: opacity 0.35s, transform 0.35s;
	transition: opacity 0.35s, transform 0.35s, -webkit-transform 0.35s;
	-webkit-transform: translate3d(0, 20px, 0);
	        transform: translate3d(0, 20px, 0);
}

.redux-message .twitter-share-button,
p.redux-actions .twitter-share-button {
	margin-top: -3px;
	margin-left: 3px;
	vertical-align: middle;
}
.redux-message a.review-us,
p.redux-actions a.review-us {
	opacity: 0.5;
	text-decoration: none !important;
}
.redux-message a.button-demo,
p.redux-actions a.button-demo {
	margin-left: 15px;
	color: #a00;
}
.redux-message a.button-demo.active,
p.redux-actions a.button-demo.active {
	color: #d98500;
}

#redux-message {
	margin: 5px 0 15px;
	margin-top: 15px !important;
	display: block !important;
	border-color: #00a2e3;
}
#redux-message h4 {
	margin-top: 0.5em;
}

.redux-product {
	margin-bottom: 15px;
	border: 1px solid #ccc;
	background: #fff;
	padding: 0 20px;
	min-width: 350px;
	float: left;
	margin-right: 20px;
}
.redux-product .name {
	color: #23282d;
	font-size: 32px;
	font-weight: 100;
	margin: 10px 0 0;
	line-height: 1.3;
	word-wrap: break-word;
	overflow-wrap: break-word;
	text-align: left;
}
.redux-product .version {
	color: #72777c;
	font-size: 13px;
	font-weight: 400;
	float: none;
	display: inline-block;
	margin-left: 10px;
}
.redux-product .author {
	margin: 15px 0 25px;
	color: #72777c;
	font-size: 16px;
	font-weight: 400;
	line-height: inherit;
}
.redux-product .type {
	margin-left: 5px;
	background-color: #f0ad4e;
	display: inline;
	padding: 0.2em 0.5em 0.2em;
	font-weight: 400;
	line-height: 1;
	font-size: 12px;
	color: #fff !important;
	text-align: center;
	white-space: nowrap;
	vertical-align: baseline;
	border-radius: 0.25em;
}
.redux-product .type.theme {
	background-color: #0099d5;
}

.hide {
	display: none !important;
}

@media screen and (max-width: 500px) {
	.about-wrap h2 .nav-tab {
		width: 100%;
		padding: 0;
		height: 40px;
		line-height: 40px;
		text-align: center;
		margin: 10px 0;
	}
	.about-wrap h2.nav-tab-wrapper {
		padding: 0;
		width: 100%;
	}
}
/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInJlZHV4LXdlbGNvbWUuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFLQTtDQUNJO0NBQ0E7Q0FDQTs7O0FBSUE7Q0FDSTtDQUNBOztBQUdKO0NBQ0k7O0FBR0o7Q0FDSTtDQUNBO0NBRUE7Q0FDQTtDQUVBO0NBQ0E7Q0FDQTtDQUNBOztBQUVBO0NBQ0k7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7O0FBR0o7Q0FDSTtDQUNBO0NBQ0E7Q0FDQTtDQUNBOztBQUdKO0NBQ0k7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBOztBQUdKO0NBQ0k7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7O0FBSVI7Q0FDSTtDQUNBO0NBQ0E7O0FBS0k7Q0FFSTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7O0FBR0o7Q0FDSTtDQUNBO0NBQ0E7O0FBR0o7Q0FDSTtDQUNBO0NBQ0E7O0FBSVI7Q0FDSTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTs7O0FBT1I7QUFBQTtDQUNJO0NBQ0E7Q0FDQTs7QUFJQTtBQUFBO0NBQ0k7Q0FDQTs7QUFHSjtBQUFBO0NBQ0k7Q0FDQTs7QUFFQTtBQUFBO0NBQ0k7OztBQU1oQjtDQUNJO0NBQ0E7Q0FDQTtDQUNBOztBQUVBO0NBQ0k7OztBQUlSO0NBQ0k7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7O0FBRUE7Q0FDSTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBOztBQUdKO0NBQ0k7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBOztBQUdKO0NBQ0k7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTs7QUFHSjtDQUNJO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTs7QUFFQTtDQUNJOzs7QUFLWjtDQUNJOzs7QUFHSjtDQUVRO0VBQ0k7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBOztDQUdKO0VBQ0k7RUFDQSIsImZpbGUiOiJyZWR1eC13ZWxjb21lLmNzcyJ9 */

/*# sourceMappingURL=redux-welcome.css.map */
