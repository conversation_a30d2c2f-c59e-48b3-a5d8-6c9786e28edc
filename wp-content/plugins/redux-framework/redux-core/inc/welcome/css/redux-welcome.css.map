{"version": 3, "sources": ["redux-welcome.css", "redux-welcome.scss"], "names": [], "mappings": "AAKA;CCOI,uBAAA;CACI,4BAAA;CACA,6BAAA;ADVR;;AAQI;CCSA,eAAA;CACI,0BAAA;ADbR;ACiBQ;CAEA,aAAA;ADhBR;ACmBQ;CDTA,kBAAA;CCWA,MAAA;CAEI,mBAAA;CACA,aAAA;CAEA,cAAA;CACA,iBAAA;CACA,gDAAA;CACA,wCAAA;ADnBZ;ACqBY;CACA,WAAA;CACA,mCAAA;CACA,kCAAA;CDTA,eAAA;CCYJ,mBAAA;CACI,YAAA;CACA,aAAA;CACA,kBAAA;CACA,kBAAA;CACA,kBAAA;CDVA,MAAA;CCaJ,SAAA;CACI,sBAAA;ADrBZ;ACwBY;CACA,mBAAA;CACA,yBAAA;CACA,cAAA;CACA,WAAA;CACA,mBAAA;ADtBZ;AC0BY;CACA,gBAAA;CACA,eAAA;CACA,kBAAA;CACA,kBAAA;CACA,SAAA;CACA,kCAAA;CACA,OAAA;CACA,QAAA;CACA,YAAA;ADxBZ;AC6BQ;CACA,WAAA;CACA,gBAAA;CDdI,eAAA;CCmBA,kBAAA;CAEI,kBAAA;CACA,SAAA;CACA,uBAAA;CACA,OAAA;CACA,QAAA;CACA,YAAA;AD/BhB;AAgBI;CCqBQ,mBAAA;CACI,UAAA;CACA,yBAAA;ADlChB;ACwCgB;CDnBA,kBAAA;CCwBR,SAAA;CACI,WAAA;CACA,YAAA;CACA,UAAA;CACA,WAAA;CACA,UAAA;CACA,0DAAA;CACA,kDAAA;CAAA,0CAAA;CAAA,mEAAA;ADzCZ;ACgDI;CAAA,0BAAA;CACI,6BAAA;CACA,8BAAA;SAAA,sBAAA;AD7CR;ACkDQ;CAAA,4BAAA;CACI,2BAAA;CACA,8BAAA;SAAA,sBAAA;AD/CZ;ACmDY;CACA,aAAA;CD1BA,oBAAA;CC4BA,UAAA;CAAA,wBAAA;CACI,0DAAA;CDzBJ,kDAAA;CAAA,0CAAA;CAAA,mEAAA;CACA,0CAAA;SAAA,kCAAA;AAtBZ;;AC0DI;;CACI,gBAAA;CD5BA,gBAAA;CACA,sBAAA;AAzBR;AC2DI;;CACA,YAAA;CACA,gCAAA;ADxDJ;AA6BQ;;CC+BJ,iBAAA;CACI,WAAA;ADxDR;AC0DQ;;CACA,cAAA;ADvDR;;AC8DI;CACI,kBAAA;CACA,2BAAA;CACA,yBAAA;CACA,qBAAA;AD3DR;AC6DQ;CD9BA,iBAAA;AA5BR;;AAgCA;CCiCQ,mBAAA;CACA,sBAAA;CD/BJ,gBAAA;CCkCA,eAAA;CACI,gBAAA;CACA,WAAA;CACA,kBAAA;AD9DR;ACgEQ;CACA,cAAA;CACA,eAAA;CACA,gBAAA;CACA,gBAAA;CACA,gBAAA;CACA,qBAAA;CACA,yBAAA;CDhCA,gBAAA;AA7BR;AAgCI;CACI,cAAA;CCoCR,eAAA;CACI,gBAAA;CDlCI,WAAA;CACA,qBAAA;CCoCR,iBAAA;ADjEA;ACqEY;CACA,mBAAA;CACA,cAAA;CACA,eAAA;CACA,gBAAA;CDnCJ,oBAAA;AA/BR;ACuEY;CDpCJ,gBAAA;CACA,yBAAA;CACA,eAAA;CACA,0BAAA;CACA,gBAAA;CACA,cAAA;CACA,eAAA;CACA,sBAAA;CACA,kBAAA;CACA,mBAAA;CACA,wBAAA;CACA,qBAAA;AAhCR;AAkCQ;CACI,yBAAA;AAhCZ;;AAqCA;CACI,wBAAA;AAlCJ;;AAqCA;CAEQ;EACI,WAAA;EACA,UAAA;EACA,YAAA;EACA,iBAAA;EACA,kBAAA;EACA,cAAA;CAnCX;CAsCO;EACI,UAAA;EACA,WAAA;CApCX;AACD;AACA,61CAA61C", "file": "redux-welcome.css", "sourcesContent": ["#wpbody-content .wrap {\n\theight: auto !important;\n\toverflow: initial !important;\n\tmax-width: initial !important;\n}\n\n.about-wrap hr {\n\tborder: inherit;\n\tborder-top: 1px solid #ccc;\n}\n.about-wrap #footer-upgrade {\n\tdisplay: none;\n}\n.about-wrap .redux-badge {\n\tposition: absolute;\n\ttop: 0;\n\tbackground: #00a2e3;\n\tpadding: 20px;\n\tcolor: #efefef;\n\tmargin: 5px 0 0 0;\n\t-webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\n\tbox-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\n}\n.about-wrap .redux-badge::before {\n\tcolor: #fff;\n\t-webkit-font-smoothing: antialiased;\n\t-moz-osx-font-smoothing: grayscale;\n\tfont-size: 80px;\n\tfont-weight: normal;\n\twidth: 165px;\n\theight: 165px;\n\tline-height: 165px;\n\ttext-align: center;\n\tposition: absolute;\n\ttop: 0;\n\tmargin: 0;\n\tvertical-align: middle;\n}\n.about-wrap .redux-badge .el {\n\tbackground: #00a2e3;\n\tdisplay: block !important;\n\tfont-size: 8em;\n\tcolor: #fff;\n\tmargin-bottom: 30px;\n}\n.about-wrap .redux-badge span {\n\tfont-weight: 600;\n\tfont-size: 14px;\n\ttext-align: center;\n\tposition: absolute;\n\tbottom: 0;\n\tbackground: rgba(50, 50, 49, 0.47);\n\tleft: 0;\n\tright: 0;\n\tpadding: 8px;\n}\n.about-wrap .redux-badge small {\n\tclear: both;\n\tfont-weight: 600;\n\tfont-size: 14px;\n\ttext-align: center;\n\tposition: absolute;\n\tbottom: 0;\n\tbackground: transparent;\n\tleft: 0;\n\tright: 0;\n\tpadding: 8px;\n}\n.about-wrap div.icon {\n\twidth: 0 !important;\n\tpadding: 0;\n\tmargin: 20px 0 !important;\n}\n.about-wrap figure figcaption::before, .about-wrap figure figcaption::after {\n\tposition: absolute;\n\ttop: 15px;\n\tright: 15px;\n\tbottom: 15px;\n\tleft: 15px;\n\tcontent: \"\";\n\topacity: 0;\n\t-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;\n\ttransition: opacity 0.35s, -webkit-transform 0.35s;\n\ttransition: opacity 0.35s, transform 0.35s;\n\ttransition: opacity 0.35s, transform 0.35s, -webkit-transform 0.35s;\n}\n.about-wrap figure figcaption::before {\n\tborder-top: 1px solid #fff;\n\tborder-bottom: 1px solid #fff;\n\t-webkit-transform: scale(0, 1);\n\t        transform: scale(0, 1);\n}\n.about-wrap figure figcaption::after {\n\tborder-right: 1px solid #fff;\n\tborder-left: 1px solid #fff;\n\t-webkit-transform: scale(1, 0);\n\t        transform: scale(1, 0);\n}\n.about-wrap figure p {\n\tpadding: 10px;\n\ttext-transform: none;\n\topacity: 0;\n\tmargin-top: 0 !important;\n\t-webkit-transition: opacity 0.35s, -webkit-transform 0.35s;\n\ttransition: opacity 0.35s, -webkit-transform 0.35s;\n\ttransition: opacity 0.35s, transform 0.35s;\n\ttransition: opacity 0.35s, transform 0.35s, -webkit-transform 0.35s;\n\t-webkit-transform: translate3d(0, 20px, 0);\n\t        transform: translate3d(0, 20px, 0);\n}\n\n.redux-message .twitter-share-button,\np.redux-actions .twitter-share-button {\n\tmargin-top: -3px;\n\tmargin-left: 3px;\n\tvertical-align: middle;\n}\n.redux-message a.review-us,\np.redux-actions a.review-us {\n\topacity: 0.5;\n\ttext-decoration: none !important;\n}\n.redux-message a.button-demo,\np.redux-actions a.button-demo {\n\tmargin-left: 15px;\n\tcolor: #a00;\n}\n.redux-message a.button-demo.active,\np.redux-actions a.button-demo.active {\n\tcolor: #d98500;\n}\n\n#redux-message {\n\tmargin: 5px 0 15px;\n\tmargin-top: 15px !important;\n\tdisplay: block !important;\n\tborder-color: #00a2e3;\n}\n#redux-message h4 {\n\tmargin-top: 0.5em;\n}\n\n.redux-product {\n\tmargin-bottom: 15px;\n\tborder: 1px solid #ccc;\n\tbackground: #fff;\n\tpadding: 0 20px;\n\tmin-width: 350px;\n\tfloat: left;\n\tmargin-right: 20px;\n}\n.redux-product .name {\n\tcolor: #23282d;\n\tfont-size: 32px;\n\tfont-weight: 100;\n\tmargin: 10px 0 0;\n\tline-height: 1.3;\n\tword-wrap: break-word;\n\toverflow-wrap: break-word;\n\ttext-align: left;\n}\n.redux-product .version {\n\tcolor: #72777c;\n\tfont-size: 13px;\n\tfont-weight: 400;\n\tfloat: none;\n\tdisplay: inline-block;\n\tmargin-left: 10px;\n}\n.redux-product .author {\n\tmargin: 15px 0 25px;\n\tcolor: #72777c;\n\tfont-size: 16px;\n\tfont-weight: 400;\n\tline-height: inherit;\n}\n.redux-product .type {\n\tmargin-left: 5px;\n\tbackground-color: #f0ad4e;\n\tdisplay: inline;\n\tpadding: 0.2em 0.5em 0.2em;\n\tfont-weight: 400;\n\tline-height: 1;\n\tfont-size: 12px;\n\tcolor: #fff !important;\n\ttext-align: center;\n\twhite-space: nowrap;\n\tvertical-align: baseline;\n\tborder-radius: 0.25em;\n}\n.redux-product .type.theme {\n\tbackground-color: #0099d5;\n}\n\n.hide {\n\tdisplay: none !important;\n}\n\n@media screen and (max-width: 500px) {\n\t.about-wrap h2 .nav-tab {\n\t\twidth: 100%;\n\t\tpadding: 0;\n\t\theight: 40px;\n\t\tline-height: 40px;\n\t\ttext-align: center;\n\t\tmargin: 10px 0;\n\t}\n\t.about-wrap h2.nav-tab-wrapper {\n\t\tpadding: 0;\n\t\twidth: 100%;\n\t}\n}\n/*# sourceMappingURL=data:application/json;charset=utf8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInJlZHV4LXdlbGNvbWUuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFLQTtDQUNJO0NBQ0E7Q0FDQTs7O0FBSUE7Q0FDSTtDQUNBOztBQUdKO0NBQ0k7O0FBR0o7Q0FDSTtDQUNBO0NBRUE7Q0FDQTtDQUVBO0NBQ0E7Q0FDQTtDQUNBOztBQUVBO0NBQ0k7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7O0FBR0o7Q0FDSTtDQUNBO0NBQ0E7Q0FDQTtDQUNBOztBQUdKO0NBQ0k7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBOztBQUdKO0NBQ0k7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7O0FBSVI7Q0FDSTtDQUNBO0NBQ0E7O0FBS0k7Q0FFSTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7O0FBR0o7Q0FDSTtDQUNBO0NBQ0E7O0FBR0o7Q0FDSTtDQUNBO0NBQ0E7O0FBSVI7Q0FDSTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTs7O0FBT1I7QUFBQTtDQUNJO0NBQ0E7Q0FDQTs7QUFJQTtBQUFBO0NBQ0k7Q0FDQTs7QUFHSjtBQUFBO0NBQ0k7Q0FDQTs7QUFFQTtBQUFBO0NBQ0k7OztBQU1oQjtDQUNJO0NBQ0E7Q0FDQTtDQUNBOztBQUVBO0NBQ0k7OztBQUlSO0NBQ0k7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7O0FBRUE7Q0FDSTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBOztBQUdKO0NBQ0k7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBOztBQUdKO0NBQ0k7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTs7QUFHSjtDQUNJO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTtDQUNBO0NBQ0E7Q0FDQTs7QUFFQTtDQUNJOzs7QUFLWjtDQUNJOzs7QUFHSjtDQUVRO0VBQ0k7RUFDQTtFQUNBO0VBQ0E7RUFDQTtFQUNBOztDQUdKO0VBQ0k7RUFDQSIsImZpbGUiOiJyZWR1eC13ZWxjb21lLmNzcyJ9 */\n\n/*# sourceMappingURL=redux-welcome.css.map */\n", "$green: #7ad03a;\r\n$red: #a00;\r\n$orange: #ffba00;\r\n$blue: #2ea2cc;\r\n\r\n#wpbody-content .wrap {\r\n    height: auto !important;\r\n    overflow: initial !important;\r\n    max-width: initial !important;\r\n}\r\n\r\n.about-wrap {\r\n    hr {\r\n        border: inherit;\r\n        border-top: 1px solid #ccc;\r\n    }\r\n\r\n    #footer-upgrade {\r\n        display: none;\r\n    }\r\n\r\n    .redux-badge {\r\n        position: absolute;\r\n        top: 0;\r\n\r\n        background: #00a2e3;\r\n        padding: 20px;\r\n\r\n        color: #efefef;\r\n        margin: 5px 0 0 0;\r\n        -webkit-box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\r\n        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);\r\n\r\n        &::before {\r\n            color: #fff;\r\n            -webkit-font-smoothing: antialiased;\r\n            -moz-osx-font-smoothing: grayscale;\r\n            font-size: 80px;\r\n            font-weight: normal;\r\n            width: 165px;\r\n            height: 165px;\r\n            line-height: 165px;\r\n            text-align: center;\r\n            position: absolute;\r\n            top: 0;\r\n            margin: 0;\r\n            vertical-align: middle;\r\n        }\r\n\r\n        .el {\r\n            background: #00a2e3;\r\n            display: block !important;\r\n            font-size: 8em;\r\n            color: #fff;\r\n            margin-bottom: 30px;\r\n        }\r\n\r\n        span {\r\n            font-weight: 600;\r\n            font-size: 14px;\r\n            text-align: center;\r\n            position: absolute;\r\n            bottom: 0;\r\n            background: rgba(50, 50, 49, 0.47);\r\n            left: 0;\r\n            right: 0;\r\n            padding: 8px;\r\n        }\r\n\r\n        small {\r\n            clear: both;\r\n            font-weight: 600;\r\n            font-size: 14px;\r\n            text-align: center;\r\n            position: absolute;\r\n            bottom: 0;\r\n            background: transparent;\r\n            left: 0;\r\n            right: 0;\r\n            padding: 8px;\r\n        }\r\n    }\r\n\r\n    div.icon {\r\n        width: 0 !important;\r\n        padding: 0;\r\n        margin: 20px 0 !important;\r\n    }\r\n\r\n    figure {\r\n        figcaption {\r\n            &::before,\r\n            &::after {\r\n                position: absolute;\r\n                top: 15px;\r\n                right: 15px;\r\n                bottom: 15px;\r\n                left: 15px;\r\n                content: \"\";\r\n                opacity: 0;\r\n                -webkit-transition: opacity 0.35s, -webkit-transform 0.35s;\r\n                transition: opacity 0.35s, transform 0.35s;\r\n            }\r\n\r\n            &::before {\r\n                border-top: 1px solid #fff;\r\n                border-bottom: 1px solid #fff;\r\n                transform: scale(0, 1);\r\n            }\r\n\r\n            &::after {\r\n                border-right: 1px solid #fff;\r\n                border-left: 1px solid #fff;\r\n                transform: scale(1, 0);\r\n            }\r\n        }\r\n\r\n        p {\r\n            padding: 10px;\r\n            text-transform: none;\r\n            opacity: 0;\r\n            margin-top: 0 !important;\r\n            -webkit-transition: opacity 0.35s, -webkit-transform 0.35s;\r\n            transition: opacity 0.35s, transform 0.35s;\r\n            transform: translate3d(0, 20px, 0);\r\n        }\r\n    }\r\n}\r\n\r\n.redux-message,\r\np.redux-actions {\r\n    .twitter-share-button {\r\n        margin-top: -3px;\r\n        margin-left: 3px;\r\n        vertical-align: middle;\r\n    }\r\n\r\n    a {\r\n        &.review-us {\r\n            opacity: 0.5;\r\n            text-decoration: none !important;\r\n        }\r\n\r\n        &.button-demo {\r\n            margin-left: 15px;\r\n            color: #a00;\r\n\r\n            &.active {\r\n                color: #d98500;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n#redux-message {\r\n    margin: 5px 0 15px;\r\n    margin-top: 15px !important;\r\n    display: block !important;\r\n    border-color: #00a2e3;\r\n\r\n    h4 {\r\n        margin-top: 0.5em;\r\n    }\r\n}\r\n\r\n.redux-product {\r\n    margin-bottom: 15px;\r\n    border: 1px solid #ccc;\r\n    background: #fff;\r\n    padding: 0 20px;\r\n    min-width: 350px;\r\n    float: left;\r\n    margin-right: 20px;\r\n\r\n    .name {\r\n        color: #23282d;\r\n        font-size: 32px;\r\n        font-weight: 100;\r\n        margin: 10px 0 0;\r\n        line-height: 1.3;\r\n        word-wrap: break-word;\r\n        overflow-wrap: break-word;\r\n        text-align: left;\r\n    }\r\n\r\n    .version {\r\n        color: #72777c;\r\n        font-size: 13px;\r\n        font-weight: 400;\r\n        float: none;\r\n        display: inline-block;\r\n        margin-left: 10px;\r\n    }\r\n\r\n    .author {\r\n        margin: 15px 0 25px;\r\n        color: #72777c;\r\n        font-size: 16px;\r\n        font-weight: 400;\r\n        line-height: inherit;\r\n    }\r\n\r\n    .type {\r\n        margin-left: 5px;\r\n        background-color: #f0ad4e;\r\n        display: inline;\r\n        padding: 0.2em 0.5em 0.2em;\r\n        font-weight: 400;\r\n        line-height: 1;\r\n        font-size: 12px;\r\n        color: #fff !important;\r\n        text-align: center;\r\n        white-space: nowrap;\r\n        vertical-align: baseline;\r\n        border-radius: 0.25em;\r\n\r\n        &.theme {\r\n            background-color: #0099d5;\r\n        }\r\n    }\r\n}\r\n\r\n.hide {\r\n    display: none !important;\r\n}\r\n\r\n@media screen and (max-width: 500px) {\r\n    .about-wrap h2 {\r\n        .nav-tab {\r\n            width: 100%;\r\n            padding: 0;\r\n            height: 40px;\r\n            line-height: 40px;\r\n            text-align: center;\r\n            margin: 10px 0;\r\n        }\r\n\r\n        &.nav-tab-wrapper {\r\n            padding: 0;\r\n            width: 100%;\r\n        }\r\n    }\r\n}\r\n"]}