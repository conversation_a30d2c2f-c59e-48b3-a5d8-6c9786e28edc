<?php
/**
 * Redux Multi-Media Sample config.
 *
 * For full documentation, please visit: https://devs.redux.io
 *
 * @package Redux
 */

// phpcs:disable
defined( 'ABSPATH' ) || exit;

Redux::set_section(
	$opt_name,
	array(
		'title'      => esc_html__( 'Multi Media', 'your-textdomain-here' ),
		'desc'       => esc_html__( 'For full documentation on this field, visit: ', 'your-textdomain-here' ) . '<a href="https://devs.redux.io/core-extensions/multi-media.html" target="_blank">https://devs.redux.io/core-extensions/multi-media.html</a>',
		'subsection' => true,
		'fields'     => array(
			array(
				'id'              => 'opt-multi-media',
				'type'            => 'multi_media',
				'title'           => esc_html__( 'Multi Media Selector', 'your-textdomain-here' ),
				'subtitle'        => esc_html__( 'Alternative media field which allows for multi selections', 'your-textdomain-here' ),
				'desc'            => esc_html__( 'max_file_limit has been set to 5.', 'your-textdomain-here' ),
				'max_file_upload' => 5,
			),
		),
	)
);
// phpcs:enable
