/*! For license information please see editor.min.js.LICENSE.txt */
(()=>{"use strict";var e={3994:(e,t,n)=>{n.d(t,{i:()=>r});var r=window.droip;n(206)},206:(e,t,n)=>{n.r(t);var r={};function i(e,t){return function(){return e.apply(t,arguments)}}n.r(r),n.d(r,{hasBrowserEnv:()=>re,hasStandardBrowserEnv:()=>ie,hasStandardBrowserWebWorkerEnv:()=>ae});const{toString:o}=Object.prototype,{getPrototypeOf:a}=Object,s=(l=Object.create(null),e=>{const t=o.call(e);return l[t]||(l[t]=t.slice(8,-1).toLowerCase())});var l;const c=e=>(e=e.toLowerCase(),t=>s(t)===e),u=e=>t=>typeof t===e,{isArray:p}=Array,m=u("undefined"),f=c("ArrayBuffer"),d=u("string"),g=u("function"),y=u("number"),b=e=>null!==e&&"object"==typeof e,h=e=>{if("object"!==s(e))return!1;const t=a(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||Symbol.toStringTag in e||Symbol.iterator in e)},v=c("Date"),O=c("File"),w=c("Blob"),S=c("FileList"),E=c("URLSearchParams");function j(e,t,{allOwnKeys:n=!1}={}){if(null==e)return;let r,i;if("object"!=typeof e&&(e=[e]),p(e))for(r=0,i=e.length;r<i;r++)t.call(null,e[r],r,e);else{const i=n?Object.getOwnPropertyNames(e):Object.keys(e),o=i.length;let a;for(r=0;r<o;r++)a=i[r],t.call(null,e[a],a,e)}}function C(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,i=n.length;for(;i-- >0;)if(r=n[i],t===r.toLowerCase())return r;return null}const A="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,L=e=>!m(e)&&e!==A,x=(P="undefined"!=typeof Uint8Array&&a(Uint8Array),e=>P&&e instanceof P);var P;const R=c("HTMLFormElement"),M=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),N=c("RegExp"),Z=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};j(n,((n,i)=>{let o;!1!==(o=t(n,i,e))&&(r[i]=o||n)})),Object.defineProperties(e,r)},T="abcdefghijklmnopqrstuvwxyz",I="0123456789",k={DIGIT:I,ALPHA:T,ALPHA_DIGIT:T+T.toUpperCase()+I},D=c("AsyncFunction"),_={isArray:p,isArrayBuffer:f,isBuffer:function(e){return null!==e&&!m(e)&&null!==e.constructor&&!m(e.constructor)&&g(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||g(e.append)&&("formdata"===(t=s(e))||"object"===t&&g(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&f(e.buffer),t},isString:d,isNumber:y,isBoolean:e=>!0===e||!1===e,isObject:b,isPlainObject:h,isUndefined:m,isDate:v,isFile:O,isBlob:w,isRegExp:N,isFunction:g,isStream:e=>b(e)&&g(e.pipe),isURLSearchParams:E,isTypedArray:x,isFileList:S,forEach:j,merge:function e(){const{caseless:t}=L(this)&&this||{},n={},r=(r,i)=>{const o=t&&C(n,i)||i;h(n[o])&&h(r)?n[o]=e(n[o],r):h(r)?n[o]=e({},r):p(r)?n[o]=r.slice():n[o]=r};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&j(arguments[e],r);return n},extend:(e,t,n,{allOwnKeys:r}={})=>(j(t,((t,r)=>{n&&g(t)?e[r]=i(t,n):e[r]=t}),{allOwnKeys:r}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let i,o,s;const l={};if(t=t||{},null==e)return t;do{for(i=Object.getOwnPropertyNames(e),o=i.length;o-- >0;)s=i[o],r&&!r(s,e,t)||l[s]||(t[s]=e[s],l[s]=!0);e=!1!==n&&a(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:s,kindOfTest:c,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(p(e))return e;let t=e.length;if(!y(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Symbol.iterator]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:R,hasOwnProperty:M,hasOwnProp:M,reduceDescriptors:Z,freezeMethods:e=>{Z(e,((t,n)=>{if(g(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];g(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return p(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>(e=+e,Number.isFinite(e)?e:t),findKey:C,global:A,isContextDefined:L,ALPHABET:k,generateString:(e=16,t=k.ALPHA_DIGIT)=>{let n="";const{length:r}=t;for(;e--;)n+=t[Math.random()*r|0];return n},isSpecCompliantForm:function(e){return!!(e&&g(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(b(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const i=p(e)?[]:{};return j(e,((e,t)=>{const o=n(e,r+1);!m(o)&&(i[t]=o)})),t[r]=void 0,i}}return e};return n(e,0)},isAsyncFn:D,isThenable:e=>e&&(b(e)||g(e))&&g(e.then)&&g(e.catch)};function W(e,t,n,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i)}_.inherits(W,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:_.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const F=W.prototype,z={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{z[e]={value:e}})),Object.defineProperties(W,z),Object.defineProperty(F,"isAxiosError",{value:!0}),W.from=(e,t,n,r,i,o)=>{const a=Object.create(F);return _.toFlatObject(e,a,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),W.call(a,e.message,t,n,r,i),a.cause=e,a.name=e.name,o&&Object.assign(a,o),a};const U=W;function B(e){return _.isPlainObject(e)||_.isArray(e)}function H(e){return _.endsWith(e,"[]")?e.slice(0,-2):e}function q(e,t,n){return e?e.concat(t).map((function(e,t){return e=H(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const V=_.toFlatObject(_,{},null,(function(e){return/^is[A-Z]/.test(e)})),Q=function(e,t,n){if(!_.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=_.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!_.isUndefined(t[e])}))).metaTokens,i=n.visitor||c,o=n.dots,a=n.indexes,s=(n.Blob||"undefined"!=typeof Blob&&Blob)&&_.isSpecCompliantForm(t);if(!_.isFunction(i))throw new TypeError("visitor must be a function");function l(e){if(null===e)return"";if(_.isDate(e))return e.toISOString();if(!s&&_.isBlob(e))throw new U("Blob is not supported. Use a Buffer instead.");return _.isArrayBuffer(e)||_.isTypedArray(e)?s&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function c(e,n,i){let s=e;if(e&&!i&&"object"==typeof e)if(_.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(_.isArray(e)&&function(e){return _.isArray(e)&&!e.some(B)}(e)||(_.isFileList(e)||_.endsWith(n,"[]"))&&(s=_.toArray(e)))return n=H(n),s.forEach((function(e,r){!_.isUndefined(e)&&null!==e&&t.append(!0===a?q([n],r,o):null===a?n:n+"[]",l(e))})),!1;return!!B(e)||(t.append(q(i,n,o),l(e)),!1)}const u=[],p=Object.assign(V,{defaultVisitor:c,convertValue:l,isVisitable:B});if(!_.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!_.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+r.join("."));u.push(n),_.forEach(n,(function(n,o){!0===(!(_.isUndefined(n)||null===n)&&i.call(t,n,_.isString(o)?o.trim():o,r,p))&&e(n,r?r.concat(o):[o])})),u.pop()}}(e),t};function G(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function $(e,t){this._pairs=[],e&&Q(e,this,t)}const J=$.prototype;J.append=function(e,t){this._pairs.push([e,t])},J.toString=function(e){const t=e?function(t){return e.call(this,t,G)}:G;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const K=$;function X(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Y(e,t,n){if(!t)return e;const r=n&&n.encode||X,i=n&&n.serialize;let o;if(o=i?i(t,n):_.isURLSearchParams(t)?t.toString():new K(t,n).toString(r),o){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}const ee=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){_.forEach(this.handlers,(function(t){null!==t&&e(t)}))}},te={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ne={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:K,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},re="undefined"!=typeof window&&"undefined"!=typeof document,ie=(oe="undefined"!=typeof navigator&&navigator.product,re&&["ReactNative","NativeScript","NS"].indexOf(oe)<0);var oe;const ae="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,se={...r,...ne},le=function(e){function t(e,n,r,i){let o=e[i++];const a=Number.isFinite(+o),s=i>=e.length;return o=!o&&_.isArray(r)?r.length:o,s?(_.hasOwnProp(r,o)?r[o]=[r[o],n]:r[o]=n,!a):(r[o]&&_.isObject(r[o])||(r[o]=[]),t(e,n,r[o],i)&&_.isArray(r[o])&&(r[o]=function(e){const t={},n=Object.keys(e);let r;const i=n.length;let o;for(r=0;r<i;r++)o=n[r],t[o]=e[o];return t}(r[o])),!a)}if(_.isFormData(e)&&_.isFunction(e.entries)){const n={};return _.forEachEntry(e,((e,r)=>{t(function(e){return _.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null},ce={transitional:te,adapter:["xhr","http"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,i=_.isObject(e);if(i&&_.isHTMLForm(e)&&(e=new FormData(e)),_.isFormData(e))return r&&r?JSON.stringify(le(e)):e;if(_.isArrayBuffer(e)||_.isBuffer(e)||_.isStream(e)||_.isFile(e)||_.isBlob(e))return e;if(_.isArrayBufferView(e))return e.buffer;if(_.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(i){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return Q(e,new se.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return se.isNode&&_.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((o=_.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return Q(o?{"files[]":e}:e,t&&new t,this.formSerializer)}}return i||r?(t.setContentType("application/json",!1),function(e,t,n){if(_.isString(e))try{return(0,JSON.parse)(e),_.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||ce.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(e&&_.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(e){if(n){if("SyntaxError"===e.name)throw U.from(e,U.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:se.classes.FormData,Blob:se.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};_.forEach(["delete","get","head","post","put","patch"],(e=>{ce.headers[e]={}}));const ue=ce,pe=_.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),me=Symbol("internals");function fe(e){return e&&String(e).trim().toLowerCase()}function de(e){return!1===e||null==e?e:_.isArray(e)?e.map(de):String(e)}function ge(e,t,n,r,i){return _.isFunction(r)?r.call(this,t,n):(i&&(t=n),_.isString(t)?_.isString(r)?-1!==t.indexOf(r):_.isRegExp(r)?r.test(t):void 0:void 0)}class ye{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function i(e,t,n){const i=fe(t);if(!i)throw new Error("header name must be a non-empty string");const o=_.findKey(r,i);(!o||void 0===r[o]||!0===n||void 0===n&&!1!==r[o])&&(r[o||t]=de(e))}const o=(e,t)=>_.forEach(e,((e,n)=>i(e,n,t)));return _.isPlainObject(e)||e instanceof this.constructor?o(e,t):_.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim())?o((e=>{const t={};let n,r,i;return e&&e.split("\n").forEach((function(e){i=e.indexOf(":"),n=e.substring(0,i).trim().toLowerCase(),r=e.substring(i+1).trim(),!n||t[n]&&pe[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t):null!=e&&i(t,e,n),this}get(e,t){if(e=fe(e)){const n=_.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(_.isFunction(t))return t.call(this,e,n);if(_.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=fe(e)){const n=_.findKey(this,e);return!(!n||void 0===this[n]||t&&!ge(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function i(e){if(e=fe(e)){const i=_.findKey(n,e);!i||t&&!ge(0,n[i],i,t)||(delete n[i],r=!0)}}return _.isArray(e)?e.forEach(i):i(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const i=t[n];e&&!ge(0,this[i],i,e,!0)||(delete this[i],r=!0)}return r}normalize(e){const t=this,n={};return _.forEach(this,((r,i)=>{const o=_.findKey(n,i);if(o)return t[o]=de(r),void delete t[i];const a=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(i):String(i).trim();a!==i&&delete t[i],t[a]=de(r),n[a]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return _.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&_.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,t])=>e+": "+t)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach((e=>n.set(e))),n}static accessor(e){const t=(this[me]=this[me]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=fe(e);t[r]||(function(e,t){const n=_.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,i){return this[r].call(this,t,e,n,i)},configurable:!0})}))}(n,e),t[r]=!0)}return _.isArray(e)?e.forEach(r):r(e),this}}ye.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),_.reduceDescriptors(ye.prototype,(({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}})),_.freezeMethods(ye);const be=ye;function he(e,t){const n=this||ue,r=t||n,i=be.from(r.headers);let o=r.data;return _.forEach(e,(function(e){o=e.call(n,o,i.normalize(),t?t.status:void 0)})),i.normalize(),o}function ve(e){return!(!e||!e.__CANCEL__)}function Oe(e,t,n){U.call(this,null==e?"canceled":e,U.ERR_CANCELED,t,n),this.name="CanceledError"}_.inherits(Oe,U,{__CANCEL__:!0});const we=Oe,Se=se.hasStandardBrowserEnv?{write(e,t,n,r,i,o){const a=[e+"="+encodeURIComponent(t)];_.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),_.isString(r)&&a.push("path="+r),_.isString(i)&&a.push("domain="+i),!0===o&&a.push("secure"),document.cookie=a.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Ee(e,t){return e&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)?function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const je=se.hasStandardBrowserEnv?function(){const e=/(msie|trident)/i.test(navigator.userAgent),t=document.createElement("a");let n;function r(n){let r=n;return e&&(t.setAttribute("href",r),r=t.href),t.setAttribute("href",r),{href:t.href,protocol:t.protocol?t.protocol.replace(/:$/,""):"",host:t.host,search:t.search?t.search.replace(/^\?/,""):"",hash:t.hash?t.hash.replace(/^#/,""):"",hostname:t.hostname,port:t.port,pathname:"/"===t.pathname.charAt(0)?t.pathname:"/"+t.pathname}}return n=r(window.location.href),function(e){const t=_.isString(e)?r(e):e;return t.protocol===n.protocol&&t.host===n.host}}():function(){return!0};function Ce(e,t){let n=0;const r=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let i,o=0,a=0;return t=void 0!==t?t:1e3,function(s){const l=Date.now(),c=r[a];i||(i=l),n[o]=s,r[o]=l;let u=a,p=0;for(;u!==o;)p+=n[u++],u%=e;if(o=(o+1)%e,o===a&&(a=(a+1)%e),l-i<t)return;const m=c&&l-c;return m?Math.round(1e3*p/m):void 0}}(50,250);return i=>{const o=i.loaded,a=i.lengthComputable?i.total:void 0,s=o-n,l=r(s);n=o;const c={loaded:o,total:a,progress:a?o/a:void 0,bytes:s,rate:l||void 0,estimated:l&&a&&o<=a?(a-o)/l:void 0,event:i};c[t?"download":"upload"]=!0,e(c)}}const Ae="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){let r=e.data;const i=be.from(e.headers).normalize();let o,a,{responseType:s,withXSRFToken:l}=e;function c(){e.cancelToken&&e.cancelToken.unsubscribe(o),e.signal&&e.signal.removeEventListener("abort",o)}if(_.isFormData(r))if(se.hasStandardBrowserEnv||se.hasStandardBrowserWebWorkerEnv)i.setContentType(!1);else if(!1!==(a=i.getContentType())){const[e,...t]=a?a.split(";").map((e=>e.trim())).filter(Boolean):[];i.setContentType([e||"multipart/form-data",...t].join("; "))}let u=new XMLHttpRequest;if(e.auth){const t=e.auth.username||"",n=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";i.set("Authorization","Basic "+btoa(t+":"+n))}const p=Ee(e.baseURL,e.url);function m(){if(!u)return;const r=be.from("getAllResponseHeaders"in u&&u.getAllResponseHeaders());!function(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new U("Request failed with status code "+n.status,[U.ERR_BAD_REQUEST,U.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}((function(e){t(e),c()}),(function(e){n(e),c()}),{data:s&&"text"!==s&&"json"!==s?u.response:u.responseText,status:u.status,statusText:u.statusText,headers:r,config:e,request:u}),u=null}if(u.open(e.method.toUpperCase(),Y(p,e.params,e.paramsSerializer),!0),u.timeout=e.timeout,"onloadend"in u?u.onloadend=m:u.onreadystatechange=function(){u&&4===u.readyState&&(0!==u.status||u.responseURL&&0===u.responseURL.indexOf("file:"))&&setTimeout(m)},u.onabort=function(){u&&(n(new U("Request aborted",U.ECONNABORTED,e,u)),u=null)},u.onerror=function(){n(new U("Network Error",U.ERR_NETWORK,e,u)),u=null},u.ontimeout=function(){let t=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded";const r=e.transitional||te;e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(new U(t,r.clarifyTimeoutError?U.ETIMEDOUT:U.ECONNABORTED,e,u)),u=null},se.hasStandardBrowserEnv&&(l&&_.isFunction(l)&&(l=l(e)),l||!1!==l&&je(p))){const t=e.xsrfHeaderName&&e.xsrfCookieName&&Se.read(e.xsrfCookieName);t&&i.set(e.xsrfHeaderName,t)}void 0===r&&i.setContentType(null),"setRequestHeader"in u&&_.forEach(i.toJSON(),(function(e,t){u.setRequestHeader(t,e)})),_.isUndefined(e.withCredentials)||(u.withCredentials=!!e.withCredentials),s&&"json"!==s&&(u.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&u.addEventListener("progress",Ce(e.onDownloadProgress,!0)),"function"==typeof e.onUploadProgress&&u.upload&&u.upload.addEventListener("progress",Ce(e.onUploadProgress)),(e.cancelToken||e.signal)&&(o=t=>{u&&(n(!t||t.type?new we(null,e,u):t),u.abort(),u=null)},e.cancelToken&&e.cancelToken.subscribe(o),e.signal&&(e.signal.aborted?o():e.signal.addEventListener("abort",o)));const f=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(p);f&&-1===se.protocols.indexOf(f)?n(new U("Unsupported protocol "+f+":",U.ERR_BAD_REQUEST,e)):u.send(r||null)}))},Le={http:null,xhr:Ae};_.forEach(Le,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}}));const xe=e=>`- ${e}`,Pe=e=>_.isFunction(e)||null===e||!1===e,Re=e=>{e=_.isArray(e)?e:[e];const{length:t}=e;let n,r;const i={};for(let o=0;o<t;o++){let t;if(n=e[o],r=n,!Pe(n)&&(r=Le[(t=String(n)).toLowerCase()],void 0===r))throw new U(`Unknown adapter '${t}'`);if(r)break;i[t||"#"+o]=r}if(!r){const e=Object.entries(i).map((([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build")));let n=t?e.length>1?"since :\n"+e.map(xe).join("\n"):" "+xe(e[0]):"as no adapter specified";throw new U("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function Me(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new we(null,e)}function Ne(e){return Me(e),e.headers=be.from(e.headers),e.data=he.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Re(e.adapter||ue.adapter)(e).then((function(t){return Me(e),t.data=he.call(e,e.transformResponse,t),t.headers=be.from(t.headers),t}),(function(t){return ve(t)||(Me(e),t&&t.response&&(t.response.data=he.call(e,e.transformResponse,t.response),t.response.headers=be.from(t.response.headers))),Promise.reject(t)}))}const Ze=e=>e instanceof be?e.toJSON():e;function Te(e,t){t=t||{};const n={};function r(e,t,n){return _.isPlainObject(e)&&_.isPlainObject(t)?_.merge.call({caseless:n},e,t):_.isPlainObject(t)?_.merge({},t):_.isArray(t)?t.slice():t}function i(e,t,n){return _.isUndefined(t)?_.isUndefined(e)?void 0:r(void 0,e,n):r(e,t,n)}function o(e,t){if(!_.isUndefined(t))return r(void 0,t)}function a(e,t){return _.isUndefined(t)?_.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function s(n,i,o){return o in t?r(n,i):o in e?r(void 0,n):void 0}const l={url:o,method:o,data:o,baseURL:a,transformRequest:a,transformResponse:a,paramsSerializer:a,timeout:a,timeoutMessage:a,withCredentials:a,withXSRFToken:a,adapter:a,responseType:a,xsrfCookieName:a,xsrfHeaderName:a,onUploadProgress:a,onDownloadProgress:a,decompress:a,maxContentLength:a,maxBodyLength:a,beforeRedirect:a,transport:a,httpAgent:a,httpsAgent:a,cancelToken:a,socketPath:a,responseEncoding:a,validateStatus:s,headers:(e,t)=>i(Ze(e),Ze(t),!0)};return _.forEach(Object.keys(Object.assign({},e,t)),(function(r){const o=l[r]||i,a=o(e[r],t[r],r);_.isUndefined(a)&&o!==s||(n[r]=a)})),n}const Ie={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{Ie[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const ke={};Ie.transitional=function(e,t,n){function r(e,t){return"[Axios v1.6.2] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,i,o)=>{if(!1===e)throw new U(r(i," has been removed"+(t?" in "+t:"")),U.ERR_DEPRECATED);return t&&!ke[i]&&(ke[i]=!0,console.warn(r(i," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,i,o)}};const De={assertOptions:function(e,t,n){if("object"!=typeof e)throw new U("options must be an object",U.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let i=r.length;for(;i-- >0;){const o=r[i],a=t[o];if(a){const t=e[o],n=void 0===t||a(t,o,e);if(!0!==n)throw new U("option "+o+" must be "+n,U.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new U("Unknown option "+o,U.ERR_BAD_OPTION)}},validators:Ie},_e=De.validators;class We{constructor(e){this.defaults=e,this.interceptors={request:new ee,response:new ee}}request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=Te(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:i}=t;void 0!==n&&De.assertOptions(n,{silentJSONParsing:_e.transitional(_e.boolean),forcedJSONParsing:_e.transitional(_e.boolean),clarifyTimeoutError:_e.transitional(_e.boolean)},!1),null!=r&&(_.isFunction(r)?t.paramsSerializer={serialize:r}:De.assertOptions(r,{encode:_e.function,serialize:_e.function},!0)),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=i&&_.merge(i.common,i[t.method]);i&&_.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete i[e]})),t.headers=be.concat(o,i);const a=[];let s=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(s=s&&e.synchronous,a.unshift(e.fulfilled,e.rejected))}));const l=[];let c;this.interceptors.response.forEach((function(e){l.push(e.fulfilled,e.rejected)}));let u,p=0;if(!s){const e=[Ne.bind(this),void 0];for(e.unshift.apply(e,a),e.push.apply(e,l),u=e.length,c=Promise.resolve(t);p<u;)c=c.then(e[p++],e[p++]);return c}u=a.length;let m=t;for(p=0;p<u;){const e=a[p++],t=a[p++];try{m=e(m)}catch(e){t.call(this,e);break}}try{c=Ne.call(this,m)}catch(e){return Promise.reject(e)}for(p=0,u=l.length;p<u;)c=c.then(l[p++],l[p++]);return c}getUri(e){return Y(Ee((e=Te(this.defaults,e)).baseURL,e.url),e.params,e.paramsSerializer)}}_.forEach(["delete","get","head","options"],(function(e){We.prototype[e]=function(t,n){return this.request(Te(n||{},{method:e,url:t,data:(n||{}).data}))}})),_.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,i){return this.request(Te(i||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}We.prototype[e]=t(),We.prototype[e+"Form"]=t(!0)}));const Fe=We;class ze{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,i){n.reason||(n.reason=new we(e,r,i),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}static source(){let e;return{token:new ze((function(t){e=t})),cancel:e}}}const Ue=ze,Be={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Be).forEach((([e,t])=>{Be[t]=e}));const He=Be,qe=function e(t){const n=new Fe(t),r=i(Fe.prototype.request,n);return _.extend(r,Fe.prototype,n,{allOwnKeys:!0}),_.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(Te(t,n))},r}(ue);qe.Axios=Fe,qe.CanceledError=we,qe.CancelToken=Ue,qe.isCancel=ve,qe.VERSION="1.6.2",qe.toFormData=Q,qe.AxiosError=U,qe.Cancel=qe.CanceledError,qe.all=function(e){return Promise.all(e)},qe.spread=function(e){return function(t){return e.apply(null,t)}},qe.isAxiosError=function(e){return _.isObject(e)&&!0===e.isAxiosError},qe.mergeConfig=Te,qe.AxiosHeaders=be,qe.formToJSON=e=>le(_.isHTMLForm(e)?new FormData(e):e),qe.getAdapter=Re,qe.HttpStatusCode=He,qe.default=qe;const Ve=qe;var Qe=n(3994),Ge="tde",$e=n(7294);function Je(){return Je=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Je.apply(this,arguments)}const Ke=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",Je({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[[Ge+"-wish-list-normal",{properties:{}}],[Ge+"-wish-list-loading",{properties:{}}],[Ge+"-wish-list-wishlisted",{properties:{}}],[Ge+"-wish-list-unauthinticate",{properties:{}}]]}))}));function Xe(e){return Xe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Xe(e)}function Ye(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function et(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ye(Object(n),!0).forEach((function(t){tt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ye(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function tt(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Xe(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Xe(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Xe(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var nt=[{key:"state",label:"Show State",setting:et(et({},Qe.i.elementSettings.SELECT),{},{options:[{value:"normal",title:"Normal"},{value:"loading",title:"Loading"},{value:"wishlisted",title:"Wish listed"},{value:"unauthinticate",title:"Unauthinticate"}]})}];function rt(e){return rt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},rt(e)}var it=function(e){var t=Qe.i.getCanvasElement(e).properties.settings;return(null==t?void 0:t.state)||"normal"};const ot={name:Ge+"-wish-list",type:"element",source:Ge,className:"",title:"Wish List",description:"Wish List",icon:"".concat(Qe.i.iconPrefix,"-wishlist-line"),hoverIcon:"".concat(Qe.i.iconPrefix,"-wishlist-fill"),category:"Tutor LMS",properties:{settings:{state:"normal"},attributes:(at={},st="data-element",lt=Ge+"-wish-list",(st=function(e){var t=function(e,t){if("object"!==rt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==rt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===rt(t)?t:String(t)}(st))in at?Object.defineProperty(at,st,{value:lt,enumerable:!0,configurable:!0,writable:!0}):at[st]=lt,at)},children:[],defaultStyle:"display:flex;align-items:center;justify-content:center;width:max-content;text-decoration:none;color:#757c8e;gap:8px;",constraints:{childrens:[{element:Ge+"-wish-list-wishlisted",condition:"ALLOW"},{element:Ge+"-wish-list-loading",condition:"ALLOW"},{element:Ge+"-wish-list-normal",condition:"ALLOW"},{element:Ge+"-wish-list-unauthinticate",condition:"ALLOW"}]},Component:Ke,controls:{margin:!0,padding:!1,height:!1,width:!0},settings:nt};var at,st,lt;function ct(e){return ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ct(e)}function ut(){return ut=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ut.apply(this,arguments)}var pt=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",ut({},Qe.i.getAllAttributes(o),{ref:t,className:r,"data-element_hide":"loading"!==it(o.parentId)}),i({template:[["svg",{properties:{svgOuterHtml:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024"><path d="M376.092 389.504c33.688-37.396 82.278-60.802 136.336-60.802 94.34 0 172.03 71.288 182.14 162.934l0.074 0.823-88.96-59.307c-3.327-2.247-7.427-3.587-11.84-3.587-11.784 0-21.336 9.552-21.336 21.336 0 7.37 3.737 13.868 9.42 17.701l0.076 0.048 138.453 92.331 12.245-15.275 81.451-101.845c2.702-3.537 4.329-8.02 4.329-12.883 0-11.782-9.551-21.333-21.333-21.333-6.533 0-12.379 2.936-16.293 7.561l-0.026 0.031-47.275 59.093c-17.815-107.412-110.029-188.307-221.138-188.307-70.033 0-132.56 32.139-173.636 82.474l-0.32 0.404c-2.516 3.191-4.035 7.269-4.035 11.702 0 6.711 3.482 12.609 8.738 15.986l0.075 0.045c8.32 5.547 19.2 4.096 26.581-2.304 0.94-0.821 1.792-1.685 2.574-2.61l0.029-0.035 3.669-4.181zM210.631 580.139c-3.176 3.703-5.11 8.552-5.11 13.853 0 11.782 9.551 21.333 21.333 21.333 6.949 0 13.123-3.323 17.018-8.466l0.039-0.053 47.36-59.136c17.815 107.412 110.029 188.307 221.138 188.307 70.033 0 132.56-32.139 173.636-82.474l0.32-0.404c2.507-3.187 4.021-7.259 4.021-11.684 0-6.723-3.494-12.63-8.766-16.004l-0.076-0.046c-3.336-2.149-7.41-3.426-11.783-3.426-5.709 0-10.909 2.177-14.815 5.746l0.017-0.015c-0.941 0.82-1.792 1.684-2.574 2.61l-0.029 0.035c-33.832 39.851-83.967 64.972-139.969 64.972-94.321 0-171.998-71.259-182.133-162.878l-0.074-0.824 88.917 59.264c3.207 2.031 7.112 3.237 11.298 3.237 11.782 0 21.333-9.551 21.333-21.333 0-7.156-3.523-13.489-8.93-17.359l-0.064-0.044-138.453-92.331-12.203 15.275-81.493 101.845z"></path></svg>'},style:"height:24px;width:24px;min-width:auto;min-height:auto;"}],["text",{properties:{contents:["Loading..."]}}]]}))}));const mt={name:Ge+"-wish-list-loading",type:"element",source:Ge,className:"",title:"Loading State",description:"Wish List",visibility:!1,properties:{tag:"div",attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==ct(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==ct(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===ct(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","loading")},children:[],defaultStyle:"display:flex;align-items:center;justify-content:center;width:max-content;text-decoration:none;color:#757c8e;gap:8px;",constraints:{parents:[{element:Ge+"-wish-list",condition:"ALLOW"}]},Component:pt,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function ft(e){return ft="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ft(e)}function dt(){return dt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},dt.apply(this,arguments)}var gt=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",dt({},Qe.i.getAllAttributes(o),{ref:t,className:r,"data-element_hide":"normal"!==it(o.parentId)}),i({template:[["svg",{properties:{svgOuterHtml:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>\n                    <path d='M17 3H7c-1.1 0-1.99.9-1.99 2L5 21l7-3 7 3V5c0-1.1-.9-2-2-2zm0 15-5-2.18L7 18V5h10v13z'></path>\n                </svg>"},style:"height:16px;width:16px;min-width:auto;min-height:auto;"}],["text",{properties:{contents:["Wishlist"]}}]]}))}));const yt={name:Ge+"-wish-list-normal",type:"element",source:Ge,className:"",title:"Noraml State",description:"Wish List",visibility:!1,properties:{tag:"div",attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==ft(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==ft(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===ft(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","normal")},children:[],defaultStyle:"display:flex;align-items:center;justify-content:center;width:max-content;text-decoration:none;color:#757c8e;gap:8px;",constraints:{parents:[{element:Ge+"-wish-list",condition:"ALLOW"}]},Component:gt,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function bt(e){return bt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},bt(e)}function ht(){return ht=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ht.apply(this,arguments)}var vt=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",ht({},Qe.i.getAllAttributes(o),{ref:t,className:r,"data-element_hide":"wishlisted"!==it(o.parentId)}),i({template:[["svg",{properties:{svgOuterHtml:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>\n                    <path d='M17 3H7c-1.1 0-1.99.9-1.99 2L5 21l7-3 7 3V5c0-1.1-.9-2-2-2zm0 15-5-2.18L7 18V5h10v13z'></path>\n                </svg>"},style:"height:16px;width:16px;min-width:auto;min-height:auto;"}],["text",{properties:{contents:["Wishlisted"]}}]]}))}));const Ot={name:Ge+"-wish-list-wishlisted",type:"element",source:Ge,className:"",title:"Wishlisted State",description:"Wish List",visibility:!1,properties:{tag:"div",attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==bt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==bt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===bt(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","wishlisted")},children:[],defaultStyle:"display:flex;align-items:center;justify-content:center;width:max-content;text-decoration:none;color:#757c8e;gap:8px;",constraints:{parents:[{element:Ge+"-wish-list",condition:"ALLOW"}]},Component:vt,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function wt(e){return wt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},wt(e)}function St(){return St=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},St.apply(this,arguments)}var Et=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("a",St({},Qe.i.getAllAttributes(o),{ref:t,className:r,"data-element_hide":"unauthinticate"!==it(o.parentId)}),i({template:[["svg",{properties:{svgOuterHtml:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'>\n                    <path d='M17 3H7c-1.1 0-1.99.9-1.99 2L5 21l7-3 7 3V5c0-1.1-.9-2-2-2zm0 15-5-2.18L7 18V5h10v13z'></path>\n                </svg>"},style:"height:16px;width:16px;min-width:auto;min-height:auto;"}],["text",{properties:{contents:["Wishlist"]}}]]}))}));const jt={name:Ge+"-wish-list-unauthinticate",type:"element",source:Ge,className:"",title:"Unauthinticate State",description:"Wish List",visibility:!1,properties:{tag:"a",attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==wt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==wt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===wt(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","unauthinticate")},children:[],defaultStyle:"display:flex;align-items:center;justify-content:center;width:max-content;text-decoration:none;color:#757c8e;gap:8px;",constraints:{parents:[{element:Ge+"-wish-list",condition:"ALLOW"}]},Component:Et,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function Ct(e){return Ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ct(e)}function At(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Lt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?At(Object(n),!0).forEach((function(t){xt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):At(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function xt(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Ct(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Ct(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Ct(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Pt=[{key:"state",label:"Show State",setting:Lt(Lt({},Qe.i.elementSettings.SELECT),{},{options:[{value:"no-topics",title:"No Topics"},{value:"has-topics",title:"Has Topics"}]})}];function Rt(){return Rt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Rt.apply(this,arguments)}var Mt=function(e){var t=Qe.i.getCanvasElement(e).properties.settings;return(null==t?void 0:t.state)||"no-topics"},Nt=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",Rt({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[[Ge+"-no-topics",{properties:{}}],[Ge+"-has-topics",{properties:{}}]]}))}));const Zt={name:Ge+"-topics",type:"element",source:Ge,className:"",title:"Topics",description:"Topics",icon:"".concat(Qe.i.iconPrefix,"-lessons-line"),hoverIcon:"".concat(Qe.i.iconPrefix,"-lessons-fill"),category:"Tutor LMS",properties:{tag:"div",settings:{state:"no-topics"}},children:[],defaultStyle:"margin: 0 auto;",constraints:{childrens:[{element:Ge+"-has-topics",condition:"ALLOW"},{element:Ge+"-no-topics",condition:"ALLOW"}]},Component:Nt,controls:{margin:!0,padding:!1,height:!1,width:!0},settings:Pt};function Tt(e){return Tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Tt(e)}function It(){return It=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},It.apply(this,arguments)}var kt=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=(e.properties.contents,Qe.i.getCanvasElement(n));return $e.createElement("div",It({},Qe.i.getAllAttributes(o),{ref:t,className:r,"data-element_hide":"has-topics"!==Mt(o.parentId)}),i({template:[["collection",{properties:{dynamicContent:{collectionType:"posts",type:"topics",items:10,pageNo:1,inherit:!0,pagination:!1,filters:[],sorting:{type:"heading",value:"A-Z"}}},title:"Course Topics"},[["collection-wrapper",{properties:{componentType:"collection"}},[["collection-item",{properties:{}},[["div",{properties:{}},[["heading",{properties:{dynamicContent:{type:"post",value:"post_title"}}}],[Ge+"-lessons",{properties:{}}]]]]]]]]]]}))}));const Dt={name:Ge+"-has-topics",type:"element",source:Ge,className:"",title:"Has Topics",description:"Has Topics",visibility:!1,properties:{tag:"div",attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Tt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Tt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Tt(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","has-topics")},children:[],constraints:{parents:[{element:Ge+"-topics",condition:"ALLOW"}],childrens:[{element:"*",condition:"ALLOW"}]},Component:kt,controls:{margin:!0,padding:!1},manualDelete:!1};function _t(e){return _t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_t(e)}function Wt(){return Wt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Wt.apply(this,arguments)}var Ft=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=e.properties.contents,a=Qe.i.getCanvasElement(n);return $e.createElement("div",Wt({},Qe.i.getAllAttributes(a),{ref:t,className:r,"data-element_hide":"no-topics"!==Mt(a.parentId)}),i({template:[["svg",{style:"height: auto;",properties:{svgOuterHtml:'<svg viewBox="0 0 824 242" fill="none" style="height: auto;" xmlns="http://www.w3.org/2000/svg">\n            <path d="M0 241.266s369.993-203.2255 824-.957l-824 .957Z" fill="url(#a)"/>\n            <path d="M281.32 167.026v20.072" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>\n            <path d="M291.09 156.888c-6.818-23.128-9.617-25.522-9.617-25.522s-2.137 2.547-9.567 25.522c-6.513 20.021 4.987 20.123 8.091 19.817-.051 0 17.708 2.649 11.093-19.817Z" fill="#E5E7EA"/>\n            <path d="M502.926 152.1v20.785" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>\n            <path d="M513.002 141.605c-7.074-23.943-9.974-26.388-9.974-26.388s-2.188 2.649-9.923 26.388c-6.717 20.734 5.191 20.836 8.346 20.53.05 0 18.42 2.7 11.551-20.53Z" fill="#E5E7EA"/>\n            <path d="M534.322 167.077v15.793" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>\n            <path d="M542.006 159.13c-5.394-18.187-7.531-20.072-7.531-20.072s-1.679 1.987-7.531 20.072c-5.089 15.69 3.918 15.792 6.361 15.588-.051 0 13.891 2.038 8.701-15.588Z" fill="#E5E7EA"/>\n            <path d="m349.761 45.1695 100.397-8.9151s24.272 1.1717 26.868 31.0754c2.595 29.9038 5.037 47.3262 5.037 47.3262l-92.153 11.106-5.954-50.3322c-.763-6.5208-3.256-12.6849-7.327-17.8302-4.987-6.266-13.281-12.7358-26.868-12.4301Z" fill="#E3E5EA"/>\n            <path d="m392.963 125.202-61.928 7.03-6.259-52.2679c-2.086-17.4226 10.534-33.1133 27.987-34.8453 16.894-1.6302 32.007 10.5453 33.992 27.4585l6.208 52.6247ZM404.616 123.469v18.391h16.385l-.204-19.868-16.181 1.477Z" fill="#C1C3CA"/>\n            <path d="m330.374 132.792-7.887 26.185c-1.221 3.617-1.73 7.438-1.476 11.258.509 7.183 3.918 16.251 18.064 15.895 8.753-.255 16.182-3.515 22.085-7.438 8.243-5.502 14.604-13.347 18.573-22.415l13.739-30.464-63.098 6.979Z" fill="#D0D2D6"/>\n            <path d="M422.324 140.994h-18.777v43.149h18.777v-43.149Z" fill="#CDCFD4"/>\n            <path d="m381.768 60.045-54.447 39.0227M382.989 60.8607l-51.75 70.6073M360.396 128.666l22.441-65.2582M350.27 82.7662s11.042-3.0058 10.991 6.2659c0 0 10.635-2.3942 10.025 8.253M335.615 93.5153s13.994-3.0056 14.248 11.6657c0 0 11.195-4.126 15.52 7.744" stroke="#CDCFD4" stroke-width="3" stroke-miterlimit="10"/>\n            <path fill-rule="evenodd" clip-rule="evenodd" d="m273.789 22.3978-.814-.051v1.3246l.814.0509v-1.3245ZM273.942 20.0038l-.814-.0509-.102 1.3244.814.051.102-1.3245ZM274.044 17.6093l-.814-.0508-.051 1.3244.814.051.051-1.3246ZM273.84 29.5302l-.814.0509c.051.4584.051.917.102 1.3245l.814-.051c-.102-.4075-.102-.8659-.102-1.3244ZM273.738 27.1358h-.814c0 .4584 0 .917.051 1.3245h.814c0-.4075-.051-.8661-.051-1.3245ZM273.738 24.7925h-.814v1.3245h.814v-1.3245ZM274.705 36.611l-.763.2037.305 1.3246.763-.2037-.305-1.3246ZM274.298 34.2678l-.814.102c.051.4585.153.9169.203 1.3244l.815-.1528c-.102-.3566-.153-.8151-.204-1.2736ZM273.993 31.9246l-.814.1019c.051.4585.101.917.152 1.3245l.815-.1019c-.051-.4585-.102-.866-.153-1.3245ZM276.995 43.3356l-.712.3566c.203.4076.407.8152.61 1.2227l.713-.4075c-.255-.3566-.407-.7642-.611-1.1718ZM276.079 41.1959l-.763.2547.509 1.2736.712-.3057-.458-1.2226ZM275.316 38.9547l-.763.2547c.102.4585.254.8661.407 1.2736l.763-.2547c-.153-.4585-.305-.8661-.407-1.2736ZM280.863 49.3468l-.662.5095.916 1.0188.611-.5604-.865-.9679ZM279.387 47.4623l-.662.4585c.255.3566.56.7132.814 1.0698l.662-.5094c-.255-.3057-.56-.6623-.814-1.0189ZM278.115 45.4754l-.713.4075c.204.4076.458.7642.713 1.1718l.661-.4585c-.203-.3566-.458-.7642-.661-1.1208ZM286.002 54.2377l-.509.6622c.356.3056.763.5094 1.12.7641l.458-.6621c-.357-.2548-.713-.5095-1.069-.7642ZM284.17 52.7094l-.56.6113c.357.3057.662.6113 1.018.866l.509-.6113c-.305-.2547-.662-.5603-.967-.866ZM282.44 51.0791l-.611.5604c.305.3057.611.6622.967.9679l.56-.6113c-.306-.2547-.611-.6113-.916-.917ZM292.159 57.7525l-.305.7641c.407.1529.814.3566 1.272.5095l.305-.7642c-.458-.1528-.865-.3056-1.272-.5094ZM290.022 56.7336l-.407.7642 1.221.6113.356-.7642-1.17-.6113ZM287.986 55.562l-.458.7133 1.171.6622.407-.6622-1.12-.7133ZM298.978 59.7395l-.102.8151c.458.051.916.1529 1.374.1529l.051-.8152c-.458-.0509-.916-.1019-1.323-.1528ZM296.637 59.2808l-.203.8152 1.373.2547.153-.8152-1.323-.2547ZM294.398 58.6186l-.254.7642c.407.1528.865.2547 1.272.4075l.203-.7641c-.407-.1528-.814-.2547-1.221-.4076ZM306 59.5355l.153.8152c.458-.1019.916-.2038 1.323-.3057l-.204-.7642c-.407.051-.814.1529-1.272.2547ZM303.659 59.8923l.051.8152c.458-.051.916-.1019 1.374-.1529l-.102-.8151c-.407.0509-.865.1019-1.323.1528ZM301.318 59.9435v.815c.458 0 .916.0509 1.374 0v-.815c-.458.0509-.916 0-1.374 0ZM312.36 56.6318l.509.6113c.356-.3057.662-.6622 1.018-.9679l-.611-.5604c-.305.3056-.61.6113-.916.917ZM310.427 57.9565l.407.7133c.407-.2548.763-.5096 1.17-.7133l-.458-.6622c-.407.2038-.763.4584-1.119.6622ZM308.29 58.9246l.254.7641c.407-.1528.865-.3566 1.272-.5094l-.356-.7642c-.356.1529-.763.3566-1.17.5095ZM315.566 50.6717l.814.0508c.051-.4585 0-.9679-.051-1.4264l-.814.102c.102.4075.102.866.051 1.2736ZM315.058 52.9131l.763.3057c.203-.4585.305-.9171.407-1.3755l-.814-.1528c-.051.4076-.204.8151-.356 1.2226ZM313.938 54.8997l.662.4586c.254-.4075.508-.7642.763-1.1717l-.713-.4076c-.254.4076-.458.7641-.712 1.1207ZM311.953 45.6282l.204-.815c-.458-.1019-.916-.2038-1.425-.2548l-.051.8151c.458.1018.865.1528 1.272.2547ZM313.989 46.6468l.508-.6113c-.407-.3056-.814-.5604-1.272-.7641l-.356.7641c.407.1528.763.3566 1.12.6113ZM315.312 48.4301l.763-.3056c-.153-.4585-.407-.917-.712-1.2736l-.611.5095c.203.3056.407.6622.56 1.0697ZM305.287 46.8508l-.407-.7133c-.203.1019-.407.2547-.559.4076-.204.1528-.408.2547-.56.4075l.509.6623c.152-.1528.356-.2547.508-.4075.153-.1529.306-.2547.509-.3566ZM307.425 45.8321l-.255-.7642c-.254.051-.458.1528-.661.2547-.204.1019-.407.2037-.662.3056l.356.7133c.204-.1019.407-.2038.611-.2547.204-.1019.356-.2038.611-.2547ZM309.664 45.4244l-.051-.8152c-.255 0-.458.051-.713.051-.254 0-.458.0509-.712.1019l.153.8151c.203-.0509.407-.102.61-.102.204 0 .509-.0508.713-.0508ZM300.758 52.047l-.763-.3057c-.203.4076-.356.866-.509 1.2736l.764.2547c.203-.4075.356-.815.508-1.2226ZM301.929 50.0094l-.662-.4586c-.152.2038-.254.4076-.407.5605-.102.2037-.254.4075-.356.6113l.712.4075c.102-.2038.204-.3566.357-.5604.101-.2038.203-.3565.356-.5603ZM303.456 48.2771l-.56-.6113c-.153.1529-.356.3057-.509.5095-.152.1528-.305.3566-.458.5094l.611.5094c.152-.1528.254-.3565.407-.4584.203-.1528.356-.3057.509-.4586Z" fill="#C1C3CA"/>\n            <path fill-rule="evenodd" clip-rule="evenodd" d="m299.588 58.9753-.814.051c0 .4585.051.917.102 1.3755l.814-.1019c-.051-.4585-.102-.917-.102-1.3246ZM299.639 56.5808l-.814-.1019c-.051.4585-.051.917-.102 1.3754h.814c.051-.4075.051-.8151.102-1.2735ZM299.995 54.2887l-.763-.2037c-.102.4585-.204.9169-.254 1.3754l.814.1019c.051-.4075.101-.866.203-1.2736ZM300.962 65.9034l-.763.2547c.152.4076.305.8661.458 1.2736l.763-.3057c-.153-.4075-.305-.815-.458-1.2226ZM300.25 63.611l-.764.2037c.102.4585.255.8661.357 1.3246l.763-.2548c-.102-.4075-.255-.815-.356-1.2735ZM299.792 61.3185l-.814.1019c.05.4585.152.917.254 1.3246l.814-.1529c-.102-.4075-.203-.8151-.254-1.2736ZM304.168 72.2206l-.662.4586.764 1.1207.661-.5094-.763-1.0699ZM302.947 70.1829l-.713.4076.662 1.1717.712-.4076-.661-1.1717ZM301.878 68.0944l-.763.3566c.203.4075.356.815.559 1.2226l.713-.3566-.509-1.2226ZM308.849 77.5696l-.559.6115 1.017.9168.509-.6113-.967-.917ZM307.17 75.9394l-.61.5604.916.9679.61-.6113-.916-.917ZM305.593 74.1564l-.611.5094.865 1.0189.611-.5095-.865-1.0188ZM314.599 81.7471l-.407.7132 1.171.6623.407-.7133-1.171-.6622ZM312.564 80.5243l-.458.6623 1.17.7641.407-.7131-1.119-.7133ZM310.681 79.1487l-.509.6113 1.069.8151.458-.6622-1.018-.7642ZM321.113 84.4978l-.204.8151 1.323.3566.153-.8152-1.272-.3565ZM318.874 83.7849l-.254.7641 1.272.4584.254-.8151-1.272-.4074ZM316.686 82.868l-.306.7131 1.222.5605.305-.7642-1.221-.5094ZM281.371 8.49081s-.712 4.17739 3.257 4.83959c0 0 2.392-1.732 1.272-4.07544-1.068-2.29245-4.529-.76415-4.529-.76415Z" fill="#C1C3CA"/>\n            <path fill-rule="evenodd" clip-rule="evenodd" d="M280.812 8.79593s-.407 4.33027 3.409 4.94157c0 0-1.628 1.834-3.155.7642-1.425-.9679-2.442-2.8019-2.086-4.4831.101-.40751.305-.8151.661-1.06982.306-.10189.713-.25474 1.171-.15285Z" fill="#C1C3CA"/>\n            <path fill-rule="evenodd" clip-rule="evenodd" d="M278.115 10.3243c0-.1018-1.985 2.1397-1.527 3.7699 0 0-1.17.6113-1.119 1.3245 0 0 .865-.866 1.628-.4585 0 0 1.527 1.7321 3.918.4585.051-.051-2.697-1.5793-2.9-5.0944ZM281.117 7.77717c-1.221-.96792-2.137-2.2924-2.697-3.76976-.153-.35661-.254-.71318-.254-1.12073-.051-.61132.152-1.22264.509-1.78302.254-.407546.508-.764224.966-.967998.865-.407547 1.985.15283 2.443 1.018868.458.86604.407 1.88495.254 2.85288-.152 1.12075-.458 2.2925-.865 3.36231-.102.20378-.203.40745-.356.40745-.153.05094-.305-.10184-.458-.20372-1.425-1.47736-3.358-2.29241-5.241-3.00561-.407-.15283-.865-.30576-1.272-.35671-.509 0-.967.15293-1.425.35671-.611.30566-1.17.76405-1.425 1.42631-.254.66226-.102 1.42647.407 1.83401.306.25472.713.30569 1.12.40757 2.697.4585 5.444.35663 8.091-.35657" fill="#C1C3CA"/>\n            <path d="m392.352 124.998-61.266 6.979-6.208-51.7582c-2.086-17.2189 10.432-32.8076 27.682-34.4378 16.741-1.6302 31.701 10.4434 33.635 27.1529l6.157 52.0641Z" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>\n            <path d="M457.995 24.5374 474.329 36.56c.661.4584.458 1.5283-.356 1.7321l-6.565 1.5283.56 6.8774c.051.866-.916 1.3754-1.577.815l-16.487-14.4169 8.091-8.5585Z" fill="#3E64DE"/>\n            <path d="M443.492 60.0443c1.996 0 3.613-1.6422 3.613-3.6679 0-2.0258-1.617-3.6679-3.613-3.6679-1.995 0-3.613 1.6421-3.613 3.6679 0 2.0257 1.618 3.6679 3.613 3.6679Z" fill="#949BA9"/>\n            <path d="M444.815 54.5424c.611-3.0566 1.222-6.1132 1.832-9.1698.407-2.1397.865-4.2793 1.272-6.4698.204-.917.713-2.2925.611-3.2095.051.6623-.814 1.019.102.2039.509-.4585 1.017-1.0189 1.475-1.5283 1.527-1.5283 3.054-3.1076 4.529-4.6359 2.188-2.2415 4.427-4.534 6.615-6.7755 1.629-1.6302-.916-4.1773-2.493-2.4962-2.341 2.3943-4.631 4.7377-6.971 7.1321-1.476 1.5283-2.952 3.0565-4.478 4.5339-.713.7132-1.73 1.4774-2.188 2.4453-.407.917-.458 2.1396-.662 3.0566-.407 2.0887-.814 4.2282-1.272 6.3169-.661 3.2095-1.272 6.4698-1.934 9.6793-.305 2.1905 3.155 3.1585 3.562.917Z" fill="#949BA9"/>\n            <path d="M558.035 22.0415c2.952 0 2.952-4.5849 0-4.5849-2.951 0-2.951 4.5849 0 4.5849Z" fill="url(#b)"/>\n            <defs>\n            <linearGradient id="a" x1="406.358" y1="69.2193" x2="408.386" y2="355.63" gradientUnits="userSpaceOnUse">\n                <stop offset=".1653" stop-color="#E3E5EA"/>\n                <stop offset=".3741" stop-color="#F4F5F7" stop-opacity=".6199"/>\n                <stop offset=".5962" stop-color="#F6F7F8" stop-opacity="0"/>\n            </linearGradient>\n            <linearGradient id="b" x1="558.006" y1="22.0271" x2="558.065" y2="17.4425" gradientUnits="userSpaceOnUse">\n                <stop offset=".1653" stop-color="#E3E5EA"/>\n                <stop offset=".2826" stop-color="#EDEFF1" stop-opacity=".8497"/>\n                <stop offset=".4662" stop-color="#F4F5F7" stop-opacity=".6143"/>\n                <stop offset=".9455" stop-color="#F6F7F8" stop-opacity="0"/>\n            </linearGradient>\n            </defs>\n        </svg>'}}],["text",{properties:{contents:o},style:"color: #41454F;"}]]}))}));const zt={name:Ge+"-no-topics",type:"element",source:Ge,className:"",title:"No Topics",description:"No Topics",visibility:!1,properties:{tag:"div",contents:["No Data Available in this Section"],attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==_t(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==_t(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===_t(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","no-topics")},children:[],defaultStyle:"padding: 32px; text-align: center;",constraints:{parents:[{element:Ge+"-topics",condition:"ALLOW"}],childrens:[{element:"*",condition:"ALLOW"}]},Component:Ft,controls:{margin:!0,padding:!1},manualDelete:!1};var Ut=[Ge+"-lesson-video",{properties:{componentType:"collection"},style:"display: flex;"}],Bt=[Ge+"-lesson-text",{properties:{componentType:"collection"},style:"display: flex;"}],Ht=[Ge+"-quiz",{properties:{componentType:"collection"},style:"display: flex;"}],qt=[Ge+"-assignment",{properties:{componentType:"collection"},style:"display: flex;"}],Vt=[Ge+"-googlemeet",{properties:{componentType:"collection"},style:"display: flex;"}],Qt=[Ge+"-zoom",{properties:{componentType:"collection"},style:"display: flex;"}];function Gt(){return Gt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Gt.apply(this,arguments)}const $t=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",Gt({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[Ut,Bt,Ht,qt,Vt,Qt]}))}));function Jt(e){return Jt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Jt(e)}function Kt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Xt(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Jt(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Jt(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Jt(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Yt=Qe.i.elementSettings,en=Object.freeze([{key:"locked",label:"Show locked",setting:function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Kt(Object(n),!0).forEach((function(t){Xt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Kt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},Yt.CHECKBOX)}]);const tn={name:Ge+"-lessons",type:"element",source:Ge,className:"",title:"Lessons",description:"Lessons loop",icon:"".concat(Qe.i.iconPrefix,"-lessons-line"),hoverIcon:"".concat(Qe.i.iconPrefix,"-lessons-fill"),visibility:!1,category:"Tutor LMS",properties:{settings:{locked:!1}},children:[],defaultStyle:"",constraints:{childrens:[{element:Ge+"-assignment",condition:"ALLOW"},{element:Ge+"-googlemeet",condition:"ALLOW"},{element:Ge+"-lesson-text",condition:"ALLOW"},{element:Ge+"-lesson-video",condition:"ALLOW"},{element:Ge+"-quiz",condition:"ALLOW"},{element:Ge+"-zoom",condition:"ALLOW"}]},Component:$t,controls:{margin:!0,padding:!1,height:!1,width:!0},settings:en};function nn(){return nn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},nn.apply(this,arguments)}var rn=Object.freeze([[Ge+"-lesson-locked-icon",{}],[Ge+"-lesson-unlocked-icon",{}]]),on=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";"body"!==e;){var t=Qe.i.getCanvasElement(e);if(t.name===Ge+"-lessons")return e;e=t.parentId}return""},an=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("span",nn({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:rn}))}));const sn={name:Ge+"-lesson-access",visibility:!1,type:"element",source:Ge,className:"",title:"Lesson Access",description:"Lesson Access",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{tag:"span"},children:[],defaultStyle:"",constraints:{ancestors:[{element:Ge+"-assignment",condition:"REQUIRED"},{element:Ge+"-googlemeet",condition:"REQUIRED"},{element:Ge+"-quiz",condition:"REQUIRED"},{element:Ge+"-zoom",condition:"REQUIRED"},{element:Ge+"-lesson-text",condition:"REQUIRED"},{element:Ge+"-lesson-video",condition:"REQUIRED"}]},Component:an,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function ln(e){return ln="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ln(e)}function cn(){return cn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},cn.apply(this,arguments)}var un=Object.freeze([["svg",{style:"width:40px;height:40px;",properties:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==ln(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==ln(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===ln(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:"svg",enumerable:!0,configurable:!0,writable:!0}):e[t]="svg",e}({tag:"span",svgOuterHtml:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M12 4c1.648 0 3 1.352 3 3v3H9V7c0-1.648 1.352-3 3-3zm5 6V7c0-2.752-2.248-5-5-5S7 4.248 7 7v3H6a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8a2 2 0 0 0-2-2h-1zM6 12h12v8H6v-8z' ></path></svg>"},"tag")}]]),pn=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n),a=on(n),s=Qe.i.getCanvasElement(a);return $e.createElement("span",cn({},Qe.i.getAllAttributes(o),{ref:t,className:r,"data-element_hide":!Boolean(s.properties.settings.locked)}),i({template:un}))}));const mn={name:Ge+"-lesson-locked-icon",visibility:!1,type:"element",source:Ge,className:"",title:"Lesson locked icon",description:"Lesson locked icon",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{tag:"span"},children:[],defaultStyle:"",constraints:{ancestors:[{element:Ge+"-lesson-access",condition:"REQUIRED"}]},Component:pn,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function fn(e){return fn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},fn(e)}function dn(){return dn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},dn.apply(this,arguments)}var gn=Object.freeze([["svg",{style:"width:40px;height:40px;",properties:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==fn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==fn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===fn(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:"svg",enumerable:!0,configurable:!0,writable:!0}):e[t]="svg",e}({tag:"span",svgOuterHtml:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0z' ></path><path d='M21.894 11.553C19.736 7.236 15.904 5 12 5c-3.903 0-7.736 2.236-9.894 6.553a1 1 0 0 0 0 .894C4.264 16.764 8.096 19 12 19c3.903 0 7.736-2.236 9.894-6.553a1 1 0 0 0 0-.894zM12 17c-2.969 0-6.002-1.62-7.87-5C5.998 8.62 9.03 7 12 7c2.969 0 6.002 1.62 7.87 5-1.868 3.38-4.901 5-7.87 5z' ></path></svg>"},"tag")}]]),yn=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n),a=on(n),s=Qe.i.getCanvasElement(a);return $e.createElement("span",dn({},Qe.i.getAllAttributes(o),{ref:t,className:r,"data-element_hide":Boolean(s.properties.settings.locked)}),i({template:gn}))}));const bn={name:Ge+"-lesson-unlocked-icon",visibility:!1,type:"element",source:Ge,className:"",title:"Lesson unlocked icon",description:"Lesson unlocked icon",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{tag:"span"},children:[],defaultStyle:"",constraints:{ancestors:[{element:Ge+"-lesson-access",condition:"REQUIRED"}]},Component:yn,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function hn(e){return hn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},hn(e)}function vn(){return vn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},vn.apply(this,arguments)}function On(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function wn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?On(Object(n),!0).forEach((function(t){Sn(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):On(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Sn(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==hn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==hn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===hn(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var En=Qe.i.elementSettings,jn=Object.freeze([{key:"meeting_status",label:"Meeting status",setting:wn(wn({},En.SELECT),{},{options:[{value:"upcoming",title:"Upcoming"},{value:"live",title:"Live"},{value:"expired",title:"Expired"}]})}]),Cn=Object.freeze([[Ge+"-meeting-live",{}],[Ge+"-meeting-expired",{}]]),An=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return Qe.i.getCanvasElement(e).properties.settings.meeting_status||""},Ln=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("span",vn({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:Cn}))}));const xn={name:Ge+"-meeting-status",visibility:!1,type:"element",source:Ge,className:"",title:"Meeting status",description:"Meeting status",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{tag:"span",settings:{meeting_status:"live"}},defaultStyle:"",constraints:{childrens:[{element:Ge+"-meeting-live",condition:"ALLOW"},{element:Ge+"-meeting-expired",condition:"ALLOW"}],ancestors:[{element:Ge+"-zoom",condition:"REQUIRED"}]},Component:Ln,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1,settings:jn};function Pn(){return Pn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Pn.apply(this,arguments)}var Rn=Object.freeze([["paragraph",{properties:{contents:["Live"]}}]]),Mn=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("span",Pn({},Qe.i.getAllAttributes(o),{ref:t,className:r,"data-element_hide":"live"!==An(o.parentId)}),i({template:Rn}))}));const Nn={name:Ge+"-meeting-live",visibility:!1,type:"element",source:Ge,className:"",title:"Meeting live",description:"Meeting live",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{tag:"span"},children:[],defaultStyle:"",constraints:{parents:[{element:Ge+"-meeting-status",condition:"ALLOW"}]},Component:Mn,controls:{margin:!0,padding:!1,height:!1,width:!0}};function Zn(){return Zn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Zn.apply(this,arguments)}var Tn=Object.freeze([["paragraph",{properties:{contents:["Expired"]}}]]),In=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("span",Zn({},Qe.i.getAllAttributes(o),{ref:t,className:r,"data-element_hide":"expired"!==An(o.parentId)}),i({template:Tn}))}));const kn={name:Ge+"-meeting-expired",visibility:!1,type:"element",source:Ge,className:"",title:"Meeting expired",description:"Meeting expired",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{tag:"span"},children:[],defaultStyle:"",constraints:{parents:[{element:Ge+"-meeting-status",condition:"ALLOW"}]},Component:In,controls:{margin:!0,padding:!1,height:!1,width:!0}};function Dn(){return Dn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Dn.apply(this,arguments)}var _n=Object.freeze([[Ge+"-assignment-icon",{properties:{}}],["paragraph",{properties:{componentType:"collection",dynamicContent:{type:"post",value:"post_title"},contents:["Assignment Title"]},title:"Assignment Title"}],[Ge+"-lesson-access",{properties:{}}]]),Wn=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("a",Dn({href:"#"},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:_n}))}));const Fn={name:Ge+"-assignment",visibility:!1,type:"element",source:Ge,className:"",title:"Assignment",description:"Assignment",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{tag:"a"},children:[],defaultStyle:"",constraints:{childrens:[{element:"*",condition:"ALLOW"}],parents:[{element:Ge+"-lessons",condition:"ALLOW"}]},Component:Wn,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function zn(){return zn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},zn.apply(this,arguments)}var Un=Object.freeze([[Ge+"-googlemeet-icon",{properties:{}}],["paragraph",{properties:{componentType:"collection",dynamicContent:{type:"post",value:"post_title"},contents:["Google Meet Lesson Title"]},title:"Google Meet Title"}],[Ge+"-lesson-access",{properties:{}}]]),Bn=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("a",zn({href:"#"},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:Un}))}));const Hn={name:Ge+"-googlemeet",visibility:!1,type:"element",source:Ge,className:"",title:"Google Meet",description:"Google Meet",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{tag:"a"},children:[],defaultStyle:"",constraints:{childrens:[{element:"*",condition:"ALLOW"}],parents:[{element:Ge+"-lessons",condition:"ALLOW"}]},Component:Bn,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function qn(e){return qn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},qn(e)}function Vn(){return Vn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Vn.apply(this,arguments)}function Qn(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==qn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==qn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===qn(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Gn=Object.freeze([["svg",Qn(Qn({style:"width:40px;height:40px;",properties:{iconClass:"".concat(iconPrefix,"-phone-vr-default"),tag:"span"}},"properties",{svgOuterHtml:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M4 4a2 2 0 0 1 2-2h8a1 1 0 0 1 .707.293l5 5A1 1 0 0 1 20 8v12a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4zm13.586 4L14 4.414V8h3.586zM12 4H6v16h12V10h-5a1 1 0 0 1-1-1V4zm-4 9a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2H9a1 1 0 0 1-1-1zm0 4a1 1 0 0 1 1-1h6a1 1 0 1 1 0 2H9a1 1 0 0 1-1-1z' ></path></svg>",tag:"svg"}),"title","Lesson Icon")],["paragraph",{properties:{componentType:"collection",dynamicContent:{type:"post",value:"post_title"},contents:["Text Lesson Title"]},title:"Lesson Title"}],[Ge+"-lesson-access",{properties:{}}]]),$n=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("a",Vn({href:"#"},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:Gn}))}));const Jn={name:Ge+"-lesson-text",visibility:!1,type:"element",source:Ge,className:"",title:"Text Lesson",description:"Text Lesson",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{tag:"a"},children:[],defaultStyle:"",constraints:{childrens:[{element:"*",condition:"ALLOW"}],parents:[{element:Ge+"-lessons",condition:"ALLOW"}]},Component:$n,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function Kn(e){return Kn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Kn(e)}function Xn(){return Xn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Xn.apply(this,arguments)}function Yn(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Kn(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Kn(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Kn(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var er=Object.freeze([["svg",Yn(Yn({style:"width:40px;height:40px;",properties:{iconClass:"".concat(iconPrefix,"-phone-vr-default"),tag:"span"}},"properties",{svgOuterHtml:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M12.5636 22.7307C12.2836 22.9021 12.0658 23 11.8013 23 11.3112 23 11 22.6083 11 21.9962V11.0119C11 10.3917 11.3112 10 11.8013 10 12.0658 10 12.2836 10.1061 12.5636 10.2693L21.261 15.5738C21.7977 15.9002 22 16.1287 22 16.5041 22 16.8795 21.7977 17.0998 21.261 17.4344L12.5636 22.7307ZM12 21.8717C12 21.9572 12.0404 22 12.0889 22 12.1375 22 12.1698 21.9829 12.2102 21.9572L20.903 16.6283C20.9515 16.5941 21 16.5599 21 16.5 21 16.4401 20.9515 16.3974 20.903 16.3717L12.2102 11.0428C12.1698 11.0171 12.1375 11 12.0889 11 12.0404 11 12 11.0342 12 11.1198V21.8717Z'></path></svg>",tag:"svg"}),"title","Lesson Icon")],["paragraph",{properties:{componentType:"collection",dynamicContent:{type:"post",value:"post_title"},contents:["Video Lesson Title"]},title:"Lesson Title"}],[Ge+"-lesson-duration",{properties:{componentType:"collection"}}],[Ge+"-lesson-access",{properties:{}}]]),tr=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("a",Xn({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:er}))}));const nr={name:Ge+"-lesson-video",visibility:!1,type:"element",source:Ge,className:"",title:"Video Lesson",description:"Video Lesson",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{tag:"a"},children:[],defaultStyle:"",constraints:{childrens:[{element:"*",condition:"ALLOW"}],parents:[{element:Ge+"-lessons",condition:"ALLOW"}]},Component:tr,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function rr(){return rr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},rr.apply(this,arguments)}var ir=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("div",rr({},Qe.i.getAllAttributes(i),{ref:t,className:r}),"04.00")}));const or={name:Ge+"-lesson-duration",visibility:!1,type:"element",source:Ge,className:"",title:"Lesson duration",description:"Lesson duration",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{},defaultStyle:"",constraints:{ancestors:[{element:Ge+"-lesson-video",condition:"REQUIRED"}]},Component:ir,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function ar(){return ar=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ar.apply(this,arguments)}var sr=Object.freeze([[Ge+"-quiz-icon",{properties:{}}],["paragraph",{properties:{componentType:"collection",dynamicContent:{type:"post",value:"post_title"},contents:["Quiz Title"]},title:"Quiz Title"}],[Ge+"-lesson-access",{properties:{}}]]),lr=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("a",ar({href:"#"},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:sr}))}));const cr={name:Ge+"-quiz",visibility:!1,type:"element",source:Ge,className:"",title:"Quiz",description:"Quiz",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{tag:"a"},children:[],defaultStyle:"",constraints:{childrens:[{element:"*",condition:"ALLOW"}],parents:[{element:Ge+"-lessons",condition:"ALLOW"}]},Component:lr,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function ur(){return ur=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ur.apply(this,arguments)}var pr=Object.freeze([[Ge+"-zoom-icon",{properties:{}}],["paragraph",{properties:{componentType:"collection",dynamicContent:{type:"post",value:"post_title"},contents:["Zoom Lesson Title"]},title:"Zoom Title"}],[Ge+"-meeting-status",{properties:{}}],[Ge+"-lesson-access",{properties:{}}]]),mr=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("a",ur({href:"#"},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:pr}))}));const fr={name:Ge+"-zoom",visibility:!1,type:"element",source:Ge,className:"",title:"Zoom",description:"Zoom",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{tag:"a"},children:[],defaultStyle:"",constraints:{childrens:[{element:"*",condition:"ALLOW"}],parents:[{element:Ge+"-lessons",condition:"ALLOW"}]},Component:mr,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function dr(e){return dr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},dr(e)}function gr(){return gr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},gr.apply(this,arguments)}var yr=Object.freeze([["svg",{style:"width:40px;height:40px;",properties:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==dr(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==dr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===dr(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:"svg",enumerable:!0,configurable:!0,writable:!0}):e[t]="svg",e}({tag:"span",svgOuterHtml:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M17.707 2.293a1 1 0 0 0-1.414 0L14 4.586l-1.293-1.293a1 1 0 0 0-1.414 0l-6 6a1 1 0 0 0 1.414 1.414L12 5.414l.586.586-2.293 2.293-7 7A1 1 0 0 0 3 16v4a1 1 0 0 0 1 1h4a1 1 0 0 0 .707-.293l7-7 6-6a1 1 0 0 0 0-1.414l-4-4zm-3 4.414L17 4.414 19.586 7 15 11.586 12.414 9l2.293-2.293zM5 16.414l6-6L13.586 13l-6 6H5v-2.586z' ></path></svg>"},"tag")}]]),br=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",gr({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:yr}))}));const hr={name:Ge+"-assignment-icon",visibility:!1,type:"element",source:Ge,className:"",title:"Assignment Icon",description:"Assignment Icon",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{},children:[],defaultStyle:"",constraints:{ancestors:[{element:Ge+"-assignment",condition:"REQUIRED"}]},Component:br,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function vr(e){return vr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},vr(e)}function Or(){return Or=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Or.apply(this,arguments)}var wr=Object.freeze([["svg",{style:"width:40px;height:40px;",properties:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==vr(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==vr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===vr(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:"svg",enumerable:!0,configurable:!0,writable:!0}):e[t]="svg",e}({tag:"span",svgOuterHtml:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M 18.683594 15.40625 C 18.683594 13.429688 21.894531 13.257813 21.894531 9.394531 C 21.894531 6.597656 19.949219 5.257813 19.75 5.136719 L 21.6875 5.136719 L 23.625 4 L 17.347656 4 C 10.960938 4 9.9375 8.46875 9.9375 9.949219 C 9.9375 12.15625 11.664063 14.898438 15.136719 14.898438 C 15.453125 14.898438 15.789063 14.878906 16.144531 14.839844 C 16.085938 15.003906 15.835938 15.566406 15.835938 16.074219 C 15.835938 17.136719 16.515625 17.8125 16.75 18.222656 C 12.949219 18.1875 8.375 19.835938 8.375 23.503906 C 8.375 24.84375 9.527344 28 14.941406 28 C 21.117188 28 22.988281 24.1875 22.988281 22.050781 C 22.984375 17.96875 18.683594 17.292969 18.683594 15.40625 Z M 16.585938 14.042969 C 14.425781 14.042969 12.601563 11.324219 12.601563 8.417969 C 12.601563 7.554688 13.015625 5.046875 15.40625 5.046875 C 18.484375 5.046875 19.234375 9.609375 19.234375 10.851563 C 19.234375 11.140625 19.4375 14.042969 16.585938 14.042969 Z M 16.4375 26.679688 C 14.457031 26.679688 11.039063 25.835938 11.039063 22.835938 C 11.039063 21.796875 11.644531 19.082031 16.902344 19.082031 C 17.171875 19.082031 17.40625 19.09375 17.605469 19.113281 C 18.699219 19.929688 20.964844 21.109375 20.964844 23.28125 C 20.964844 24.265625 20.378906 26.679688 16.4375 26.679688 Z'></path></svg>"},"tag")}]]),Sr=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("span",Or({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:wr}))}));const Er={name:Ge+"-googlemeet-icon",visibility:!1,type:"element",source:Ge,className:"",title:"Google Meet Icon",description:"Google Meet Icon",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{tag:"span"},children:[],defaultStyle:"",constraints:{ancestors:[{element:Ge+"-googlemeet",condition:"REQUIRED"}]},Component:Sr,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function jr(e){return jr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},jr(e)}function Cr(){return Cr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Cr.apply(this,arguments)}var Ar=Object.freeze([["svg",{style:"width:40px;height:40px;",properties:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==jr(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==jr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===jr(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:"svg",enumerable:!0,configurable:!0,writable:!0}):e[t]="svg",e}({tag:"span",svgOuterHtml:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M 16 4 C 9.382813 4 4 9.382813 4 16 C 4 22.617188 9.382813 28 16 28 C 22.617188 28 28 22.617188 28 16 C 28 9.382813 22.617188 4 16 4 Z M 16 6 C 21.535156 6 26 10.464844 26 16 C 26 21.535156 21.535156 26 16 26 C 10.464844 26 6 21.535156 6 16 C 6 10.464844 10.464844 6 16 6 Z M 16 10 C 13.800781 10 12 11.800781 12 14 L 14 14 C 14 12.882813 14.882813 12 16 12 C 17.117188 12 18 12.882813 18 14 C 18 14.765625 17.507813 15.445313 16.78125 15.6875 L 16.375 15.8125 C 15.558594 16.082031 15 16.863281 15 17.71875 L 15 19 L 17 19 L 17 17.71875 L 17.40625 17.59375 C 18.945313 17.082031 20 15.621094 20 14 C 20 11.800781 18.199219 10 16 10 Z M 15 20 L 15 22 L 17 22 L 17 20 Z'></path></svg>"},"tag")}]]),Lr=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",Cr({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:Ar}))}));const xr={name:Ge+"-quiz-icon",visibility:!1,type:"element",source:Ge,className:"",title:"Quiz Icon",description:"Quiz Icon",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{},children:[],defaultStyle:"",constraints:{ancestors:[{element:Ge+"-quiz",condition:"REQUIRED"}]},Component:Lr,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function Pr(e){return Pr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Pr(e)}function Rr(){return Rr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Rr.apply(this,arguments)}var Mr=Object.freeze([["svg",{style:"width:40px;height:40px;",properties:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Pr(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Pr(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Pr(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:"svg",enumerable:!0,configurable:!0,writable:!0}):e[t]="svg",e}({tag:"span",svgOuterHtml:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4zM14 13h-3v3H9v-3H6v-2h3V8h2v3h3v2z'></path></svg>"},"tag")}]]),Nr=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",Rr({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:Mr}))}));const Zr={name:Ge+"-zoom-icon",visibility:!1,type:"element",source:Ge,className:"",title:"Zoom Icon",description:"Zoom Icon",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{},children:[],defaultStyle:"",constraints:{ancestors:[{element:Ge+"-zoom",condition:"REQUIRED"}]},Component:Nr,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function Tr(){return Tr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Tr.apply(this,arguments)}const Ir=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",Tr({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[[Ge+"-instructor-list-item",{properties:{}}]]}))})),kr={name:Ge+"-instructor-list",type:"element",source:Ge,className:"",title:"Instructors",description:"Instructors",icon:"".concat(Qe.i.iconPrefix,"-instructor-list-line"),hoverIcon:"".concat(Qe.i.iconPrefix,"-instructor-list-fill"),category:"Tutor LMS",properties:{},children:[],defaultStyle:"",constraints:{childrens:[{element:Ge+"-instructor-list-item",condition:"ALLOW"}]},Component:Ir,controls:{margin:!0,padding:!1,height:!1,width:!0}};function Dr(){return Dr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Dr.apply(this,arguments)}const _r={name:Ge+"-instructor-list-item",type:"element",source:Ge,className:"",title:"Instructor",description:"Instructor",visibility:!1,category:"Tutor LMS",properties:{tag:"a"},children:[],defaultStyle:"display:flex;gap:20px;align-items:center;",constraints:{parents:[{element:Ge+"-instructor-list",condition:"ALLOW"}],childrens:[{element:"*",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("a",Dr({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[[Ge+"-instructor-avatar",{properties:{}}],[Ge+"-instructor-name",{properties:{}}],[Ge+"-instructor-bio",{properties:{}}],[Ge+"-instructor-job-title",{properties:{}}]]}))})),controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function Wr(){return Wr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Wr.apply(this,arguments)}const Fr={name:Ge+"-instructor-avatar",type:"element",source:Ge,className:"",title:"Avatar",description:"Avatar",visibility:!1,category:"Tutor LMS",properties:{},defaultStyle:"height:40px;width:40px;font-size:25px;border-radius:50%;background:rgba(0,0,0,0.4);display:flex;align-items:center;justify-content:center;",constraints:{ancestors:[{element:Ge+"-instructor-list-item",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("div",Wr({},Qe.i.getAllAttributes(i),{ref:t,className:r}),"J")})),controls:{margin:!0,padding:!1,height:!1,width:!0}};function zr(){return zr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},zr.apply(this,arguments)}const Ur={name:Ge+"-instructor-name",type:"element",source:Ge,className:"",title:"Name",description:"Name",visibility:!1,category:"Tutor LMS",properties:{},defaultStyle:"font-size:18px;font-weight:bold;",constraints:{ancestors:[{element:Ge+"-instructor-list-item",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("div",zr({},Qe.i.getAllAttributes(i),{ref:t,className:r}),"Instructor")})),controls:{margin:!0,padding:!1,height:!1,width:!0}};function Br(){return Br=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Br.apply(this,arguments)}const Hr={name:Ge+"-instructor-bio",type:"element",source:Ge,className:"",title:"Bio",description:"Bio",visibility:!1,category:"Tutor LMS",properties:{},defaultStyle:"font-size:18px;font-weight:bold;",constraints:{ancestors:[{element:Ge+"-instructor-list-item",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("div",Br({},Qe.i.getAllAttributes(i),{ref:t,className:r}),"Instructor Bio")})),controls:{margin:!0,padding:!1,height:!1,width:!0}};function qr(){return qr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},qr.apply(this,arguments)}const Vr={name:Ge+"-instructor-job-title",type:"element",source:Ge,className:"",title:"Job title",description:"Job title",visibility:!1,category:"Tutor LMS",properties:{},defaultStyle:"font-size:18px;font-weight:bold;",constraints:{ancestors:[{element:Ge+"-instructor-list-item",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("div",qr({},Qe.i.getAllAttributes(i),{ref:t,className:r}),"Instructor Job Title")})),controls:{margin:!0,padding:!1,height:!1,width:!0}};function Qr(){return Qr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Qr.apply(this,arguments)}const Gr=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",Qr({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[["svg",{properties:{svgOuterHtml:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92 1.61 0 2.92-1.31 2.92-2.92s-1.31-2.92-2.92-2.92z'></path></svg>"},style:"height:24px;width:24px;min-width:auto;min-height:auto;"}],["text",{properties:{contents:["Share"]}}]]}))}));function $r(e){return $r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$r(e)}function Jr(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==$r(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==$r(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===$r(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const Kr={name:Ge+"-share-course",type:"element",source:Ge,className:"",title:"Share",description:"Share Course",icon:"".concat(Qe.i.iconPrefix,"-share-line"),hoverIcon:"".concat(Qe.i.iconPrefix,"-share-fill"),category:"Tutor LMS",properties:{attributes:Jr(Jr(Jr({},"data-element",Ge+"-share-course"),"data-tutor-modal-target","tutor-course-share-opener"),"href","#")},children:[],defaultStyle:"display:flex;align-items:center;justify-content:center;width:max-content;text-decoration:none;color:#757c8e;gap:8px;cursor:pointer;",constraints:{childrens:[{element:"*",condition:"ALLOW"}]},Component:Gr,controls:{margin:!0,padding:!1,height:!1,width:!0}};function Xr(){return Xr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Xr.apply(this,arguments)}const Yr=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",Xr({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[[Ge+"-course-level",{properties:{}}],[Ge+"-enroll-count",{properties:{}}],[Ge+"-course-duration",{properties:{}}],[Ge+"-course-update",{properties:{}}],[Ge+"-sidebar-meta",{properties:{}}]]}))}));function ei(e){return ei="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ei(e)}const ti={name:Ge+"-course-meta",type:"element",source:Ge,className:"",title:"Course Meta",description:"Show course meta elements",icon:"".concat(Qe.i.iconPrefix,"-course-meta-line"),hoverIcon:"".concat(Qe.i.iconPrefix,"-course-meta-fill"),category:"Tutor LMS",properties:{attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==ei(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==ei(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===ei(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-element",Ge+"-course-meta")},children:[],defaultStyle:"display:flex;align-items:flex-start;justify-content:center;flex-direction:column;column-gap:12px;",constraints:{childrens:[{element:"*",condition:"ALLOW"}]},Component:Yr,controls:{margin:!0,padding:!1,height:!1,width:!0}};function ni(){return ni=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ni.apply(this,arguments)}const ri={name:Ge+"-course-level",type:"element",source:Ge,className:"",title:"Course Level",description:"Course level",visibility:!1,properties:{tag:"div"},children:[],defaultStyle:"display:flex;align-items:center;column-gap:12px;",constraints:{ancestors:[{element:Ge+"-course-meta",condition:"REQUIRED"}],childrens:[{element:"*",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",ni({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[["svg",{properties:{svgOuterHtml:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M 4 5 L 4 7 L 16 7 L 16 5 L 4 5 z M 22 5.5 L 21.279297 6.1894531 L 17 10.5 L 18.410156 11.910156 L 21 9.3105469 L 21 28 L 23 28 L 23 9.3105469 L 25.589844 11.910156 L 27 10.5 L 22.720703 6.1894531 L 22 5.5 z M 4 9 L 4 11 L 14 11 L 14 9 L 4 9 z M 4 13 L 4 15 L 12 15 L 12 13 L 4 13 z M 4 17 L 4 19 L 10 19 L 10 17 L 4 17 z M 4 21 L 4 23 L 8 23 L 8 21 L 4 21 z M 4 25 L 4 27 L 6 27 L 6 25 L 4 25 z'></path></svg>"},style:"height:24px;width:24px;min-width:auto;min-height:auto;"}],[Ge+"-course-level-text",{properties:{}}]]}))})),controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function ii(){return ii=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ii.apply(this,arguments)}const oi={name:Ge+"-course-level-text",type:"element",source:Ge,className:"",title:"Course Level Text",description:"Course Level Text",visibility:!1,properties:{tag:"span"},constraints:{ancestors:[{element:Ge+"-course-level",condition:"REQUIRED"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=(e.renderChildren,Qe.i.getCanvasElement(n));return $e.createElement("span",ii({},Qe.i.getAllAttributes(i),{ref:t,className:r}),"Beginner")})),controls:{margin:!1,padding:!1,height:!1,width:!1}};function ai(){return ai=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ai.apply(this,arguments)}const si={name:Ge+"-enroll-count",type:"element",source:Ge,className:"",title:"Enroll Count",description:"Enroll count",visibility:!1,properties:{tag:"div"},children:[],defaultStyle:"display:flex;align-items:center;column-gap:12px;",constraints:{ancestors:[{element:Ge+"-course-meta",condition:"REQUIRED"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",ai({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[["svg",{properties:{svgOuterHtml:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M13.5.67s.74 2.65.74 4.8c0 2.06-1.35 3.73-3.41 3.73-2.07 0-3.63-1.67-3.63-3.73l.03-.36C5.21 7.51 4 10.62 4 14c0 4.42 3.58 8 8 8s8-3.58 8-8C20 8.61 17.41 3.8 13.5.67zM11.71 19c-1.78 0-3.22-1.4-3.22-3.14 0-1.62 1.05-2.76 2.81-3.12 1.77-.36 3.6-1.21 4.62-2.58.39 1.29.59 2.65.59 4.04 0 2.65-2.15 4.8-4.8 4.8z'></path></svg>"},style:"height:24px;width:24px;min-width:auto;min-height:auto;"}],[Ge+"-enroll-count-value",{properties:{}}],["text",{properties:{contents:["Total Enrolled"]}}]]}))})),controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function li(){return li=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},li.apply(this,arguments)}const ci={name:Ge+"-enroll-count-value",type:"element",source:Ge,className:"",title:"Enroll Count Value",description:"Enroll Count Value",visibility:!1,properties:{tag:"span"},constraints:{ancestors:[{element:Ge+"-enroll-count",condition:"REQUIRED"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=(e.renderChildren,Qe.i.getCanvasElement(n));return $e.createElement("span",li({},Qe.i.getAllAttributes(i),{ref:t,className:r}),"0")})),controls:{margin:!1,padding:!1,height:!1,width:!1}};function ui(){return ui=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ui.apply(this,arguments)}const pi={name:Ge+"-course-duration",type:"element",source:Ge,className:"",title:"Course Duration",description:"Course Duration",visibility:!1,properties:{tag:"div"},children:[],defaultStyle:"display:flex;align-items:center;column-gap:12px;",constraints:{childrens:[{element:"*",condition:"ALLOW"}],ancestors:[{element:Ge+"-course-meta",condition:"REQUIRED"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",ui({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[["svg",{properties:{svgOuterHtml:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='m14.5 14.2 2.9 1.7-.8 1.3L13 15v-5h1.5v4.2zM22 14c0 4.41-3.59 8-8 8-2.02 0-3.86-.76-5.27-2H4c-1.15 0-2-.85-2-2V9c0-1.12.89-1.96 2-2v-.5C4 4.01 6.01 2 8.5 2c2.34 0 4.24 1.79 4.46 4.08.34-.05.69-.08 1.04-.08 4.41 0 8 3.59 8 8zM6 7h5v-.74A2.509 2.509 0 0 0 8.5 4 2.5 2.5 0 0 0 6 6.5V7zm14 7c0-3.31-2.69-6-6-6s-6 2.69-6 6 2.69 6 6 6 6-2.69 6-6z'></path></svg>"},style:"height:24px;width:24px;min-width:auto;min-height:auto;"}],[Ge+"-course-duration-value",{properties:{}}],["text",{properties:{contents:["Duration"]}}]]}))})),controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function mi(){return mi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},mi.apply(this,arguments)}const fi={name:Ge+"-course-update",type:"element",source:Ge,className:"",title:"Course Update",description:"Course update",visibility:!1,properties:{tag:"div"},children:[],defaultStyle:"display:flex;align-items:center;column-gap:12px;",constraints:{ancestors:[{element:Ge+"-course-meta",condition:"REQUIRED"}],childrens:[{element:"*",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",mi({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[["svg",{properties:{svgOuterHtml:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M21 10.12h-6.78l2.74-2.82c-2.73-2.7-7.15-2.8-9.88-.1-2.73 2.71-2.73 7.08 0 9.79s7.15 2.71 9.88 0C18.32 15.65 19 14.08 19 12.1h2c0 1.98-.88 4.55-2.64 6.29-3.51 3.48-9.21 3.48-12.72 0-3.5-3.47-3.53-9.11-.02-12.58s9.14-3.47 12.65 0L21 3v7.12zM12.5 8v4.25l3.5 2.08-.72 1.21L11 13V8h1.5z'></path></svg>"},style:"height:24px;width:24px;min-width:auto;min-height:auto;"}],[Ge+"-course-update-value",{properties:{}}],["text",{properties:{contents:["Last Updated"]}}]]}))})),controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function di(){return di=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},di.apply(this,arguments)}const gi={name:Ge+"-course-update-value",type:"element",source:Ge,className:"",title:"Course Update Value",description:"Course Update Value",visibility:!1,properties:{tag:"span"},constraints:{ancestors:[{element:Ge+"-course-update",condition:"REQUIRED"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=(e.renderChildren,Qe.i.getCanvasElement(n));return $e.createElement("span",di({},Qe.i.getAllAttributes(i),{ref:t,className:r}),"March 12, 2022")})),controls:{margin:!1,padding:!1,height:!1,width:!1}};function yi(){return yi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},yi.apply(this,arguments)}const bi={name:Ge+"-sidebar-meta",type:"element",source:Ge,className:"",title:"Sidebar Meta",description:"Sidebar Meta",visibility:!1,properties:{tag:"div"},children:[],defaultStyle:"display:flex;align-items:center;column-gap:12px;",constraints:{ancestors:[{element:Ge+"-course-meta",condition:"REQUIRED"}],childrens:[{element:Ge+"-sidebar-meta-value",condition:"ALLOW"},{element:Ge+"-sidebar-meta-icon",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",yi({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[[Ge+"-sidebar-meta-icon",{properties:{},style:"font-size: 24px;"}],[Ge+"-sidebar-meta-value",{properties:{}}]]}))})),controls:{margin:!0,padding:!1,height:!1,width:!0}};function hi(){return hi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},hi.apply(this,arguments)}const vi={name:Ge+"-sidebar-meta-value",type:"element",source:Ge,className:"",title:"Sidebar Meta Value",description:"Sidebar Meta Value",visibility:!1,properties:{tag:"span"},constraints:{ancestors:[{element:Ge+"-sidebar-meta",condition:"REQUIRED"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("span",hi({},Qe.i.getAllAttributes(i),{ref:t,className:r}),"Certificate of completion")})),controls:{margin:!1,padding:!1,height:!1,width:!1}};function Oi(){return Oi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Oi.apply(this,arguments)}const wi={name:Ge+"-sidebar-meta-icon",type:"element",source:Ge,className:"",title:"Sidebar Meta Icon",description:"Sidebar Meta Icon",visibility:!1,properties:{tag:"span"},constraints:{ancestors:[{element:Ge+"-sidebar-meta",condition:"REQUIRED"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("span",Oi({},Qe.i.getAllAttributes(i),{ref:t,className:"droipIcon-library ".concat(r)}))})),controls:{margin:!1,padding:!1,height:!1,width:!1}};function Si(){return Si=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Si.apply(this,arguments)}const Ei={name:Ge+"-course-duration-value",type:"element",source:Ge,className:"",title:"Course Duration Value",description:"Course Duration Value",visibility:!1,properties:{tag:"span"},constraints:{ancestors:[{element:Ge+"-course-duration",condition:"REQUIRED"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("span",Si({},Qe.i.getAllAttributes(i),{ref:t,className:r}),"0 Hours 0 Minutes")})),controls:{margin:!1,padding:!1,height:!1,width:!1}};function ji(){return ji=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ji.apply(this,arguments)}const Ci=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",ji({href:"#"},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[[Ge+"-course-thumbnail-image",{properties:{}}],[Ge+"-course-thumbnail-video",{properties:{}}]]}))}));function Ai(e){return Ai="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ai(e)}function Li(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function xi(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Li(Object(n),!0).forEach((function(t){Pi(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Li(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Pi(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Ai(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Ai(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Ai(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ri=[{key:"state",label:"Show State",setting:xi(xi({},Qe.i.elementSettings.SELECT),{},{options:[{value:"image",title:"Image"},{value:"video",title:"Video"}]})}],Mi=function(e){var t=Qe.i.getCanvasElement(e).properties.settings;return(null==t?void 0:t.state)||"image"};const Ni={name:Ge+"-course-thumbnail",type:"element",source:Ge,className:"",title:"Thumbnail",description:"Thumbnail",icon:"".concat(Qe.i.iconPrefix,"-thumbnail-line"),hoverIcon:"".concat(Qe.i.iconPrefix,"-thumbnail-fill"),category:"Tutor LMS",properties:{tag:"div",settings:{state:"image"}},children:[],constraints:{childrens:[{element:Ge+"-course-thumbnail-image",condition:"ALLOW"},{element:Ge+"-course-thumbnail-video",condition:"ALLOW"}]},Component:Ci,controls:{margin:!0,padding:!1,height:!1,width:!0},settings:Ri};function Zi(e){return Zi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Zi(e)}function Ti(){return Ti=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ti.apply(this,arguments)}var Ii=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=(e.renderChildren,Qe.i.getCanvasElement(n));return $e.createElement("img",Ti({src:"https://placehold.co/728X428?text=Image"},Qe.i.getAllAttributes(i),{ref:t,className:r,"data-element_hide":"image"!==Mi(i.parentId)}))}));const ki={name:Ge+"-course-thumbnail-image",type:"element",source:Ge,className:"",title:"Image",description:"Image",visibility:!1,properties:{tag:"img",attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Zi(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Zi(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Zi(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","image")},defaultStyle:"width:100%;",constraints:{parents:[{element:Ge+"-course-thumbnail",condition:"ALLOW"}]},Component:Ii,controls:{margin:!1,padding:!1,height:!1,width:!1},manualDelete:!1};function Di(e){return Di="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Di(e)}function _i(){return _i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},_i.apply(this,arguments)}var Wi=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=(e.renderChildren,Qe.i.getCanvasElement(n));return $e.createElement("video",_i({src:"https://placehold.co/1920X1080.mp4?text=Video"},Qe.i.getAllAttributes(i),{ref:t,className:r,"data-element_hide":"video"!==Mi(i.parentId)}))}));const Fi={name:Ge+"-course-thumbnail-video",type:"element",source:Ge,className:"",title:"Video",description:"Video",visibility:!1,properties:{tag:"video",attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Di(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Di(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Di(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","video")},defaultStyle:"width:100%;",constraints:{parents:[{element:Ge+"-course-thumbnail",condition:"ALLOW"}]},Component:Wi,controls:{margin:!1,padding:!1,height:!1,width:!1},manualDelete:!1};function zi(){return zi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},zi.apply(this,arguments)}const Ui=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",zi({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[[Ge+"-course-progress",{properties:{}}],[Ge+"-course-enroll-buttons",{properties:{}}],[Ge+"-course-action-buttons",{properties:{}}],[Ge+"-enroll-info",{properties:{}}]]}))})),Bi={name:Ge+"-action-box",type:"element",source:Ge,className:"",title:"Action Box",description:"Show Action Box elements",icon:"".concat(Qe.i.iconPrefix,"-action-box-line"),hoverIcon:"".concat(Qe.i.iconPrefix,"-action-box-fill"),category:"Tutor LMS",properties:{tag:"div"},children:[],defaultStyle:"padding:32px;background-color:#f4f6f9;border-top-left-radius:6px;border-top-right-radius:6px;",constraints:{childrens:[{element:Ge+"-course-progress",condition:"ALLOW"},{element:Ge+"-course-enroll-buttons",condition:"ALLOW"},{element:Ge+"-course-action-buttons",condition:"ALLOW"},{element:Ge+"-enroll-info",condition:"ALLOW"}]},Component:Ui,controls:{margin:!0,padding:!1,height:!1,width:!0}};function Hi(e){return Hi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Hi(e)}function qi(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Vi(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?qi(Object(n),!0).forEach((function(t){Qi(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):qi(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Qi(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Hi(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Hi(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Hi(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Gi=Qe.i.elementSettings,$i=[{key:"state",label:"Show State",setting:Vi(Vi({},Gi.SELECT),{},{options:[{value:"free",title:"Free"},{value:"paid",title:"Paid"}]})}],Ji=[{key:"state",label:"Show State",setting:Vi(Vi({},Gi.SELECT),{},{options:[{value:"add-to-cart",title:"Add to cart"},{value:"view-cart",title:"View Cart"}]})}],Ki=[{key:"state",label:"Show State",setting:Vi(Vi({},Gi.SELECT),{},{options:[{value:"start-learning",title:"Start Learning"},{value:"continue-learning",title:"Continue Learning"},{value:"complete-course",title:"Complete Course"},{value:"view-certificate",title:"View Certificate"}]})}];function Xi(e){return Xi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Xi(e)}function Yi(){return Yi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Yi.apply(this,arguments)}var eo=function(e){var t=Qe.i.getCanvasElement(e).properties.settings;return(null==t?void 0:t.state)||"free"},to=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",Yi({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[[Ge+"-enroll-button",{properties:{}}],[Ge+"-paid-action-buttons",{properties:{}}]]}))}));const no={name:Ge+"-course-enroll-buttons",type:"element",source:Ge,className:"",title:"Course Enroll Buttons",description:"Show Course Enroll Button elements",visibility:!1,category:"Tutor LMS",properties:{tag:"div",attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Xi(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Xi(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Xi(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-element",Ge+"-course-enroll-buttons"),settings:{state:"free"}},children:[],defaultStyle:"margin-top: 20px;",constraints:{childrens:[{element:Ge+"-enroll-button",condition:"ALLOW"},{element:Ge+"-paid-action-buttons",condition:"ALLOW"}],parents:[{element:Ge+"-action-box",condition:"ALLOW"}]},Component:to,controls:{margin:!0,padding:!1,height:!1,width:!0},settings:$i,manualDelete:!1};function ro(e){return ro="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ro(e)}function io(){return io=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},io.apply(this,arguments)}var oo=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=e.properties.contents,a=Qe.i.getCanvasElement(n);return $e.createElement("a",io({},Qe.i.getAllAttributes(a),{ref:t,className:r,"data-element_hide":"free"!==eo(a.parentId)}),i({template:[["text",{properties:{contents:o}}]]}))}));const ao={name:Ge+"-enroll-button",type:"element",source:Ge,className:"",title:"Enroll Button",description:"Enroll Button",visibility:!1,properties:{tag:"a",contents:["Free"],type:"href",attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==ro(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==ro(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===ro(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({href:"",target:""},"data-state","free")},children:[],defaultStyle:"display:flex;\n    justify-content:center;\n    width:100%;font-size:18px;\n    padding:10px 20px;\n    border:0px;\n    background-color:#3e64de;\n    color:#fff;\n    align-items: center;\n    font-weight: 400;\n    line-height: 1.4;\n    text-align: center;\n    text-decoration: none;\n    vertical-align: middle;\n    user-select: none;\n    border-radius: 6px;\n    transition: color 200ms ease-in-out,background-color 200ms ease-in-out,border-color 200ms ease-in-out;",constraints:{parents:[{element:Ge+"-course-enroll-buttons",condition:"ALLOW"}]},Component:oo,controls:{margin:!0,padding:!1}};function so(e){return so="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},so(e)}function lo(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==so(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==so(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===so(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function co(){return co=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},co.apply(this,arguments)}var uo=function(e){var t=Qe.i.getCanvasElement(e).properties.settings;return(null==t?void 0:t.state)||"add-to-cart"},po=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",co({},Qe.i.getAllAttributes(o),{ref:t,className:r,"data-element_hide":"paid"!==eo(o.parentId)}),i({template:[[Ge+"-price-and-add-to-cart",{properties:{}}],[Ge+"-view-cart-button",{properties:{}}]]}))}));const mo={name:Ge+"-paid-action-buttons",type:"element",source:Ge,className:"",title:"Paid Action Buttons",description:"Show Paid Action Button elements",icon:"".concat(Qe.i.iconPrefix,"-tutorials-line"),hoverIcon:"".concat(Qe.i.iconPrefix,"-tutorials-fill"),category:"Tutor LMS",visibility:!1,properties:{tag:"div",attributes:lo(lo({},"data-element",Ge+"-paid-action-buttons"),"data-state","paid"),settings:{state:"add-to-cart"}},children:[],defaultStyle:"margin-top: 20px;",constraints:{parents:[{element:Ge+"-course-enroll-buttons",condition:"ALLOW"}]},Component:po,controls:{margin:!0,padding:!1,height:!1,width:!0},settings:Ji,manualDelete:!1};function fo(e){return fo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},fo(e)}function go(){return go=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},go.apply(this,arguments)}var yo=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=e.properties.contents,a=Qe.i.getCanvasElement(n);return $e.createElement("button",go({},Qe.i.getAllAttributes(a),{ref:t,className:r,"data-element_hide":"add-to-cart"!==uo(a.parentId)}),i({template:[["text",{properties:{contents:o}}]]}))}));const bo={name:Ge+"-add-to-cart-button",type:"element",source:Ge,className:"",title:"Add to Cart Button",description:"Add to Cart Button",visibility:!1,properties:{tag:"button",contents:["Add to cart"],attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==fo(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==fo(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===fo(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","add-to-cart")},children:[],defaultStyle:"display:flex;\n    justify-content:center;\n    width:100%;font-size:18px;\n    padding:10px 20px;\n    border:0px;\n    background-color:#222;\n    color:#fff;\n    align-items: center;\n    font-weight: 400;\n    line-height: 1.4;\n    text-align: center;\n    text-decoration: none;\n    vertical-align: middle;\n    user-select: none;\n    border-radius: 6px;\n    transition: color 200ms ease-in-out,background-color 200ms ease-in-out,border-color 200ms ease-in-out;",constraints:{childrens:[{element:"text",condition:"ALLOW"},{element:"heading",condition:"ALLOW"},{element:"svg",condition:"ALLOW"},{element:"svg-icon",condition:"ALLOW"},{element:"custom-code",condition:"ALLOW"}]},Component:yo,controls:{margin:!0,padding:!1}};function ho(e){return ho="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ho(e)}function vo(){return vo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},vo.apply(this,arguments)}var Oo=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=e.properties.contents,a=Qe.i.getCanvasElement(n);return $e.createElement("a",vo({},Qe.i.getAllAttributes(a),{ref:t,className:r,"data-element_hide":"view-cart"!==uo(a.parentId)}),i({template:[["text",{properties:{contents:o}}]]}))}));const wo={name:Ge+"-view-cart-button",type:"element",source:Ge,className:"",title:"View Cart Button",description:"View Cart Button",visibility:!1,properties:{tag:"a",contents:["View Cart"],attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==ho(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==ho(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===ho(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","view-cart")},children:[],defaultStyle:"display:flex;\n    justify-content:center;\n    width:100%;\n    font-size:18px;\n    padding:10px 20px;\n    border:1px solid #3e64de;\n    background-color: transparent;\n    color:#3e64de;\n    align-items: center;\n    font-weight: 400;\n    line-height: 1.4;\n    text-align: center;\n    text-decoration: none;\n    vertical-align: middle;\n    user-select: none;\n    border-radius: 6px;\n    transition: color 200ms ease-in-out,background-color 200ms ease-in-out,border-color 200ms ease-in-out;",constraints:{childrens:[{element:"text",condition:"ALLOW"},{element:"heading",condition:"ALLOW"},{element:"svg",condition:"ALLOW"},{element:"svg-icon",condition:"ALLOW"},{element:"custom-code",condition:"ALLOW"}]},Component:Oo,controls:{margin:!0,padding:!1}};function So(){return So=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},So.apply(this,arguments)}const Eo={name:Ge+"-enroll-info",type:"element",source:Ge,className:"",title:"Enroll Info",description:"Enroll Info",visibility:!1,category:"Tutor LMS",properties:{tag:"div"},children:[],defaultStyle:"display:flex;align-items:center;column-gap:8px;margin-top:20px;",constraints:{parents:[{element:Ge+"-action-box",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",So({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[["svg",{properties:{svgOuterHtml:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32'><path d='M 4 5 L 4 7 L 16 7 L 16 5 L 4 5 z M 22 5.5 L 21.279297 6.1894531 L 17 10.5 L 18.410156 11.910156 L 21 9.3105469 L 21 28 L 23 28 L 23 9.3105469 L 25.589844 11.910156 L 27 10.5 L 22.720703 6.1894531 L 22 5.5 z M 4 9 L 4 11 L 14 11 L 14 9 L 4 9 z M 4 13 L 4 15 L 12 15 L 12 13 L 4 13 z M 4 17 L 4 19 L 10 19 L 10 17 L 4 17 z M 4 21 L 4 23 L 8 23 L 8 21 L 4 21 z M 4 25 L 4 27 L 6 27 L 6 25 L 4 25 z'></path></svg>"},style:"height:24px;width:24px;min-width:auto;min-height:auto;"}],["text",{properties:{contents:["You enrolled in this course on"]}}],[Ge+"-enroll-date",{properties:{},style:"color: #24A148;font-weight: 700;"}]]}))})),controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function jo(){return jo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},jo.apply(this,arguments)}const Co={name:Ge+"-enroll-date",type:"element",source:Ge,className:"",title:"Enroll Date",description:"Enroll Date",visibility:!1,properties:{tag:"span"},constraints:{ancestors:[{element:Ge+"-enroll-info",condition:"REQUIRED"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("span",jo({},Qe.i.getAllAttributes(i),{ref:t,className:r}),"November 27, 2023")})),controls:{margin:!1,padding:!1,height:!1,width:!1}};function Ao(e){return Ao="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ao(e)}function Lo(){return Lo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Lo.apply(this,arguments)}var xo=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=(e.properties.contents,Qe.i.getCanvasElement(n));return $e.createElement("div",Lo({},Qe.i.getAllAttributes(o),{ref:t,className:r,"data-element_hide":"add-to-cart"!==uo(o.parentId)}),i({template:[["div",{style:"display: flex;\n      column-gap: 16px;\n      margin-bottom: 16px;\n      ",properties:{},title:"Price Wrap"},[[Ge+"-discounted-price",{style:"color: #212327;\n          font-weight: 700;\n          font-size: 1.25rem;\n          line-height: 1.25rem;\n                  ",properties:{contents:["1500.00"]}}],[Ge+"-regular-price",{style:"color: #757c8e;\n          font-size: 0.875rem;\n          font-weight: 400;\n          line-height: 0.875rem;\n          align-self: flex-end;\n          margin-bottom: 1px;\n          text-decoration: line-through;\n          ",properties:{contents:["2000.00"]}}]]],[Ge+"-add-to-cart-button",{properties:{}}]]}))}));const Po={name:Ge+"-price-and-add-to-cart",type:"element",source:Ge,className:"",title:"Price and Add to cart",description:"Price and Add to cart",visibility:!1,properties:{tag:"div",contents:["Price and Add to cart"],attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Ao(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Ao(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Ao(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","add-to-cart")},children:[],constraints:{childrens:[{element:"text",condition:"ALLOW"},{element:"heading",condition:"ALLOW"},{element:"svg",condition:"ALLOW"},{element:"svg-icon",condition:"ALLOW"},{element:"custom-code",condition:"ALLOW"}]},Component:xo,controls:{margin:!0,padding:!1}};function Ro(){return Ro=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ro.apply(this,arguments)}const Mo={name:Ge+"-discounted-price",type:"element",source:Ge,className:"",title:"Discounted Price",description:"Discounted Price",visibility:!1,properties:{tag:"span"},constraints:{},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("span",Ro({},Qe.i.getAllAttributes(i),{ref:t,className:r}),"1500")})),controls:{margin:!1,padding:!1,height:!1,width:!1}};function No(){return No=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},No.apply(this,arguments)}const Zo={name:Ge+"-regular-price",type:"element",source:Ge,className:"",title:"Regular Price",description:"Regular Price",visibility:!1,properties:{tag:"span"},constraints:{},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("span",No({},Qe.i.getAllAttributes(i),{ref:t,className:r}),"2000")})),controls:{margin:!1,padding:!1,height:!1,width:!1}};function To(){return To=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},To.apply(this,arguments)}const Io={name:Ge+"-course-progress",type:"element",source:Ge,className:"",title:"Course Progress",description:"Course Progress",visibility:!1,category:"Tutor LMS",properties:{tag:"div"},children:[],defaultStyle:"color: #212327;\n                font-size: 1.25rem;\n                font-weight: 700;\n                line-height: 1.25rem;\n                ",constraints:{parents:[{element:Ge+"-action-box",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",To({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[["text",{properties:{contents:["Course Progress"]}}],["div",{properties:{},title:"Progress Wrapper",style:"display:flex;\n          align-items: center;\n          justify-content: space-between;\n          font-size: 1rem;\n          color: #41454F;\n          margin-top: 16px;\n          font-weight: 400;"},[[Ge+"-lesson-counter",{properties:{}}],[Ge+"-progress-percent",{properties:{}}]]],["div",{properties:{},title:"Progress Bar",style:"position: relative;\n          width: 100%;\n          height: 4px;\n          background-color: #e3e5eb;\n          border-radius: 10px;\n          overflow: hidden;\n          margin-top: 12px;"},[[Ge+"-progress-bar-inner",{properties:{}}]]]]}))})),controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function ko(){return ko=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ko.apply(this,arguments)}const Do={name:Ge+"-lesson-counter",type:"element",source:Ge,className:"",title:"Lesson Counter",description:"Lesson Counter",visibility:!1,properties:{tag:"span"},constraints:{},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("span",ko({},Qe.i.getAllAttributes(i),{ref:t,className:r}),"0/14")})),controls:{margin:!1,padding:!1,height:!1,width:!1}};function _o(){return _o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},_o.apply(this,arguments)}const Wo={name:Ge+"-progress-percent",type:"element",source:Ge,className:"",title:"Progress Percent",description:"Progress Percent",visibility:!1,properties:{tag:"span"},constraints:{},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("span",_o({},Qe.i.getAllAttributes(i),{ref:t,className:r}),"0% Complete")})),controls:{margin:!1,padding:!1,height:!1,width:!1}};function Fo(){return Fo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Fo.apply(this,arguments)}const zo={name:Ge+"-progress-bar-inner",type:"element",source:Ge,className:"",title:"Progress Bar Inner",description:"Progress Bar Inner",visibility:!1,properties:{tag:"div"},defaultStyle:"background-color: #3e64de;\n  position: absolute;\n  width: 30%;\n  height: 100%;",constraints:{ancestors:[{element:Ge+"-course-progress",condition:"REQUIRED"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("div",Fo({},Qe.i.getAllAttributes(i),{ref:t,className:r}))})),controls:{margin:!1,padding:!1,height:!1,width:!1}};function Uo(e){return Uo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Uo(e)}function Bo(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Uo(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Uo(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Uo(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ho(){return Ho=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ho.apply(this,arguments)}var qo=function(e){var t=Qe.i.getCanvasElement(e).properties.settings;return(null==t?void 0:t.state)||"start-learning"},Vo=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",Ho({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[[Ge+"-start-learning",{properties:{},style:"margin-top: 32px;"}],[Ge+"-continue-learning",{properties:{},style:"margin-top: 32px;"}],[Ge+"-complete-course",{properties:{},style:"margin-top: 32px;"}],[Ge+"-retake-and-certificate",{properties:{},style:"margin-top: 32px;"}]]}))}));const Qo={name:Ge+"-course-action-buttons",type:"element",source:Ge,className:"",title:"Course Action Buttons",description:"Course Action Buttons",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",visibility:!1,properties:{tag:"div",settings:{state:"start-learning"},attributes:Bo(Bo({},"data-element",Ge+"-course-action-buttons"),"data-state","start-learning")},children:[],constraints:{childrens:[{element:Ge+"-start-learning",condition:"ALLOW"},{element:Ge+"-continue-learning",condition:"ALLOW"},{element:Ge+"-retake-and-certificate",condition:"ALLOW"},{element:Ge+"-complete-course",condition:"ALLOW"}],parents:[{element:Ge+"-action-box",condition:"ALLOW"}]},Component:Vo,controls:{margin:!0,padding:!1,height:!1,width:!0},settings:Ki,manualDelete:!1};function Go(e){return Go="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Go(e)}function $o(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Go(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Go(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Go(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Jo(){return Jo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Jo.apply(this,arguments)}var Ko=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=e.properties.contents,a=Qe.i.getCanvasElement(n);return $e.createElement("a",Jo({},Qe.i.getAllAttributes(a),{ref:t,className:r,"data-element_hide":"start-learning"!==qo(a.parentId)}),i({template:[["text",{properties:{contents:o}}]]}))}));const Xo={name:Ge+"-start-learning",type:"element",source:Ge,className:"",title:"Start Learning",description:"Start Learning",visibility:!1,properties:$o({tag:"a",contents:["Start Learning"],type:"href",attributes:{href:"",target:""}},"attributes",$o({},"data-state","start-learning")),children:[],defaultStyle:"display:flex;\n    justify-content:center;\n    width:100%;\n    font-size:18px;\n    padding:10px 20px;\n    border:0px;\n    background-color:#3e64de;\n    color:#fff;\n    align-items: center;\n    font-weight: 400;\n    line-height: 1.4;\n    text-align: center;\n    text-decoration: none;\n    vertical-align: middle;\n    user-select: none;\n    border-radius: 6px;\n    transition: color 200ms ease-in-out,background-color 200ms ease-in-out,border-color 200ms ease-in-out;",constraints:{parents:[{element:Ge+"-course-action-buttons",condition:"ALLOW"}]},Component:Ko,controls:{margin:!0,padding:!1}};function Yo(e){return Yo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Yo(e)}function ea(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Yo(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Yo(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Yo(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ta(){return ta=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ta.apply(this,arguments)}var na=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=e.properties.contents,a=Qe.i.getCanvasElement(n);return $e.createElement("button",ta({},Qe.i.getAllAttributes(a),{ref:t,className:r,"data-element_hide":"complete-course"!==qo(a.parentId)}),i({template:[["text",{properties:{contents:o}}]]}))}));const ra={name:Ge+"-complete-course",type:"element",source:Ge,className:"",title:"Complete Course",description:"Complete Course",visibility:!1,properties:ea({tag:"button",contents:["Complete Course"],attributes:{href:"",target:""}},"attributes",ea({},"data-state","complete-course")),children:[],defaultStyle:"display:flex;\n    justify-content:center;\n    width:100%;\n    font-size:18px;\n    padding:10px 20px;\n    border:0px;\n    background-color:#222;\n    color:#fff;\n    align-items: center;\n    font-weight: 400;\n    line-height: 1.4;\n    text-align: center;\n    text-decoration: none;\n    vertical-align: middle;\n    user-select: none;\n    border-radius: 6px;\n    transition: color 200ms ease-in-out,background-color 200ms ease-in-out,border-color 200ms ease-in-out;",constraints:{parents:[{element:Ge+"-course-action-buttons",condition:"ALLOW"}]},Component:na,controls:{margin:!0,padding:!1}};function ia(e){return ia="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ia(e)}function oa(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==ia(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==ia(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===ia(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function aa(){return aa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},aa.apply(this,arguments)}var sa=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=e.properties.contents,a=Qe.i.getCanvasElement(n);return $e.createElement("a",aa({},Qe.i.getAllAttributes(a),{ref:t,className:r,"data-element_hide":"continue-learning"!==qo(a.parentId)}),i({template:[["text",{properties:{contents:o}}]]}))}));const la={name:Ge+"-continue-learning",type:"element",source:Ge,className:"",title:"Continue Learning",description:"Continue Learning",visibility:!1,properties:oa({tag:"a",contents:["Continue Learning"],attributes:{target:""}},"attributes",oa({},"data-state","continue-learning")),children:[],defaultStyle:"display:flex;\n    justify-content:center;\n    width:100%;\n    font-size:18px;\n    padding:10px 20px;\n    border:0px;\n    background-color:#222;\n    color:#fff;\n    align-items: center;\n    font-weight: 400;\n    line-height: 1.4;\n    text-align: center;\n    text-decoration: none;\n    vertical-align: middle;\n    user-select: none;\n    border-radius: 6px;\n    transition: color 200ms ease-in-out,background-color 200ms ease-in-out,border-color 200ms ease-in-out;",constraints:{parents:[{element:Ge+"-course-action-buttons",condition:"ALLOW"}]},Component:sa,controls:{margin:!0,padding:!1}};function ca(e){return ca="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ca(e)}function ua(){return ua=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ua.apply(this,arguments)}var pa=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=(e.properties.contents,Qe.i.getCanvasElement(n));return $e.createElement("div",ua({},Qe.i.getAllAttributes(o),{ref:t,className:r,"data-element_hide":"view-certificate"!==qo(o.parentId)}),i({template:[[Ge+"-retake-course",{properties:{}}],[Ge+"-view-certificate",{properties:{}}]]}))}));const ma={name:Ge+"-retake-and-certificate",type:"element",source:Ge,className:"",title:"Retake And Certificate",description:"Retake And Certificate",visibility:!1,properties:{tag:"div",contents:["Retake And Certificate"],attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==ca(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==ca(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===ca(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","view-certificate")},children:[],defaultStyle:"display:flex;\n    align-items: center;\n    gap: 20px;",constraints:{childrens:[{element:Ge+"-retake-course",condition:"ALLOW"},{element:Ge+"-view-certificate",condition:"ALLOW"}],parents:[{element:Ge+"-course-action-buttons",condition:"ALLOW"}]},Component:pa,controls:{margin:!0,padding:!1},manualDelete:!1};function fa(){return fa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},fa.apply(this,arguments)}const da={name:Ge+"-retake-course",type:"element",source:Ge,className:"",title:"Retake Course",description:"Retake Course",visibility:!1,properties:{tag:"a",contents:["Retake Course"],type:"href",attributes:{href:"",target:""}},children:[],defaultStyle:"display:flex;\n    justify-content:center;\n    width:100%;\n    font-size:18px;\n    padding:10px 20px;\n    border:0px;\n    background-color:#222;\n    color:#fff;\n    align-items: center;\n    font-weight: 400;\n    line-height: 1.4;\n    text-align: center;\n    text-decoration: none;\n    vertical-align: middle;\n    user-select: none;\n    border-radius: 6px;\n    transition: color 200ms ease-in-out,background-color 200ms ease-in-out,border-color 200ms ease-in-out;",constraints:{parents:[{element:Ge+"-retake-and-certificate",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=e.properties.contents,a=Qe.i.getCanvasElement(n);return $e.createElement("a",fa({},Qe.i.getAllAttributes(a),{ref:t,className:r}),i({template:[["text",{properties:{contents:o}}]]}))})),controls:{margin:!0,padding:!1}};function ga(){return ga=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ga.apply(this,arguments)}const ya={name:Ge+"-view-certificate",type:"element",source:Ge,className:"",title:"View Certificate",description:"View Certificate",visibility:!1,properties:{tag:"a",contents:["View Certificate"],attributes:{target:""}},children:[],defaultStyle:"display:flex;\n    justify-content:center;\n    width:100%;\n    font-size:18px;\n    padding:10px 20px;\n    border:0px;\n    background-color:#3e64de;\n    color:#fff;\n    align-items: center;\n    font-weight: 400;\n    line-height: 1.4;\n    text-align: center;\n    text-decoration: none;\n    vertical-align: middle;\n    user-select: none;\n    border-radius: 6px;\n    transition: color 200ms ease-in-out,background-color 200ms ease-in-out,border-color 200ms ease-in-out;",constraints:{parents:[{element:Ge+"-retake-and-certificate",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=e.properties.contents,a=Qe.i.getCanvasElement(n);return $e.createElement("a",ga({},Qe.i.getAllAttributes(a),{ref:t,className:r}),i({template:[["text",{properties:{contents:o}}]]}))})),controls:{margin:!0,padding:!1}};function ba(){return ba=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ba.apply(this,arguments)}const ha=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",ba({},Qe.i.getAllAttributes(o),{ref:t,className:r,style:{maxWidth:"100%"}}),i({template:[[Ge+"-rating-empty",{properties:{}}],[Ge+"-rating-not-empty",{properties:{}}],[Ge+"-review-add-edit",{properties:{}}]]}))}));function va(e){return va="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},va(e)}function Oa(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function wa(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Oa(Object(n),!0).forEach((function(t){Sa(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Oa(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Sa(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==va(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==va(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===va(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ea=Qe.i.elementSettings,ja=[{key:"state",label:"Show State",setting:wa(wa({},Ea.SELECT),{},{options:[{value:"empty",title:"Empty"},{value:"not-empty",title:"Not Empty"}]})}],Ca=[{key:"placeholder",label:"Placeholder",setting:wa({},Ea.INPUT)}],Aa=function(e){var t=Qe.i.getCanvasElement(e).properties.settings;return(null==t?void 0:t.state)||"empty"};const La={name:Ge+"-rating",type:"element",source:Ge,className:"",title:"Rating Details",description:"Rating details",icon:"".concat(Qe.i.iconPrefix,"-inline-rating-line"),hoverIcon:"".concat(Qe.i.iconPrefix,"-inline-rating-fill"),category:"Tutor LMS",properties:{tag:"div",settings:{state:"not-empty"}},children:[],defaultStyle:"display:flex;flex-direction:column;gap:8px;",constraints:{childrens:[{element:Ge+"-rating-empty",condition:"ALLOW"},{element:Ge+"-rating-not-empty",condition:"ALLOW"},{element:Ge+"-review-add-edit",condition:"ALLOW"},{element:Ge+"-review-text-input",condition:"ALLOW"},{element:Ge+"-rating-inline",condition:"ALLOW"},{element:Ge+"-rating-active-icon",condition:"ALLOW"},{element:Ge+"-rating-inactive-icon",condition:"ALLOW"},{element:Ge+"-rating-avarage-count",condition:"ALLOW"},{element:Ge+"-rating-total-count",condition:"ALLOW"},{element:Ge+"-rating-summery",condition:"ALLOW"},{element:Ge+"-rating-summery-item",condition:"ALLOW"},{element:Ge+"-rating-summery-item-index",condition:"ALLOW"},{element:Ge+"-rating-summery-item-progress",condition:"ALLOW"},{element:Ge+"-review",condition:"ALLOW"}]},Component:ha,controls:{margin:!0,padding:!1,height:!1,width:!0},settings:ja};function xa(e){return xa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xa(e)}function Pa(){return Pa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Pa.apply(this,arguments)}var Ra=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",Pa({},Qe.i.getAllAttributes(o),{ref:t,className:r,"data-element_hide":"empty"!==Aa(o.parentId)}),i({template:[["text",{properties:{contents:["No Review Yet"]}}]]}))}));const Ma={name:Ge+"-rating-empty",type:"element",source:Ge,className:"",title:"Rating Empty State",description:"Rating empty state",visibility:!1,properties:{tag:"div",attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==xa(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==xa(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===xa(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","empty")},children:[],constraints:{parents:[{element:Ge+"-rating",condition:"ALLOW"}]},Component:Ra,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function Na(e){return Na="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Na(e)}function Za(){return Za=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Za.apply(this,arguments)}var Ta=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",Za({},Qe.i.getAllAttributes(o),{ref:t,className:r,"data-element_hide":"not-empty"!==Aa(o.parentId)}),i({template:[["text",{properties:{contents:["Rating details"]}}],[Ge+"-rating-inline",{properties:{contents:["rating details"]}}],[Ge+"-rating-summery",{properties:{}}],[Ge+"-review",{properties:{}}]]}))}));const Ia={name:Ge+"-rating-not-empty",type:"element",source:Ge,className:"",title:"Rating Not Empty State",description:"Rating not empty state",visibility:!1,properties:{tag:"div",attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Na(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Na(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Na(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","not-empty")},children:[],constraints:{parents:[{element:Ge+"-rating",condition:"ALLOW"}],childrens:[{element:"*",condition:"ALLOW"}]},Component:Ta,controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function ka(){return ka=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ka.apply(this,arguments)}const Da={name:Ge+"-review",type:"element",source:Ge,className:"",title:"Review",description:"Review",visibility:!1,properties:{tag:"div"},children:[],constraints:{parents:[{element:Ge+"-rating-not-empty",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",ka({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[["collection",{style:"text-align: left;",properties:{dynamicContent:{collectionType:"comments",commentType:"tutor_course_rating",items:3,pageNo:1,pagination:!0,filters:[{type:"date"}],sorting:{type:"heading",value:"A-Z"}}}},[["comments",{properties:{componentType:"comments"}},[["comment-item",{properties:{componentType:"comment"}}]]]]]]}))})),controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function _a(){return _a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},_a.apply(this,arguments)}const Wa={name:Ge+"-rating-summery",type:"element",source:Ge,className:"",title:"Rating Summery",description:"Rating summery",visibility:!1,children:[],properties:{tag:"div"},constraints:{parents:[{element:Ge+"-rating-not-empty",condition:"ALLOW"}],childrens:[{element:Ge+"-rating-summery-item",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",_a({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[[Ge+"-rating-summery-item",{properties:{}}]]}))})),controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function Fa(e){return Fa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Fa(e)}function za(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Fa(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Fa(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Fa(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ua(){return Ua=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ua.apply(this,arguments)}var Ba=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement($e.Fragment,null,[1,2,3,4,5].map((function(e){return $e.createElement("div",Ua({},Qe.i.getAllAttributes(o),{ref:t,className:r,key:e}),i({template:[["svg",{properties:{svgOuterHtml:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M16.9781 10.3524C16.9134 10.1429 16.7197 10 16.5004 10 16.2811 10 16.0874 10.1429 16.0227 10.3524L14.7151 14.5838H10.5C10.2856 14.5838 10.0951 14.7205 10.0264 14.9236 9.95769 15.1267 10.0261 15.351 10.1965 15.4811L13.6278 18.1024 12.3143 22.3529C12.25 22.5607 12.3275 22.7862 12.5059 22.9106 12.6843 23.0351 12.9227 23.0299 13.0955 22.8979L16.5004 20.2968 19.9053 22.8979C20.0781 23.0299 20.3165 23.0351 20.4949 22.9106 20.6733 22.7862 20.7508 22.5607 20.6865 22.3529L19.373 18.1024 22.8043 15.4811C22.9747 15.351 23.0431 15.1267 22.9744 14.9236 22.9057 14.7205 22.7152 14.5838 22.5008 14.5838H18.2857L16.9781 10.3524Z"></path></svg>'},style:"height:50px;width:50px;min-width:auto;min-height:auto;"}],[Ge+"-rating-summery-item-index",{properties:{}}],["div",{properties:{},style:"width:400px;height:8px;background:#e3e5eb;overflow:hidden;border-radius:10px;",title:"Rating Summery Progress Background"},[[Ge+"-rating-summery-item-progress",{properties:{}}]]],[Ge+"-rating-total-count",{properties:{}}]]}))})))}));const Ha=za(za(za(za({name:Ge+"-rating-summery-item",type:"element",source:Ge,className:"",title:"Rating summery item",description:"Rating summery item",visibility:!1,children:[],properties:{tag:"div"},defaultStyle:"display:flex;align-items:center;gap:8px"},"children",[]),"constraints",{parents:[{element:Ge+"-rating-summery",condition:"ALLOW"}],childrens:[{element:"*",condition:"ALLOW"}]}),"Component",Ba),"controls",{margin:!0,padding:!1,height:!1,width:!0});function qa(){return qa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},qa.apply(this,arguments)}const Va={name:Ge+"-rating-summery-item-index",type:"element",source:Ge,className:"",title:"Rating summery item index",description:"Rating summery item index",visibility:!1,properties:{tag:"div"},constraints:{parents:[{element:Ge+"-rating-summery-item",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("div",qa({},Qe.i.getAllAttributes(i),{ref:t,className:r}),"5")})),controls:{margin:!1,padding:!1,height:!1,width:!1}};function Qa(){return Qa=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Qa.apply(this,arguments)}const Ga={name:Ge+"-rating-summery-item-progress",type:"element",source:Ge,className:"",title:"Rating summery item progress",description:"Rating summery item progress",visibility:!1,properties:{tag:"div"},defaultStyle:"width:40%;height:8px;background:#ed9700;",constraints:{parents:[{element:Ge+"-rating-summery-item",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("div",Qa({},Qe.i.getAllAttributes(i),{ref:t,className:r}))})),controls:{margin:!1,padding:!1,height:!1,width:!1}};function $a(){return $a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$a.apply(this,arguments)}const Ja=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",$a({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[["div",{properties:{},style:"display:flex;align-items:center;gap:8px;"},[["div",{properties:{},style:"display:flex;"},[[Ge+"-rating-active-icon",{properties:{}}],[Ge+"-rating-inactive-icon",{properties:{}}]]],[Ge+"-rating-avarage-count",{properties:{}}],[Ge+"-rating-total-count",{properties:{}}]]]]}))}));function Ka(e){return Ka="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ka(e)}function Xa(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ya(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Xa(Object(n),!0).forEach((function(t){es(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Xa(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function es(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Ka(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Ka(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Ka(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ts=Qe.i.elementSettings,ns=[{key:"template",label:"Template",setting:Ya(Ya({},ts.INPUT),{},{placeholder:"({{count}} Ratings)"})}],rs=[{key:"template",label:"Template",setting:Ya(Ya({},ts.INPUT),{},{placeholder:"{{avarage}}"})}];const is={name:Ge+"-rating-inline",type:"element",source:Ge,className:"",title:"Rating Inline",description:"Rating Inline",icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{tag:"div"},children:[],defaultStyle:"display:flex;align-items:center;justify-content:center;width:max-content;gap:8px;",constraints:{},Component:Ja,controls:{margin:!0,padding:!1,height:!1,width:!0},settings:[],manualDelete:!1};function os(){return os=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},os.apply(this,arguments)}const as={name:Ge+"-rating-active-icon",type:"element",source:Ge,className:"",title:"Rating Active Icon",description:"Rating Active Icon",visibility:!1,properties:{tag:"div"},children:[],defaultStyle:"display:flex;align-items:center;justify-content:center;width:max-content;gap:2px;",constraints:{ancestors:[{element:Ge+"-rating-inline",condition:"ALLOW"},{element:Ge+"-review-add-edit",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement($e.Fragment,null,[1,2,3].map((function(e){return $e.createElement("div",os({},Qe.i.getAllAttributes(o),{ref:t,className:r,key:e}),(1===e&&void 0===o.template_mounted||o.template_mounted)&&i({template:[["svg",{properties:{svgOuterHtml:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M16.9781 10.3524C16.9134 10.1429 16.7197 10 16.5004 10 16.2811 10 16.0874 10.1429 16.0227 10.3524L14.7151 14.5838H10.5C10.2856 14.5838 10.0951 14.7205 10.0264 14.9236 9.95769 15.1267 10.0261 15.351 10.1965 15.4811L13.6278 18.1024 12.3143 22.3529C12.25 22.5607 12.3275 22.7862 12.5059 22.9106 12.6843 23.0351 12.9227 23.0299 13.0955 22.8979L16.5004 20.2968 19.9053 22.8979C20.0781 23.0299 20.3165 23.0351 20.4949 22.9106 20.6733 22.7862 20.7508 22.5607 20.6865 22.3529L19.373 18.1024 22.8043 15.4811C22.9747 15.351 23.0431 15.1267 22.9744 14.9236 22.9057 14.7205 22.7152 14.5838 22.5008 14.5838H18.2857L16.9781 10.3524Z"></path></svg>'},style:"height:50px;width:50px;min-width:auto;min-height:auto;"}]]}))})))})),controls:{margin:!0,padding:!1,height:!1,width:!0}};function ss(){return ss=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ss.apply(this,arguments)}const ls={name:Ge+"-rating-inactive-icon",type:"element",source:Ge,className:"",title:"Rating Inactive Icon",description:"Rating Inactive Icon",visibility:!1,properties:{tag:"div"},children:[],defaultStyle:"display:flex;align-items:center;justify-content:center;width:max-content;gap:2px;",constraints:{ancestors:[{element:Ge+"-rating-inline",condition:"ALLOW"},{element:Ge+"-review-add-edit",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement($e.Fragment,null,[1,2].map((function(e){return $e.createElement("div",ss({},Qe.i.getAllAttributes(o),{ref:t,className:r,key:e}),(1===e&&void 0===o.template_mounted||o.template_mounted)&&i({template:[["svg",{properties:{svgOuterHtml:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><path d="M16.5 10C16.7107 10 16.8975 10.1421 16.9626 10.352L18.273 14.5764H22.5136C22.7243 14.5764 22.911 14.7185 22.9762 14.9284 23.0413 15.1383 22.9699 15.3683 22.7995 15.498L19.3688 18.1088 20.6792 22.3331C20.7443 22.543 20.673 22.773 20.5025 22.9027 20.332 23.0324 20.1012 23.0324 19.9307 22.9027L16.5 20.2919 13.0693 22.9027C12.8988 23.0324 12.668 23.0324 12.4975 22.9027 12.327 22.773 12.2557 22.543 12.3208 22.3331L13.6312 18.1088 10.2005 15.498C10.0301 15.3683 9.95872 15.1383 10.0238 14.9284 10.089 14.7185 10.2757 14.5764 10.4864 14.5764H14.727L16.0374 10.352C16.1025 10.1421 16.2893 10 16.5 10ZM16.5 12.1581L15.543 15.2433C15.4779 15.4532 15.2911 15.5953 15.0804 15.5953H11.9834L14.4889 17.502C14.6594 17.6317 14.7307 17.8617 14.6656 18.0716L13.7086 21.1568 16.2141 19.25C16.3846 19.1203 16.6154 19.1203 16.7859 19.25L19.2914 21.1568 18.3344 18.0716C18.2693 17.8617 18.3406 17.6317 18.5111 17.502L21.0166 15.5953H17.9196C17.7089 15.5953 17.5221 15.4532 17.457 15.2433L16.5 12.1581Z"></path></svg>'},style:"height:50px;width:50px;min-width:auto;min-height:auto;"}]]}))})))})),controls:{margin:!0,padding:!1,height:!1,width:!0}};function cs(){return cs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},cs.apply(this,arguments)}var us=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("span",cs({},Qe.i.getAllAttributes(i),{ref:t,className:r}),function(){var e=i.properties.settings;return((null==e?void 0:e.template)||"{{avarage}}").replace("{{avarage}}","4.5")}())}));const ps={name:Ge+"-rating-avarage-count",type:"element",source:Ge,className:"",title:"Rating Avarage Count",description:"Rating Avarage Count",visibility:!1,properties:{tag:"span",settings:{template:"{{avarage}}"}},constraints:{ancestors:[{element:Ge+"-rating-inline",condition:"ALLOW"}]},Component:us,controls:{margin:!1,padding:!1,height:!1,width:!1},settings:rs};function ms(){return ms=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},ms.apply(this,arguments)}const fs={name:Ge+"-rating-total-count",type:"element",source:Ge,className:"",title:"Rating Total Count",description:"Rating Total Count",visibility:!1,properties:{tag:"span",settings:{template:"({{count}} Ratings)"}},constraints:{ancestors:[{element:Ge+"-rating-inline",condition:"ALLOW"},{element:Ge+"-rating-summery-item",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("span",ms({},Qe.i.getAllAttributes(i),{ref:t,className:r}),i.properties.settings.template.replace("{{count}}","3"))})),controls:{margin:!1,padding:!1,height:!1,width:!1},settings:ns};function ds(e){return ds="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ds(e)}function gs(){return gs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},gs.apply(this,arguments)}function ys(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==ds(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==ds(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===ds(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const bs={name:Ge+"-review-add-edit",type:"element",source:Ge,className:"",title:"Review Add Edit",description:"Review Add Edit",visibility:!1,properties:{tag:"div"},defaultStyle:"display:flex;flex-direction:column;row-gap:16px;",children:[],constraints:{ancestors:[{element:Ge+"-rating",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",gs({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[["div",{properties:{},style:"display:flex;"},[[Ge+"-rating-active-icon",{properties:{}}],[Ge+"-rating-inactive-icon",{properties:{}}]]],[Ge+"-review-text-input",{properties:{}}],["button",{properties:{tag:"button",contents:["Submit review"],attributes:ys({},Ge+"-reivew-btn","submit")},style:"border-radius:0;text-align:center;"}]]}))})),controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function hs(){return hs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},hs.apply(this,arguments)}const vs={name:Ge+"-review-text-input",type:"element",source:Ge,className:"",title:"Review input",description:"Review input",visibility:!1,category:"Tutor LMS",properties:{tag:"textarea",settings:{placeholder:"Enter your review"}},defaultStyle:"display:block;width:100%;height:150px;padding:8px;",constraints:{ancestors:[{element:Ge+"-review-add-edit",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("textarea",hs({},Qe.i.getAllAttributes(i),{ref:t,className:r,defaultValue:"Review Text"}))})),controls:{margin:!0,padding:!1,height:!1,width:!0},settings:Ca};function Os(e){return Os="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Os(e)}function ws(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ss(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ws(Object(n),!0).forEach((function(t){Es(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ws(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Es(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Os(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Os(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Os(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var js=[{key:"state",label:"Show State",setting:Ss(Ss({},Qe.i.elementSettings.SELECT),{},{options:[{value:"no-announcements",title:"No Announcements"},{value:"has-announcements",title:"Has Announcements"}]})}];function Cs(){return Cs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Cs.apply(this,arguments)}var As=function(e){var t=Qe.i.getCanvasElement(e).properties.settings;return(null==t?void 0:t.state)||"no-announcements"},Ls=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",Cs({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[[Ge+"-no-announcements",{properties:{}}],[Ge+"-has-announcements",{properties:{}}]]}))}));const xs={name:Ge+"-announcements",type:"element",source:Ge,className:"",title:"Announcements",description:"Announcements",icon:"".concat(Qe.i.iconPrefix,"-announcement-line"),hoverIcon:"".concat(Qe.i.iconPrefix,"-announcement-fill"),category:"Tutor LMS",properties:{tag:"div",settings:{state:"no-announcements"}},children:[],defaultStyle:"margin: 0 auto;",constraints:{childrens:[{element:Ge+"-has-announcements",condition:"ALLOW"},{element:Ge+"-no-announcements",condition:"ALLOW"}]},Component:Ls,controls:{margin:!0,padding:!1,height:!1,width:!0},settings:js};function Ps(e){return Ps="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ps(e)}function Rs(){return Rs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Rs.apply(this,arguments)}var Ms=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=(e.properties.contents,Qe.i.getCanvasElement(n));return $e.createElement("div",Rs({},Qe.i.getAllAttributes(o),{ref:t,className:r,"data-element_hide":"has-announcements"!==As(o.parentId)}),i({template:[["collection",{style:"text-align: left;",properties:{dynamicContent:{collectionType:"posts",type:"tutor_announcements",inherit:!0,items:3,pageNo:1,pagination:!0,filters:[{type:"date"}],sorting:{type:"heading",value:"A-Z"}}}},[["collection-wrapper",{properties:{componentType:"collection"}},[["collection-item",{style:"background-color: #fff;\n              border: 1px solid #cdcfd5;\n              position: relative;\n              border-radius: 6px;\n              display: flex;\n              flex-direction: column;\n              margin-bottom: 32px;\n              "},[["div",{style:"background-color: #eff1f6;\n                      border-bottom: 1px solid #cdcfd5;\n                      padding: 16px 20px;\n                      border-top-left-radius: 5px;\n                      border-top-right-radius: 5px;\n                      ",properties:{}},[["heading",{style:"font-size: 16px;\n                        font-weight: 500;\n                        margin: 0;\n                        ",properties:{dynamicContent:{type:"post",value:"post_title"}}}],["div",{style:"display: flex;\n                          column-gap: 16px;\n                          align-items: center;\n                          margin-top: 16px;\n                          font-size: 14px;",properties:{}},[["image",{properties:{dynamicContent:{type:"author",value:"author_profile_picture"}}}],["div",{properties:{}},[["text",{properties:{contents:["By "]}}],["text",{style:"color: #212327;",properties:{dynamicContent:{type:"post",value:"post_author"}}}]]],["text",{style:"color: #757c8e;",properties:{dynamicContent:{type:"post",value:"post_date"}}}]]]]],["paragraph",{style:"padding: 20px;",properties:{dynamicContent:{type:"post",value:"post_content"}}}]]]]]]]]}))}));const Ns={name:Ge+"-has-announcements",type:"element",source:Ge,className:"",title:"Has Announcements",description:"Has Announcements",visibility:!1,properties:{tag:"div",attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Ps(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Ps(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Ps(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","has-announcements")},children:[],constraints:{parents:[{element:Ge+"-announcements",condition:"ALLOW"}],childrens:[{element:"*",condition:"ALLOW"}]},Component:Ms,controls:{margin:!0,padding:!1},manualDelete:!1};function Zs(e){return Zs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Zs(e)}function Ts(){return Ts=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ts.apply(this,arguments)}var Is=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=e.properties.contents,a=Qe.i.getCanvasElement(n);return $e.createElement("div",Ts({},Qe.i.getAllAttributes(a),{ref:t,className:r,"data-element_hide":"no-announcements"!==As(a.parentId)}),i({template:[["svg",{style:"height: auto;",properties:{svgOuterHtml:'<svg viewBox="0 0 824 242" fill="none" style="height: auto;" xmlns="http://www.w3.org/2000/svg">\n            <path d="M0 241.266s369.993-203.2255 824-.957l-824 .957Z" fill="url(#a)"/>\n            <path d="M281.32 167.026v20.072" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>\n            <path d="M291.09 156.888c-6.818-23.128-9.617-25.522-9.617-25.522s-2.137 2.547-9.567 25.522c-6.513 20.021 4.987 20.123 8.091 19.817-.051 0 17.708 2.649 11.093-19.817Z" fill="#E5E7EA"/>\n            <path d="M502.926 152.1v20.785" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>\n            <path d="M513.002 141.605c-7.074-23.943-9.974-26.388-9.974-26.388s-2.188 2.649-9.923 26.388c-6.717 20.734 5.191 20.836 8.346 20.53.05 0 18.42 2.7 11.551-20.53Z" fill="#E5E7EA"/>\n            <path d="M534.322 167.077v15.793" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>\n            <path d="M542.006 159.13c-5.394-18.187-7.531-20.072-7.531-20.072s-1.679 1.987-7.531 20.072c-5.089 15.69 3.918 15.792 6.361 15.588-.051 0 13.891 2.038 8.701-15.588Z" fill="#E5E7EA"/>\n            <path d="m349.761 45.1695 100.397-8.9151s24.272 1.1717 26.868 31.0754c2.595 29.9038 5.037 47.3262 5.037 47.3262l-92.153 11.106-5.954-50.3322c-.763-6.5208-3.256-12.6849-7.327-17.8302-4.987-6.266-13.281-12.7358-26.868-12.4301Z" fill="#E3E5EA"/>\n            <path d="m392.963 125.202-61.928 7.03-6.259-52.2679c-2.086-17.4226 10.534-33.1133 27.987-34.8453 16.894-1.6302 32.007 10.5453 33.992 27.4585l6.208 52.6247ZM404.616 123.469v18.391h16.385l-.204-19.868-16.181 1.477Z" fill="#C1C3CA"/>\n            <path d="m330.374 132.792-7.887 26.185c-1.221 3.617-1.73 7.438-1.476 11.258.509 7.183 3.918 16.251 18.064 15.895 8.753-.255 16.182-3.515 22.085-7.438 8.243-5.502 14.604-13.347 18.573-22.415l13.739-30.464-63.098 6.979Z" fill="#D0D2D6"/>\n            <path d="M422.324 140.994h-18.777v43.149h18.777v-43.149Z" fill="#CDCFD4"/>\n            <path d="m381.768 60.045-54.447 39.0227M382.989 60.8607l-51.75 70.6073M360.396 128.666l22.441-65.2582M350.27 82.7662s11.042-3.0058 10.991 6.2659c0 0 10.635-2.3942 10.025 8.253M335.615 93.5153s13.994-3.0056 14.248 11.6657c0 0 11.195-4.126 15.52 7.744" stroke="#CDCFD4" stroke-width="3" stroke-miterlimit="10"/>\n            <path fill-rule="evenodd" clip-rule="evenodd" d="m273.789 22.3978-.814-.051v1.3246l.814.0509v-1.3245ZM273.942 20.0038l-.814-.0509-.102 1.3244.814.051.102-1.3245ZM274.044 17.6093l-.814-.0508-.051 1.3244.814.051.051-1.3246ZM273.84 29.5302l-.814.0509c.051.4584.051.917.102 1.3245l.814-.051c-.102-.4075-.102-.8659-.102-1.3244ZM273.738 27.1358h-.814c0 .4584 0 .917.051 1.3245h.814c0-.4075-.051-.8661-.051-1.3245ZM273.738 24.7925h-.814v1.3245h.814v-1.3245ZM274.705 36.611l-.763.2037.305 1.3246.763-.2037-.305-1.3246ZM274.298 34.2678l-.814.102c.051.4585.153.9169.203 1.3244l.815-.1528c-.102-.3566-.153-.8151-.204-1.2736ZM273.993 31.9246l-.814.1019c.051.4585.101.917.152 1.3245l.815-.1019c-.051-.4585-.102-.866-.153-1.3245ZM276.995 43.3356l-.712.3566c.203.4076.407.8152.61 1.2227l.713-.4075c-.255-.3566-.407-.7642-.611-1.1718ZM276.079 41.1959l-.763.2547.509 1.2736.712-.3057-.458-1.2226ZM275.316 38.9547l-.763.2547c.102.4585.254.8661.407 1.2736l.763-.2547c-.153-.4585-.305-.8661-.407-1.2736ZM280.863 49.3468l-.662.5095.916 1.0188.611-.5604-.865-.9679ZM279.387 47.4623l-.662.4585c.255.3566.56.7132.814 1.0698l.662-.5094c-.255-.3057-.56-.6623-.814-1.0189ZM278.115 45.4754l-.713.4075c.204.4076.458.7642.713 1.1718l.661-.4585c-.203-.3566-.458-.7642-.661-1.1208ZM286.002 54.2377l-.509.6622c.356.3056.763.5094 1.12.7641l.458-.6621c-.357-.2548-.713-.5095-1.069-.7642ZM284.17 52.7094l-.56.6113c.357.3057.662.6113 1.018.866l.509-.6113c-.305-.2547-.662-.5603-.967-.866ZM282.44 51.0791l-.611.5604c.305.3057.611.6622.967.9679l.56-.6113c-.306-.2547-.611-.6113-.916-.917ZM292.159 57.7525l-.305.7641c.407.1529.814.3566 1.272.5095l.305-.7642c-.458-.1528-.865-.3056-1.272-.5094ZM290.022 56.7336l-.407.7642 1.221.6113.356-.7642-1.17-.6113ZM287.986 55.562l-.458.7133 1.171.6622.407-.6622-1.12-.7133ZM298.978 59.7395l-.102.8151c.458.051.916.1529 1.374.1529l.051-.8152c-.458-.0509-.916-.1019-1.323-.1528ZM296.637 59.2808l-.203.8152 1.373.2547.153-.8152-1.323-.2547ZM294.398 58.6186l-.254.7642c.407.1528.865.2547 1.272.4075l.203-.7641c-.407-.1528-.814-.2547-1.221-.4076ZM306 59.5355l.153.8152c.458-.1019.916-.2038 1.323-.3057l-.204-.7642c-.407.051-.814.1529-1.272.2547ZM303.659 59.8923l.051.8152c.458-.051.916-.1019 1.374-.1529l-.102-.8151c-.407.0509-.865.1019-1.323.1528ZM301.318 59.9435v.815c.458 0 .916.0509 1.374 0v-.815c-.458.0509-.916 0-1.374 0ZM312.36 56.6318l.509.6113c.356-.3057.662-.6622 1.018-.9679l-.611-.5604c-.305.3056-.61.6113-.916.917ZM310.427 57.9565l.407.7133c.407-.2548.763-.5096 1.17-.7133l-.458-.6622c-.407.2038-.763.4584-1.119.6622ZM308.29 58.9246l.254.7641c.407-.1528.865-.3566 1.272-.5094l-.356-.7642c-.356.1529-.763.3566-1.17.5095ZM315.566 50.6717l.814.0508c.051-.4585 0-.9679-.051-1.4264l-.814.102c.102.4075.102.866.051 1.2736ZM315.058 52.9131l.763.3057c.203-.4585.305-.9171.407-1.3755l-.814-.1528c-.051.4076-.204.8151-.356 1.2226ZM313.938 54.8997l.662.4586c.254-.4075.508-.7642.763-1.1717l-.713-.4076c-.254.4076-.458.7641-.712 1.1207ZM311.953 45.6282l.204-.815c-.458-.1019-.916-.2038-1.425-.2548l-.051.8151c.458.1018.865.1528 1.272.2547ZM313.989 46.6468l.508-.6113c-.407-.3056-.814-.5604-1.272-.7641l-.356.7641c.407.1528.763.3566 1.12.6113ZM315.312 48.4301l.763-.3056c-.153-.4585-.407-.917-.712-1.2736l-.611.5095c.203.3056.407.6622.56 1.0697ZM305.287 46.8508l-.407-.7133c-.203.1019-.407.2547-.559.4076-.204.1528-.408.2547-.56.4075l.509.6623c.152-.1528.356-.2547.508-.4075.153-.1529.306-.2547.509-.3566ZM307.425 45.8321l-.255-.7642c-.254.051-.458.1528-.661.2547-.204.1019-.407.2037-.662.3056l.356.7133c.204-.1019.407-.2038.611-.2547.204-.1019.356-.2038.611-.2547ZM309.664 45.4244l-.051-.8152c-.255 0-.458.051-.713.051-.254 0-.458.0509-.712.1019l.153.8151c.203-.0509.407-.102.61-.102.204 0 .509-.0508.713-.0508ZM300.758 52.047l-.763-.3057c-.203.4076-.356.866-.509 1.2736l.764.2547c.203-.4075.356-.815.508-1.2226ZM301.929 50.0094l-.662-.4586c-.152.2038-.254.4076-.407.5605-.102.2037-.254.4075-.356.6113l.712.4075c.102-.2038.204-.3566.357-.5604.101-.2038.203-.3565.356-.5603ZM303.456 48.2771l-.56-.6113c-.153.1529-.356.3057-.509.5095-.152.1528-.305.3566-.458.5094l.611.5094c.152-.1528.254-.3565.407-.4584.203-.1528.356-.3057.509-.4586Z" fill="#C1C3CA"/>\n            <path fill-rule="evenodd" clip-rule="evenodd" d="m299.588 58.9753-.814.051c0 .4585.051.917.102 1.3755l.814-.1019c-.051-.4585-.102-.917-.102-1.3246ZM299.639 56.5808l-.814-.1019c-.051.4585-.051.917-.102 1.3754h.814c.051-.4075.051-.8151.102-1.2735ZM299.995 54.2887l-.763-.2037c-.102.4585-.204.9169-.254 1.3754l.814.1019c.051-.4075.101-.866.203-1.2736ZM300.962 65.9034l-.763.2547c.152.4076.305.8661.458 1.2736l.763-.3057c-.153-.4075-.305-.815-.458-1.2226ZM300.25 63.611l-.764.2037c.102.4585.255.8661.357 1.3246l.763-.2548c-.102-.4075-.255-.815-.356-1.2735ZM299.792 61.3185l-.814.1019c.05.4585.152.917.254 1.3246l.814-.1529c-.102-.4075-.203-.8151-.254-1.2736ZM304.168 72.2206l-.662.4586.764 1.1207.661-.5094-.763-1.0699ZM302.947 70.1829l-.713.4076.662 1.1717.712-.4076-.661-1.1717ZM301.878 68.0944l-.763.3566c.203.4075.356.815.559 1.2226l.713-.3566-.509-1.2226ZM308.849 77.5696l-.559.6115 1.017.9168.509-.6113-.967-.917ZM307.17 75.9394l-.61.5604.916.9679.61-.6113-.916-.917ZM305.593 74.1564l-.611.5094.865 1.0189.611-.5095-.865-1.0188ZM314.599 81.7471l-.407.7132 1.171.6623.407-.7133-1.171-.6622ZM312.564 80.5243l-.458.6623 1.17.7641.407-.7131-1.119-.7133ZM310.681 79.1487l-.509.6113 1.069.8151.458-.6622-1.018-.7642ZM321.113 84.4978l-.204.8151 1.323.3566.153-.8152-1.272-.3565ZM318.874 83.7849l-.254.7641 1.272.4584.254-.8151-1.272-.4074ZM316.686 82.868l-.306.7131 1.222.5605.305-.7642-1.221-.5094ZM281.371 8.49081s-.712 4.17739 3.257 4.83959c0 0 2.392-1.732 1.272-4.07544-1.068-2.29245-4.529-.76415-4.529-.76415Z" fill="#C1C3CA"/>\n            <path fill-rule="evenodd" clip-rule="evenodd" d="M280.812 8.79593s-.407 4.33027 3.409 4.94157c0 0-1.628 1.834-3.155.7642-1.425-.9679-2.442-2.8019-2.086-4.4831.101-.40751.305-.8151.661-1.06982.306-.10189.713-.25474 1.171-.15285Z" fill="#C1C3CA"/>\n            <path fill-rule="evenodd" clip-rule="evenodd" d="M278.115 10.3243c0-.1018-1.985 2.1397-1.527 3.7699 0 0-1.17.6113-1.119 1.3245 0 0 .865-.866 1.628-.4585 0 0 1.527 1.7321 3.918.4585.051-.051-2.697-1.5793-2.9-5.0944ZM281.117 7.77717c-1.221-.96792-2.137-2.2924-2.697-3.76976-.153-.35661-.254-.71318-.254-1.12073-.051-.61132.152-1.22264.509-1.78302.254-.407546.508-.764224.966-.967998.865-.407547 1.985.15283 2.443 1.018868.458.86604.407 1.88495.254 2.85288-.152 1.12075-.458 2.2925-.865 3.36231-.102.20378-.203.40745-.356.40745-.153.05094-.305-.10184-.458-.20372-1.425-1.47736-3.358-2.29241-5.241-3.00561-.407-.15283-.865-.30576-1.272-.35671-.509 0-.967.15293-1.425.35671-.611.30566-1.17.76405-1.425 1.42631-.254.66226-.102 1.42647.407 1.83401.306.25472.713.30569 1.12.40757 2.697.4585 5.444.35663 8.091-.35657" fill="#C1C3CA"/>\n            <path d="m392.352 124.998-61.266 6.979-6.208-51.7582c-2.086-17.2189 10.432-32.8076 27.682-34.4378 16.741-1.6302 31.701 10.4434 33.635 27.1529l6.157 52.0641Z" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>\n            <path d="M457.995 24.5374 474.329 36.56c.661.4584.458 1.5283-.356 1.7321l-6.565 1.5283.56 6.8774c.051.866-.916 1.3754-1.577.815l-16.487-14.4169 8.091-8.5585Z" fill="#3E64DE"/>\n            <path d="M443.492 60.0443c1.996 0 3.613-1.6422 3.613-3.6679 0-2.0258-1.617-3.6679-3.613-3.6679-1.995 0-3.613 1.6421-3.613 3.6679 0 2.0257 1.618 3.6679 3.613 3.6679Z" fill="#949BA9"/>\n            <path d="M444.815 54.5424c.611-3.0566 1.222-6.1132 1.832-9.1698.407-2.1397.865-4.2793 1.272-6.4698.204-.917.713-2.2925.611-3.2095.051.6623-.814 1.019.102.2039.509-.4585 1.017-1.0189 1.475-1.5283 1.527-1.5283 3.054-3.1076 4.529-4.6359 2.188-2.2415 4.427-4.534 6.615-6.7755 1.629-1.6302-.916-4.1773-2.493-2.4962-2.341 2.3943-4.631 4.7377-6.971 7.1321-1.476 1.5283-2.952 3.0565-4.478 4.5339-.713.7132-1.73 1.4774-2.188 2.4453-.407.917-.458 2.1396-.662 3.0566-.407 2.0887-.814 4.2282-1.272 6.3169-.661 3.2095-1.272 6.4698-1.934 9.6793-.305 2.1905 3.155 3.1585 3.562.917Z" fill="#949BA9"/>\n            <path d="M558.035 22.0415c2.952 0 2.952-4.5849 0-4.5849-2.951 0-2.951 4.5849 0 4.5849Z" fill="url(#b)"/>\n            <defs>\n            <linearGradient id="a" x1="406.358" y1="69.2193" x2="408.386" y2="355.63" gradientUnits="userSpaceOnUse">\n                <stop offset=".1653" stop-color="#E3E5EA"/>\n                <stop offset=".3741" stop-color="#F4F5F7" stop-opacity=".6199"/>\n                <stop offset=".5962" stop-color="#F6F7F8" stop-opacity="0"/>\n            </linearGradient>\n            <linearGradient id="b" x1="558.006" y1="22.0271" x2="558.065" y2="17.4425" gradientUnits="userSpaceOnUse">\n                <stop offset=".1653" stop-color="#E3E5EA"/>\n                <stop offset=".2826" stop-color="#EDEFF1" stop-opacity=".8497"/>\n                <stop offset=".4662" stop-color="#F4F5F7" stop-opacity=".6143"/>\n                <stop offset=".9455" stop-color="#F6F7F8" stop-opacity="0"/>\n            </linearGradient>\n            </defs>\n        </svg>'}}],["text",{properties:{contents:o},style:"color: #41454F;"}]]}))}));const ks={name:Ge+"-no-announcements",type:"element",source:Ge,className:"",title:"No Announcements",description:"No Announcements",visibility:!1,properties:{tag:"div",contents:["No Data Available in this Section"],attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Zs(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Zs(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Zs(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","no-announcements")},children:[],defaultStyle:"padding: 32px; text-align: center;",constraints:{parents:[{element:Ge+"-announcements",condition:"ALLOW"}],childrens:[{element:"*",condition:"ALLOW"}]},Component:Is,controls:{margin:!0,padding:!1},manualDelete:!1};function Ds(){return Ds=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ds.apply(this,arguments)}var _s=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",Ds({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[["heading",{properties:{contents:["Question & Answer"]}}],[Ge+"-qna-editor",{properties:{}}],["div",{},[[Ge+"-question-button",{}]]],[Ge+"-qna-questions",{properties:{}}]]}))}));const Ws={name:Ge+"-q-and-a",type:"element",source:Ge,className:"",title:"Q&A",description:"Q&A",icon:"".concat(Qe.i.iconPrefix,"-qna-line"),hoverIcon:"".concat(Qe.i.iconPrefix,"-qna-fill"),category:"Tutor LMS",properties:{tag:"div"},children:[],defaultStyle:"margin: 0 auto;",constraints:{childrens:[{element:"*",condition:"ALLOW"}]},Component:_s,controls:{margin:!0,padding:!1,height:!1,width:!0}};function Fs(){return Fs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Fs.apply(this,arguments)}var zs=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=e.properties.contents,a=Qe.i.getCanvasElement(n);return $e.createElement("a",Fs({},Qe.i.getAllAttributes(a),{ref:t,className:r}),i({template:[["text",{properties:{contents:o}}]]}))}));const Us={name:Ge+"-question-button",type:"element",source:Ge,className:"",title:"Question Button",description:"Question Button",icon:"".concat(Qe.i.iconPrefix,"-star"),visibility:!1,properties:{tag:"a",contents:["Ask Question"],type:"href",attributes:{href:"",target:""}},defaultStyle:"\n    background-color: rgba(0, 250, 255, 1);\n    color: rgba(35, 35, 35, 1);\n    fill: rgba(35, 35, 35, 1);\n    padding-top: 16px;\n    padding-bottom: 16px;\n    padding-left: 16px;\n    padding-right: 16px;\n    border-top-width: 1px;\n    border-top-style: solid;\n    border-right-width: 1px;\n    border-right-style: solid;\n    border-bottom-width: 1px;\n    border-bottom-style: solid;\n    border-left-width: 1px;\n    border-left-style: solid;\n    border-radius: 16px;\n    border-top-left-radius: 16px;\n    border-bottom-left-radius: 16px;\n    border-top-right-radius: 16px;\n    border-bottom-right-radius: 16px;\n    font-weight: 400;\n    font-style: normal;\n    text-decoration: none;\n    border-top-color: rgba(0, 0, 0, 1);\n    border-right-color: rgba(0, 0, 0, 1);\n    border-bottom-color: rgba(0, 0, 0, 1);\n    border-left-color: rgba(0, 0, 0, 1);\n    display: inline-block;\n  ",children:[],constraints:{ancestors:[{element:Ge+"-q-and-a",condition:"REQUIRED"}]},Component:zs,controls:{margin:!0,padding:!1},manualDelete:!1};function Bs(){return Bs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Bs.apply(this,arguments)}var Hs=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=e.properties.contents,a=Qe.i.getCanvasElement(n);return $e.createElement("a",Bs({},Qe.i.getAllAttributes(a),{ref:t,className:r}),i({template:[["text",{properties:{contents:o}}]]}))}));const qs={name:Ge+"-reply-button",type:"element",source:Ge,className:"",title:"Reply Button",description:"Reply Button",icon:"".concat(Qe.i.iconPrefix,"-star"),visibility:!1,properties:{tag:"a",contents:["Reply"],type:"href",attributes:{href:"",target:""}},defaultStyle:"\n    background-color: rgba(0, 250, 255, 1);\n    color: rgba(35, 35, 35, 1);\n    fill: rgba(35, 35, 35, 1);\n    padding-top: 16px;\n    padding-bottom: 16px;\n    padding-left: 16px;\n    padding-right: 16px;\n    border-top-width: 1px;\n    border-top-style: solid;\n    border-right-width: 1px;\n    border-right-style: solid;\n    border-bottom-width: 1px;\n    border-bottom-style: solid;\n    border-left-width: 1px;\n    border-left-style: solid;\n    border-radius: 16px;\n    border-top-left-radius: 16px;\n    border-bottom-left-radius: 16px;\n    border-top-right-radius: 16px;\n    border-bottom-right-radius: 16px;\n    font-weight: 400;\n    font-style: normal;\n    text-decoration: none;\n    border-top-color: rgba(0, 0, 0, 1);\n    border-right-color: rgba(0, 0, 0, 1);\n    border-bottom-color: rgba(0, 0, 0, 1);\n    border-left-color: rgba(0, 0, 0, 1);\n    display: inline-block;\n  ",children:[],constraints:{ancestors:[{element:Ge+"-qna-questions",condition:"REQUIRED"}]},Component:Hs,controls:{margin:!0,padding:!1},manualDelete:!1};function Vs(){return Vs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Vs.apply(this,arguments)}var Qs=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("div",Vs({},Qe.i.getAllAttributes(i),{ref:t,className:r}),$e.createElement("img",{src:"https://placehold.co/848X175?text=Editor Placeholder"}))}));const Gs={name:Ge+"-qna-editor",type:"element",source:Ge,className:"",title:"Qna editor",description:"Qna editor",icon:"".concat(Qe.i.iconPrefix,"-star"),visibility:!1,properties:{tag:"img"},defaultStyle:"width:100%;",constraints:{ancestors:[{element:Ge+"-q-and-a",condition:"REQUIRED"}]},Component:Qs,controls:{margin:!1,padding:!1,height:!1,width:!1},manualDelete:!1};function $s(){return $s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},$s.apply(this,arguments)}var Js=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=(e.properties.contents,Qe.i.getCanvasElement(n));return $e.createElement("div",$s({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[["collection",{title:"Questions",manualDelete:!1,properties:{attributes:{"data-comment_parent_id":0},dynamicContent:{collectionType:"comments",commentType:"tutor_q_and_a",pagination:!1}}},[["comments",{},[["comment-item",{},[["link-block",{properties:{dynamicContent:{type:"author",value:"author_posts_page_link"},componentType:"comment"}},[["paragraph",{properties:{contents:["Author Name"],dynamicContent:{type:"comment",value:"comment_author"},componentType:"comment"},title:"Author"}]]],["paragraph",{title:"Question",properties:{contents:["Comment content"],dynamicContent:{type:"comment",value:"comment_content"},componentType:"comment"}}],["collection",{title:"Replies",properties:{dynamicContent:{collectionType:"comments",commentType:"tutor_q_and_a",pagination:!1}}},[["comments",{},[["comment-item",{},[[Ge+"-qna-reply",{}]]]]]]],[Ge+"-qna-editor",{properties:{}}],[Ge+"-reply-button",{properties:{}}]]]]]]]]}))}));const Ks={name:Ge+"-qna-questions",type:"element",source:Ge,className:"",title:"Questions",description:"Questions",visibility:!1,icon:"".concat(Qe.i.iconPrefix,"-star"),hoverIcon:"".concat(Qe.i.iconPrefix,"-star-filled"),category:"Tutor LMS",properties:{tag:"div"},children:[],constraints:{ancestors:[{element:Ge+"-q-and-a",condition:"REQUIRED"}]},Component:Js,controls:{margin:!0,padding:!1},manualDelete:!1};function Xs(){return Xs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Xs.apply(this,arguments)}var Ys=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=(e.properties.contents,Qe.i.getCanvasElement(n));return $e.createElement("div",Xs({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[[Ge+"-qna-noncurrent-user-reply",{}],[Ge+"-qna-current-user-reply",{}]]}))}));const el={name:Ge+"-qna-reply",type:"element",source:Ge,className:"",title:"QnA reply",description:"QnA reply",icon:"".concat(Qe.i.iconPrefix,"-star"),visibility:!1,properties:{tag:"div"},children:[],constraints:{ancestors:[{element:Ge+"-qna-questions",condition:"REQUIRED"}],childrens:[{element:Ge+"-qna-current-user-reply",condition:"ALLOW"},{element:Ge+"-qna-noncurrent-user-reply",condition:"ALLOW"}]},Component:Ys,controls:{margin:!0,padding:!1},manualDelete:!1};function tl(){return tl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},tl.apply(this,arguments)}var nl=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=(e.properties.contents,Qe.i.getCanvasElement(n));return $e.createElement("div",tl({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[["link-block",{properties:{dynamicContent:{type:"author",value:"author_posts_page_link"},componentType:"comment"}},[["paragraph",{properties:{contents:["Author Name"],dynamicContent:{type:"comment",value:"comment_author"},componentType:"comment"},title:"Author"}]]],["paragraph",{title:"Reply",properties:{contents:["Comment content"],dynamicContent:{type:"comment",value:"comment_content"},componentType:"comment"}}]]}))}));const rl={name:Ge+"-qna-current-user-reply",type:"element",source:Ge,className:"",title:"Current user reply",description:"Current user reply",icon:"".concat(Qe.i.iconPrefix,"-star"),visibility:!1,properties:{tag:"div"},children:[],constraints:{ancestors:[{element:Ge+"-qna-reply",condition:"REQUIRED"}]},Component:nl,controls:{margin:!0,padding:!1},manualDelete:!1};function il(){return il=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},il.apply(this,arguments)}var ol=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=(e.properties.contents,Qe.i.getCanvasElement(n));return $e.createElement("div",il({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[["link-block",{properties:{dynamicContent:{type:"author",value:"author_posts_page_link"},componentType:"comment"}},[["paragraph",{properties:{contents:["Author Name"],dynamicContent:{type:"comment",value:"comment_author"},componentType:"comment"},title:"Author"}]]],["paragraph",{title:"Reply",properties:{contents:["Comment content"],dynamicContent:{type:"comment",value:"comment_content"},componentType:"comment"}}]]}))}));const al={name:Ge+"-qna-noncurrent-user-reply",type:"element",source:Ge,className:"",title:"Non current user reply",description:"Non current user reply",icon:"".concat(Qe.i.iconPrefix,"-star"),visibility:!1,properties:{tag:"div"},children:[],constraints:{ancestors:[{element:Ge+"-qna-reply",condition:"REQUIRED"}]},Component:ol,controls:{margin:!0,padding:!1},manualDelete:!1};function sl(e){return sl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},sl(e)}function ll(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function cl(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ll(Object(n),!0).forEach((function(t){ul(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ll(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ul(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==sl(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==sl(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===sl(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var pl=Qe.i.elementSettings,ml=[{key:"state",label:"Show State",setting:cl(cl({},pl.SELECT),{},{options:[{value:"no-resources",title:"No Resources"},{value:"has-resources",title:"Has Resources"}]})}],fl=[{key:"template",label:"Template",setting:cl(cl({},pl.INPUT),{},{placeholder:"{{size}}"})}];function dl(){return dl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},dl.apply(this,arguments)}var gl=function(e){var t=Qe.i.getCanvasElement(e).properties.settings;return(null==t?void 0:t.state)||"no-resources"},yl=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",dl({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[[Ge+"-no-resources",{properties:{}}],[Ge+"-has-resources",{properties:{}}]]}))}));const bl={name:Ge+"-resources",type:"element",source:Ge,className:"",title:"Resources",description:"Resources",icon:"".concat(Qe.i.iconPrefix,"-resources-line"),hoverIcon:"".concat(Qe.i.iconPrefix,"-resources-fill"),category:"Tutor LMS",properties:{tag:"div",settings:{state:"no-resources"}},children:[],defaultStyle:"margin: 0 auto;",constraints:{childrens:[{element:"svg",condition:"ALLOW"},{element:"svg-icon",condition:"ALLOW"},{element:"paragraph",condition:"ALLOW"}]},Component:yl,controls:{margin:!0,padding:!1,height:!1,width:!0},settings:ml};function hl(e){return hl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},hl(e)}function vl(){return vl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},vl.apply(this,arguments)}var Ol=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=e.properties.contents,a=Qe.i.getCanvasElement(n);return $e.createElement("div",vl({},Qe.i.getAllAttributes(a),{ref:t,className:r,"data-element_hide":"no-resources"!==gl(a.parentId)}),i({template:[["svg",{style:"height: auto;",properties:{svgOuterHtml:'<svg viewBox="0 0 824 242" fill="none" style="height: auto;" xmlns="http://www.w3.org/2000/svg">\n            <path d="M0 241.266s369.993-203.2255 824-.957l-824 .957Z" fill="url(#a)"/>\n            <path d="M281.32 167.026v20.072" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>\n            <path d="M291.09 156.888c-6.818-23.128-9.617-25.522-9.617-25.522s-2.137 2.547-9.567 25.522c-6.513 20.021 4.987 20.123 8.091 19.817-.051 0 17.708 2.649 11.093-19.817Z" fill="#E5E7EA"/>\n            <path d="M502.926 152.1v20.785" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>\n            <path d="M513.002 141.605c-7.074-23.943-9.974-26.388-9.974-26.388s-2.188 2.649-9.923 26.388c-6.717 20.734 5.191 20.836 8.346 20.53.05 0 18.42 2.7 11.551-20.53Z" fill="#E5E7EA"/>\n            <path d="M534.322 167.077v15.793" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>\n            <path d="M542.006 159.13c-5.394-18.187-7.531-20.072-7.531-20.072s-1.679 1.987-7.531 20.072c-5.089 15.69 3.918 15.792 6.361 15.588-.051 0 13.891 2.038 8.701-15.588Z" fill="#E5E7EA"/>\n            <path d="m349.761 45.1695 100.397-8.9151s24.272 1.1717 26.868 31.0754c2.595 29.9038 5.037 47.3262 5.037 47.3262l-92.153 11.106-5.954-50.3322c-.763-6.5208-3.256-12.6849-7.327-17.8302-4.987-6.266-13.281-12.7358-26.868-12.4301Z" fill="#E3E5EA"/>\n            <path d="m392.963 125.202-61.928 7.03-6.259-52.2679c-2.086-17.4226 10.534-33.1133 27.987-34.8453 16.894-1.6302 32.007 10.5453 33.992 27.4585l6.208 52.6247ZM404.616 123.469v18.391h16.385l-.204-19.868-16.181 1.477Z" fill="#C1C3CA"/>\n            <path d="m330.374 132.792-7.887 26.185c-1.221 3.617-1.73 7.438-1.476 11.258.509 7.183 3.918 16.251 18.064 15.895 8.753-.255 16.182-3.515 22.085-7.438 8.243-5.502 14.604-13.347 18.573-22.415l13.739-30.464-63.098 6.979Z" fill="#D0D2D6"/>\n            <path d="M422.324 140.994h-18.777v43.149h18.777v-43.149Z" fill="#CDCFD4"/>\n            <path d="m381.768 60.045-54.447 39.0227M382.989 60.8607l-51.75 70.6073M360.396 128.666l22.441-65.2582M350.27 82.7662s11.042-3.0058 10.991 6.2659c0 0 10.635-2.3942 10.025 8.253M335.615 93.5153s13.994-3.0056 14.248 11.6657c0 0 11.195-4.126 15.52 7.744" stroke="#CDCFD4" stroke-width="3" stroke-miterlimit="10"/>\n            <path fill-rule="evenodd" clip-rule="evenodd" d="m273.789 22.3978-.814-.051v1.3246l.814.0509v-1.3245ZM273.942 20.0038l-.814-.0509-.102 1.3244.814.051.102-1.3245ZM274.044 17.6093l-.814-.0508-.051 1.3244.814.051.051-1.3246ZM273.84 29.5302l-.814.0509c.051.4584.051.917.102 1.3245l.814-.051c-.102-.4075-.102-.8659-.102-1.3244ZM273.738 27.1358h-.814c0 .4584 0 .917.051 1.3245h.814c0-.4075-.051-.8661-.051-1.3245ZM273.738 24.7925h-.814v1.3245h.814v-1.3245ZM274.705 36.611l-.763.2037.305 1.3246.763-.2037-.305-1.3246ZM274.298 34.2678l-.814.102c.051.4585.153.9169.203 1.3244l.815-.1528c-.102-.3566-.153-.8151-.204-1.2736ZM273.993 31.9246l-.814.1019c.051.4585.101.917.152 1.3245l.815-.1019c-.051-.4585-.102-.866-.153-1.3245ZM276.995 43.3356l-.712.3566c.203.4076.407.8152.61 1.2227l.713-.4075c-.255-.3566-.407-.7642-.611-1.1718ZM276.079 41.1959l-.763.2547.509 1.2736.712-.3057-.458-1.2226ZM275.316 38.9547l-.763.2547c.102.4585.254.8661.407 1.2736l.763-.2547c-.153-.4585-.305-.8661-.407-1.2736ZM280.863 49.3468l-.662.5095.916 1.0188.611-.5604-.865-.9679ZM279.387 47.4623l-.662.4585c.255.3566.56.7132.814 1.0698l.662-.5094c-.255-.3057-.56-.6623-.814-1.0189ZM278.115 45.4754l-.713.4075c.204.4076.458.7642.713 1.1718l.661-.4585c-.203-.3566-.458-.7642-.661-1.1208ZM286.002 54.2377l-.509.6622c.356.3056.763.5094 1.12.7641l.458-.6621c-.357-.2548-.713-.5095-1.069-.7642ZM284.17 52.7094l-.56.6113c.357.3057.662.6113 1.018.866l.509-.6113c-.305-.2547-.662-.5603-.967-.866ZM282.44 51.0791l-.611.5604c.305.3057.611.6622.967.9679l.56-.6113c-.306-.2547-.611-.6113-.916-.917ZM292.159 57.7525l-.305.7641c.407.1529.814.3566 1.272.5095l.305-.7642c-.458-.1528-.865-.3056-1.272-.5094ZM290.022 56.7336l-.407.7642 1.221.6113.356-.7642-1.17-.6113ZM287.986 55.562l-.458.7133 1.171.6622.407-.6622-1.12-.7133ZM298.978 59.7395l-.102.8151c.458.051.916.1529 1.374.1529l.051-.8152c-.458-.0509-.916-.1019-1.323-.1528ZM296.637 59.2808l-.203.8152 1.373.2547.153-.8152-1.323-.2547ZM294.398 58.6186l-.254.7642c.407.1528.865.2547 1.272.4075l.203-.7641c-.407-.1528-.814-.2547-1.221-.4076ZM306 59.5355l.153.8152c.458-.1019.916-.2038 1.323-.3057l-.204-.7642c-.407.051-.814.1529-1.272.2547ZM303.659 59.8923l.051.8152c.458-.051.916-.1019 1.374-.1529l-.102-.8151c-.407.0509-.865.1019-1.323.1528ZM301.318 59.9435v.815c.458 0 .916.0509 1.374 0v-.815c-.458.0509-.916 0-1.374 0ZM312.36 56.6318l.509.6113c.356-.3057.662-.6622 1.018-.9679l-.611-.5604c-.305.3056-.61.6113-.916.917ZM310.427 57.9565l.407.7133c.407-.2548.763-.5096 1.17-.7133l-.458-.6622c-.407.2038-.763.4584-1.119.6622ZM308.29 58.9246l.254.7641c.407-.1528.865-.3566 1.272-.5094l-.356-.7642c-.356.1529-.763.3566-1.17.5095ZM315.566 50.6717l.814.0508c.051-.4585 0-.9679-.051-1.4264l-.814.102c.102.4075.102.866.051 1.2736ZM315.058 52.9131l.763.3057c.203-.4585.305-.9171.407-1.3755l-.814-.1528c-.051.4076-.204.8151-.356 1.2226ZM313.938 54.8997l.662.4586c.254-.4075.508-.7642.763-1.1717l-.713-.4076c-.254.4076-.458.7641-.712 1.1207ZM311.953 45.6282l.204-.815c-.458-.1019-.916-.2038-1.425-.2548l-.051.8151c.458.1018.865.1528 1.272.2547ZM313.989 46.6468l.508-.6113c-.407-.3056-.814-.5604-1.272-.7641l-.356.7641c.407.1528.763.3566 1.12.6113ZM315.312 48.4301l.763-.3056c-.153-.4585-.407-.917-.712-1.2736l-.611.5095c.203.3056.407.6622.56 1.0697ZM305.287 46.8508l-.407-.7133c-.203.1019-.407.2547-.559.4076-.204.1528-.408.2547-.56.4075l.509.6623c.152-.1528.356-.2547.508-.4075.153-.1529.306-.2547.509-.3566ZM307.425 45.8321l-.255-.7642c-.254.051-.458.1528-.661.2547-.204.1019-.407.2037-.662.3056l.356.7133c.204-.1019.407-.2038.611-.2547.204-.1019.356-.2038.611-.2547ZM309.664 45.4244l-.051-.8152c-.255 0-.458.051-.713.051-.254 0-.458.0509-.712.1019l.153.8151c.203-.0509.407-.102.61-.102.204 0 .509-.0508.713-.0508ZM300.758 52.047l-.763-.3057c-.203.4076-.356.866-.509 1.2736l.764.2547c.203-.4075.356-.815.508-1.2226ZM301.929 50.0094l-.662-.4586c-.152.2038-.254.4076-.407.5605-.102.2037-.254.4075-.356.6113l.712.4075c.102-.2038.204-.3566.357-.5604.101-.2038.203-.3565.356-.5603ZM303.456 48.2771l-.56-.6113c-.153.1529-.356.3057-.509.5095-.152.1528-.305.3566-.458.5094l.611.5094c.152-.1528.254-.3565.407-.4584.203-.1528.356-.3057.509-.4586Z" fill="#C1C3CA"/>\n            <path fill-rule="evenodd" clip-rule="evenodd" d="m299.588 58.9753-.814.051c0 .4585.051.917.102 1.3755l.814-.1019c-.051-.4585-.102-.917-.102-1.3246ZM299.639 56.5808l-.814-.1019c-.051.4585-.051.917-.102 1.3754h.814c.051-.4075.051-.8151.102-1.2735ZM299.995 54.2887l-.763-.2037c-.102.4585-.204.9169-.254 1.3754l.814.1019c.051-.4075.101-.866.203-1.2736ZM300.962 65.9034l-.763.2547c.152.4076.305.8661.458 1.2736l.763-.3057c-.153-.4075-.305-.815-.458-1.2226ZM300.25 63.611l-.764.2037c.102.4585.255.8661.357 1.3246l.763-.2548c-.102-.4075-.255-.815-.356-1.2735ZM299.792 61.3185l-.814.1019c.05.4585.152.917.254 1.3246l.814-.1529c-.102-.4075-.203-.8151-.254-1.2736ZM304.168 72.2206l-.662.4586.764 1.1207.661-.5094-.763-1.0699ZM302.947 70.1829l-.713.4076.662 1.1717.712-.4076-.661-1.1717ZM301.878 68.0944l-.763.3566c.203.4075.356.815.559 1.2226l.713-.3566-.509-1.2226ZM308.849 77.5696l-.559.6115 1.017.9168.509-.6113-.967-.917ZM307.17 75.9394l-.61.5604.916.9679.61-.6113-.916-.917ZM305.593 74.1564l-.611.5094.865 1.0189.611-.5095-.865-1.0188ZM314.599 81.7471l-.407.7132 1.171.6623.407-.7133-1.171-.6622ZM312.564 80.5243l-.458.6623 1.17.7641.407-.7131-1.119-.7133ZM310.681 79.1487l-.509.6113 1.069.8151.458-.6622-1.018-.7642ZM321.113 84.4978l-.204.8151 1.323.3566.153-.8152-1.272-.3565ZM318.874 83.7849l-.254.7641 1.272.4584.254-.8151-1.272-.4074ZM316.686 82.868l-.306.7131 1.222.5605.305-.7642-1.221-.5094ZM281.371 8.49081s-.712 4.17739 3.257 4.83959c0 0 2.392-1.732 1.272-4.07544-1.068-2.29245-4.529-.76415-4.529-.76415Z" fill="#C1C3CA"/>\n            <path fill-rule="evenodd" clip-rule="evenodd" d="M280.812 8.79593s-.407 4.33027 3.409 4.94157c0 0-1.628 1.834-3.155.7642-1.425-.9679-2.442-2.8019-2.086-4.4831.101-.40751.305-.8151.661-1.06982.306-.10189.713-.25474 1.171-.15285Z" fill="#C1C3CA"/>\n            <path fill-rule="evenodd" clip-rule="evenodd" d="M278.115 10.3243c0-.1018-1.985 2.1397-1.527 3.7699 0 0-1.17.6113-1.119 1.3245 0 0 .865-.866 1.628-.4585 0 0 1.527 1.7321 3.918.4585.051-.051-2.697-1.5793-2.9-5.0944ZM281.117 7.77717c-1.221-.96792-2.137-2.2924-2.697-3.76976-.153-.35661-.254-.71318-.254-1.12073-.051-.61132.152-1.22264.509-1.78302.254-.407546.508-.764224.966-.967998.865-.407547 1.985.15283 2.443 1.018868.458.86604.407 1.88495.254 2.85288-.152 1.12075-.458 2.2925-.865 3.36231-.102.20378-.203.40745-.356.40745-.153.05094-.305-.10184-.458-.20372-1.425-1.47736-3.358-2.29241-5.241-3.00561-.407-.15283-.865-.30576-1.272-.35671-.509 0-.967.15293-1.425.35671-.611.30566-1.17.76405-1.425 1.42631-.254.66226-.102 1.42647.407 1.83401.306.25472.713.30569 1.12.40757 2.697.4585 5.444.35663 8.091-.35657" fill="#C1C3CA"/>\n            <path d="m392.352 124.998-61.266 6.979-6.208-51.7582c-2.086-17.2189 10.432-32.8076 27.682-34.4378 16.741-1.6302 31.701 10.4434 33.635 27.1529l6.157 52.0641Z" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>\n            <path d="M457.995 24.5374 474.329 36.56c.661.4584.458 1.5283-.356 1.7321l-6.565 1.5283.56 6.8774c.051.866-.916 1.3754-1.577.815l-16.487-14.4169 8.091-8.5585Z" fill="#3E64DE"/>\n            <path d="M443.492 60.0443c1.996 0 3.613-1.6422 3.613-3.6679 0-2.0258-1.617-3.6679-3.613-3.6679-1.995 0-3.613 1.6421-3.613 3.6679 0 2.0257 1.618 3.6679 3.613 3.6679Z" fill="#949BA9"/>\n            <path d="M444.815 54.5424c.611-3.0566 1.222-6.1132 1.832-9.1698.407-2.1397.865-4.2793 1.272-6.4698.204-.917.713-2.2925.611-3.2095.051.6623-.814 1.019.102.2039.509-.4585 1.017-1.0189 1.475-1.5283 1.527-1.5283 3.054-3.1076 4.529-4.6359 2.188-2.2415 4.427-4.534 6.615-6.7755 1.629-1.6302-.916-4.1773-2.493-2.4962-2.341 2.3943-4.631 4.7377-6.971 7.1321-1.476 1.5283-2.952 3.0565-4.478 4.5339-.713.7132-1.73 1.4774-2.188 2.4453-.407.917-.458 2.1396-.662 3.0566-.407 2.0887-.814 4.2282-1.272 6.3169-.661 3.2095-1.272 6.4698-1.934 9.6793-.305 2.1905 3.155 3.1585 3.562.917Z" fill="#949BA9"/>\n            <path d="M558.035 22.0415c2.952 0 2.952-4.5849 0-4.5849-2.951 0-2.951 4.5849 0 4.5849Z" fill="url(#b)"/>\n            <defs>\n            <linearGradient id="a" x1="406.358" y1="69.2193" x2="408.386" y2="355.63" gradientUnits="userSpaceOnUse">\n                <stop offset=".1653" stop-color="#E3E5EA"/>\n                <stop offset=".3741" stop-color="#F4F5F7" stop-opacity=".6199"/>\n                <stop offset=".5962" stop-color="#F6F7F8" stop-opacity="0"/>\n            </linearGradient>\n            <linearGradient id="b" x1="558.006" y1="22.0271" x2="558.065" y2="17.4425" gradientUnits="userSpaceOnUse">\n                <stop offset=".1653" stop-color="#E3E5EA"/>\n                <stop offset=".2826" stop-color="#EDEFF1" stop-opacity=".8497"/>\n                <stop offset=".4662" stop-color="#F4F5F7" stop-opacity=".6143"/>\n                <stop offset=".9455" stop-color="#F6F7F8" stop-opacity="0"/>\n            </linearGradient>\n            </defs>\n        </svg>'}}],["text",{properties:{contents:o},style:"color: #41454F;"}]]}))}));const wl={name:Ge+"-no-resources",type:"element",source:Ge,className:"",title:"No Resources",description:"No Resources",visibility:!1,properties:{tag:"div",contents:["No Attachment Found"],attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==hl(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==hl(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===hl(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","no-resources")},children:[],defaultStyle:"padding: 32px; text-align: center;",constraints:{childrens:[{element:"text",condition:"ALLOW"},{element:"heading",condition:"ALLOW"},{element:"svg",condition:"ALLOW"},{element:"svg-icon",condition:"ALLOW"},{element:"custom-code",condition:"ALLOW"}]},Component:Ol,controls:{margin:!0,padding:!1},manualDelete:!1};function Sl(e){return Sl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Sl(e)}function El(){return El=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},El.apply(this,arguments)}var jl=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=(e.properties.contents,Qe.i.getCanvasElement(n));return $e.createElement("div",El({},Qe.i.getAllAttributes(o),{ref:t,className:r,"data-element_hide":"has-resources"!==gl(o.parentId)}),i({template:[[Ge+"-resource-item",{properties:{}}]]}))}));const Cl={name:Ge+"-has-resources",type:"element",source:Ge,className:"",title:"Has Resources",description:"Has Resources",visibility:!1,properties:{tag:"div",attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Sl(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Sl(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Sl(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","has-resources")},children:[],defaultStyle:"display: flex; column-gap: 16px;",constraints:{childrens:[{element:"text",condition:"ALLOW"},{element:"heading",condition:"ALLOW"},{element:"svg",condition:"ALLOW"},{element:"svg-icon",condition:"ALLOW"},{element:"custom-code",condition:"ALLOW"}]},Component:jl,controls:{margin:!0,padding:!1},manualDelete:!1};function Al(){return Al=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Al.apply(this,arguments)}const Ll={name:Ge+"-resource-item",type:"element",source:Ge,className:"",title:"Resource Item",description:"Resource Item",visibility:!1,properties:{tag:"div"},children:[],defaultStyle:"background-color: #fff;\n    border: 1px solid #cdcfd5;\n    border-radius: 6px;\n    display: flex;\n    justify-content: space-between;\n    padding: 16px;",constraints:{},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",Al({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[["div",{properties:{},style:"display: flex; flex-direction: column; row-gap: 4px; width: 100%;"},[[Ge+"-resource-title",{properties:{}}],[Ge+"-resource-size",{properties:{}}]]],[Ge+"-resource-download",{properties:{}}]]}))})),controls:{margin:!0,padding:!1,height:!1,width:!0},manualDelete:!1};function xl(){return xl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},xl.apply(this,arguments)}const Pl={name:Ge+"-resource-download",type:"element",source:Ge,className:"",title:"Resource Download",description:"Resource Download",visibility:!1,properties:{tag:"a",type:"href",attributes:{href:"",target:""}},children:[],defaultStyle:"\n    background-color: rgba(62, 100, 222, 0.1);\n    color: #3e64de;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 32px;\n    height: 32px;\n    padding: 0;\n    margin-top: -4px;\n    margin-bottom: -4px;\n    font-weight: 400;\n    line-height: 1.4;\n    text-align: center;\n    text-decoration: none;\n    vertical-align: middle;\n    user-select: none;\n    font-size: 16px;\n    border: 1px solid rgba(0,0,0,0);\n    border-radius: 48px;\n    cursor: pointer;\n    transition: color 200ms ease-in-out,background-color 200ms ease-in-out;\n  ",constraints:{childrens:[{element:"text",condition:"ALLOW"},{element:"svg",condition:"ALLOW"},{element:"svg-icon",condition:"ALLOW"},{element:"custom-code",condition:"ALLOW"}]},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=(e.properties.contents,Qe.i.getCanvasElement(n));return $e.createElement("a",xl({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[["svg",{properties:{svgOuterHtml:"<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M11 4a4 4 0 0 0-3.999 4.102 1 1 0 0 1-.75.992A3.002 3.002 0 0 0 7 15h1a1 1 0 1 1 0 2H7a5 5 0 0 1-1.97-9.596 6 6 0 0 1 11.169-2.4A6 6 0 0 1 16 17a1 1 0 1 1 0-2 4 4 0 1 0-.328-7.987 1 1 0 0 1-.999-.6A4.001 4.001 0 0 0 11 4zm1 6a1 1 0 0 1 1 1v7.586l.293-.293a1 1 0 0 1 1.414 1.414l-2 2a1 1 0 0 1-1.414 0l-2-2a1 1 0 1 1 1.414-1.414l.293.293V11a1 1 0 0 1 1-1z' ></path></svg>"},style:"height:24px;width:24px;min-width:auto;min-height:auto;"}]]}))})),controls:{margin:!0,padding:!1}};function Rl(){return Rl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Rl.apply(this,arguments)}const Ml={name:Ge+"-resource-size",type:"element",source:Ge,className:"",title:"Resource Size",description:"Resource Size",visibility:!1,properties:{tag:"span",settings:{template:"Size: {{size}} KB"}},constraints:{},defaultStyle:"color: #757c8e;\n  font-size: 0.875rem;",Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("span",Rl({},Qe.i.getAllAttributes(i),{ref:t,className:r}),i.properties.settings.template.replace("{{size}}","66.7"))})),controls:{margin:!1,padding:!1,height:!1,width:!1},settings:fl};function Nl(){return Nl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Nl.apply(this,arguments)}const Zl={name:Ge+"-resource-title",type:"element",source:Ge,className:"",title:"Resource Title",description:"Resource Title",visibility:!1,properties:{tag:"span"},defaultStyle:"\n    color: #212327;\n    font-weight: 500;\n    font-size: 1rem;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n  ",constraints:{},Component:(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("span",Nl({},Qe.i.getAllAttributes(i),{ref:t,className:r}),"example.img")})),controls:{margin:!0,padding:!1,height:!1,width:!0}};function Tl(e){return Tl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Tl(e)}function Il(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function kl(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Il(Object(n),!0).forEach((function(t){Dl(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Il(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Dl(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Tl(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Tl(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Tl(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var _l=[{key:"state",label:"Show State",setting:kl(kl({},Qe.i.elementSettings.SELECT),{},{options:[{value:"no-gradebook",title:"No Gradebook"},{value:"has-gradebook",title:"Has Gradebook"}]})}];function Wl(){return Wl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Wl.apply(this,arguments)}var Fl=function(e){var t=Qe.i.getCanvasElement(e).properties.settings;return(null==t?void 0:t.state)||"no-gradebook"},zl=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=Qe.i.getCanvasElement(n);return $e.createElement("div",Wl({},Qe.i.getAllAttributes(o),{ref:t,className:r}),i({template:[[Ge+"-no-gradebook",{properties:{}}],[Ge+"-has-gradebook",{properties:{}}]]}))}));const Ul={name:Ge+"-gradebook",type:"element",source:Ge,className:"",title:"Gradebook",description:"Gradebook",icon:"".concat(Qe.i.iconPrefix,"-gradebook-line"),hoverIcon:"".concat(Qe.i.iconPrefix,"-gradebook-fill"),category:"Tutor LMS",properties:{tag:"div",settings:{state:"no-gradebook"}},children:[],defaultStyle:"margin: 0 auto;",constraints:{childrens:[{element:"svg",condition:"ALLOW"},{element:"svg-icon",condition:"ALLOW"},{element:"paragraph",condition:"ALLOW"}]},Component:zl,controls:{margin:!0,padding:!1,height:!1,width:!0},settings:_l};function Bl(e){return Bl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Bl(e)}function Hl(){return Hl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Hl.apply(this,arguments)}var ql=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=e.renderChildren,o=e.properties.contents,a=Qe.i.getCanvasElement(n);return $e.createElement("div",Hl({},Qe.i.getAllAttributes(a),{ref:t,className:r,"data-element_hide":"no-gradebook"!==Fl(a.parentId)}),i({template:[["svg",{style:"height: auto;",properties:{svgOuterHtml:'<svg viewBox="0 0 824 242" fill="none" style="height: auto;" xmlns="http://www.w3.org/2000/svg">\n            <path d="M0 241.266s369.993-203.2255 824-.957l-824 .957Z" fill="url(#gradebook_a)"/>\n            <path d="M281.32 167.026v20.072" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>\n            <path d="M291.09 156.888c-6.818-23.128-9.617-25.522-9.617-25.522s-2.137 2.547-9.567 25.522c-6.513 20.021 4.987 20.123 8.091 19.817-.051 0 17.708 2.649 11.093-19.817Z" fill="#E5E7EA"/>\n            <path d="M502.926 152.1v20.785" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>\n            <path d="M513.002 141.605c-7.074-23.943-9.974-26.388-9.974-26.388s-2.188 2.649-9.923 26.388c-6.717 20.734 5.191 20.836 8.346 20.53.05 0 18.42 2.7 11.551-20.53Z" fill="#E5E7EA"/>\n            <path d="M534.322 167.077v15.793" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>\n            <path d="M542.006 159.13c-5.394-18.187-7.531-20.072-7.531-20.072s-1.679 1.987-7.531 20.072c-5.089 15.69 3.918 15.792 6.361 15.588-.051 0 13.891 2.038 8.701-15.588Z" fill="#E5E7EA"/>\n            <path d="m349.761 45.1695 100.397-8.9151s24.272 1.1717 26.868 31.0754c2.595 29.9038 5.037 47.3262 5.037 47.3262l-92.153 11.106-5.954-50.3322c-.763-6.5208-3.256-12.6849-7.327-17.8302-4.987-6.266-13.281-12.7358-26.868-12.4301Z" fill="#E3E5EA"/>\n            <path d="m392.963 125.202-61.928 7.03-6.259-52.2679c-2.086-17.4226 10.534-33.1133 27.987-34.8453 16.894-1.6302 32.007 10.5453 33.992 27.4585l6.208 52.6247ZM404.616 123.469v18.391h16.385l-.204-19.868-16.181 1.477Z" fill="#C1C3CA"/>\n            <path d="m330.374 132.792-7.887 26.185c-1.221 3.617-1.73 7.438-1.476 11.258.509 7.183 3.918 16.251 18.064 15.895 8.753-.255 16.182-3.515 22.085-7.438 8.243-5.502 14.604-13.347 18.573-22.415l13.739-30.464-63.098 6.979Z" fill="#D0D2D6"/>\n            <path d="M422.324 140.994h-18.777v43.149h18.777v-43.149Z" fill="#CDCFD4"/>\n            <path d="m381.768 60.045-54.447 39.0227M382.989 60.8607l-51.75 70.6073M360.396 128.666l22.441-65.2582M350.27 82.7662s11.042-3.0058 10.991 6.2659c0 0 10.635-2.3942 10.025 8.253M335.615 93.5153s13.994-3.0056 14.248 11.6657c0 0 11.195-4.126 15.52 7.744" stroke="#CDCFD4" stroke-width="3" stroke-miterlimit="10"/>\n            <path fill-rule="evenodd" clip-rule="evenodd" d="m273.789 22.3978-.814-.051v1.3246l.814.0509v-1.3245ZM273.942 20.0038l-.814-.0509-.102 1.3244.814.051.102-1.3245ZM274.044 17.6093l-.814-.0508-.051 1.3244.814.051.051-1.3246ZM273.84 29.5302l-.814.0509c.051.4584.051.917.102 1.3245l.814-.051c-.102-.4075-.102-.8659-.102-1.3244ZM273.738 27.1358h-.814c0 .4584 0 .917.051 1.3245h.814c0-.4075-.051-.8661-.051-1.3245ZM273.738 24.7925h-.814v1.3245h.814v-1.3245ZM274.705 36.611l-.763.2037.305 1.3246.763-.2037-.305-1.3246ZM274.298 34.2678l-.814.102c.051.4585.153.9169.203 1.3244l.815-.1528c-.102-.3566-.153-.8151-.204-1.2736ZM273.993 31.9246l-.814.1019c.051.4585.101.917.152 1.3245l.815-.1019c-.051-.4585-.102-.866-.153-1.3245ZM276.995 43.3356l-.712.3566c.203.4076.407.8152.61 1.2227l.713-.4075c-.255-.3566-.407-.7642-.611-1.1718ZM276.079 41.1959l-.763.2547.509 1.2736.712-.3057-.458-1.2226ZM275.316 38.9547l-.763.2547c.102.4585.254.8661.407 1.2736l.763-.2547c-.153-.4585-.305-.8661-.407-1.2736ZM280.863 49.3468l-.662.5095.916 1.0188.611-.5604-.865-.9679ZM279.387 47.4623l-.662.4585c.255.3566.56.7132.814 1.0698l.662-.5094c-.255-.3057-.56-.6623-.814-1.0189ZM278.115 45.4754l-.713.4075c.204.4076.458.7642.713 1.1718l.661-.4585c-.203-.3566-.458-.7642-.661-1.1208ZM286.002 54.2377l-.509.6622c.356.3056.763.5094 1.12.7641l.458-.6621c-.357-.2548-.713-.5095-1.069-.7642ZM284.17 52.7094l-.56.6113c.357.3057.662.6113 1.018.866l.509-.6113c-.305-.2547-.662-.5603-.967-.866ZM282.44 51.0791l-.611.5604c.305.3057.611.6622.967.9679l.56-.6113c-.306-.2547-.611-.6113-.916-.917ZM292.159 57.7525l-.305.7641c.407.1529.814.3566 1.272.5095l.305-.7642c-.458-.1528-.865-.3056-1.272-.5094ZM290.022 56.7336l-.407.7642 1.221.6113.356-.7642-1.17-.6113ZM287.986 55.562l-.458.7133 1.171.6622.407-.6622-1.12-.7133ZM298.978 59.7395l-.102.8151c.458.051.916.1529 1.374.1529l.051-.8152c-.458-.0509-.916-.1019-1.323-.1528ZM296.637 59.2808l-.203.8152 1.373.2547.153-.8152-1.323-.2547ZM294.398 58.6186l-.254.7642c.407.1528.865.2547 1.272.4075l.203-.7641c-.407-.1528-.814-.2547-1.221-.4076ZM306 59.5355l.153.8152c.458-.1019.916-.2038 1.323-.3057l-.204-.7642c-.407.051-.814.1529-1.272.2547ZM303.659 59.8923l.051.8152c.458-.051.916-.1019 1.374-.1529l-.102-.8151c-.407.0509-.865.1019-1.323.1528ZM301.318 59.9435v.815c.458 0 .916.0509 1.374 0v-.815c-.458.0509-.916 0-1.374 0ZM312.36 56.6318l.509.6113c.356-.3057.662-.6622 1.018-.9679l-.611-.5604c-.305.3056-.61.6113-.916.917ZM310.427 57.9565l.407.7133c.407-.2548.763-.5096 1.17-.7133l-.458-.6622c-.407.2038-.763.4584-1.119.6622ZM308.29 58.9246l.254.7641c.407-.1528.865-.3566 1.272-.5094l-.356-.7642c-.356.1529-.763.3566-1.17.5095ZM315.566 50.6717l.814.0508c.051-.4585 0-.9679-.051-1.4264l-.814.102c.102.4075.102.866.051 1.2736ZM315.058 52.9131l.763.3057c.203-.4585.305-.9171.407-1.3755l-.814-.1528c-.051.4076-.204.8151-.356 1.2226ZM313.938 54.8997l.662.4586c.254-.4075.508-.7642.763-1.1717l-.713-.4076c-.254.4076-.458.7641-.712 1.1207ZM311.953 45.6282l.204-.815c-.458-.1019-.916-.2038-1.425-.2548l-.051.8151c.458.1018.865.1528 1.272.2547ZM313.989 46.6468l.508-.6113c-.407-.3056-.814-.5604-1.272-.7641l-.356.7641c.407.1528.763.3566 1.12.6113ZM315.312 48.4301l.763-.3056c-.153-.4585-.407-.917-.712-1.2736l-.611.5095c.203.3056.407.6622.56 1.0697ZM305.287 46.8508l-.407-.7133c-.203.1019-.407.2547-.559.4076-.204.1528-.408.2547-.56.4075l.509.6623c.152-.1528.356-.2547.508-.4075.153-.1529.306-.2547.509-.3566ZM307.425 45.8321l-.255-.7642c-.254.051-.458.1528-.661.2547-.204.1019-.407.2037-.662.3056l.356.7133c.204-.1019.407-.2038.611-.2547.204-.1019.356-.2038.611-.2547ZM309.664 45.4244l-.051-.8152c-.255 0-.458.051-.713.051-.254 0-.458.0509-.712.1019l.153.8151c.203-.0509.407-.102.61-.102.204 0 .509-.0508.713-.0508ZM300.758 52.047l-.763-.3057c-.203.4076-.356.866-.509 1.2736l.764.2547c.203-.4075.356-.815.508-1.2226ZM301.929 50.0094l-.662-.4586c-.152.2038-.254.4076-.407.5605-.102.2037-.254.4075-.356.6113l.712.4075c.102-.2038.204-.3566.357-.5604.101-.2038.203-.3565.356-.5603ZM303.456 48.2771l-.56-.6113c-.153.1529-.356.3057-.509.5095-.152.1528-.305.3566-.458.5094l.611.5094c.152-.1528.254-.3565.407-.4584.203-.1528.356-.3057.509-.4586Z" fill="#C1C3CA"/>\n            <path fill-rule="evenodd" clip-rule="evenodd" d="m299.588 58.9753-.814.051c0 .4585.051.917.102 1.3755l.814-.1019c-.051-.4585-.102-.917-.102-1.3246ZM299.639 56.5808l-.814-.1019c-.051.4585-.051.917-.102 1.3754h.814c.051-.4075.051-.8151.102-1.2735ZM299.995 54.2887l-.763-.2037c-.102.4585-.204.9169-.254 1.3754l.814.1019c.051-.4075.101-.866.203-1.2736ZM300.962 65.9034l-.763.2547c.152.4076.305.8661.458 1.2736l.763-.3057c-.153-.4075-.305-.815-.458-1.2226ZM300.25 63.611l-.764.2037c.102.4585.255.8661.357 1.3246l.763-.2548c-.102-.4075-.255-.815-.356-1.2735ZM299.792 61.3185l-.814.1019c.05.4585.152.917.254 1.3246l.814-.1529c-.102-.4075-.203-.8151-.254-1.2736ZM304.168 72.2206l-.662.4586.764 1.1207.661-.5094-.763-1.0699ZM302.947 70.1829l-.713.4076.662 1.1717.712-.4076-.661-1.1717ZM301.878 68.0944l-.763.3566c.203.4075.356.815.559 1.2226l.713-.3566-.509-1.2226ZM308.849 77.5696l-.559.6115 1.017.9168.509-.6113-.967-.917ZM307.17 75.9394l-.61.5604.916.9679.61-.6113-.916-.917ZM305.593 74.1564l-.611.5094.865 1.0189.611-.5095-.865-1.0188ZM314.599 81.7471l-.407.7132 1.171.6623.407-.7133-1.171-.6622ZM312.564 80.5243l-.458.6623 1.17.7641.407-.7131-1.119-.7133ZM310.681 79.1487l-.509.6113 1.069.8151.458-.6622-1.018-.7642ZM321.113 84.4978l-.204.8151 1.323.3566.153-.8152-1.272-.3565ZM318.874 83.7849l-.254.7641 1.272.4584.254-.8151-1.272-.4074ZM316.686 82.868l-.306.7131 1.222.5605.305-.7642-1.221-.5094ZM281.371 8.49081s-.712 4.17739 3.257 4.83959c0 0 2.392-1.732 1.272-4.07544-1.068-2.29245-4.529-.76415-4.529-.76415Z" fill="#C1C3CA"/>\n            <path fill-rule="evenodd" clip-rule="evenodd" d="M280.812 8.79593s-.407 4.33027 3.409 4.94157c0 0-1.628 1.834-3.155.7642-1.425-.9679-2.442-2.8019-2.086-4.4831.101-.40751.305-.8151.661-1.06982.306-.10189.713-.25474 1.171-.15285Z" fill="#C1C3CA"/>\n            <path fill-rule="evenodd" clip-rule="evenodd" d="M278.115 10.3243c0-.1018-1.985 2.1397-1.527 3.7699 0 0-1.17.6113-1.119 1.3245 0 0 .865-.866 1.628-.4585 0 0 1.527 1.7321 3.918.4585.051-.051-2.697-1.5793-2.9-5.0944ZM281.117 7.77717c-1.221-.96792-2.137-2.2924-2.697-3.76976-.153-.35661-.254-.71318-.254-1.12073-.051-.61132.152-1.22264.509-1.78302.254-.407546.508-.764224.966-.967998.865-.407547 1.985.15283 2.443 1.018868.458.86604.407 1.88495.254 2.85288-.152 1.12075-.458 2.2925-.865 3.36231-.102.20378-.203.40745-.356.40745-.153.05094-.305-.10184-.458-.20372-1.425-1.47736-3.358-2.29241-5.241-3.00561-.407-.15283-.865-.30576-1.272-.35671-.509 0-.967.15293-1.425.35671-.611.30566-1.17.76405-1.425 1.42631-.254.66226-.102 1.42647.407 1.83401.306.25472.713.30569 1.12.40757 2.697.4585 5.444.35663 8.091-.35657" fill="#C1C3CA"/>\n            <path d="m392.352 124.998-61.266 6.979-6.208-51.7582c-2.086-17.2189 10.432-32.8076 27.682-34.4378 16.741-1.6302 31.701 10.4434 33.635 27.1529l6.157 52.0641Z" stroke="#C1C3CA" stroke-width="5" stroke-miterlimit="10"/>\n            <path d="M457.995 24.5374 474.329 36.56c.661.4584.458 1.5283-.356 1.7321l-6.565 1.5283.56 6.8774c.051.866-.916 1.3754-1.577.815l-16.487-14.4169 8.091-8.5585Z" fill="#3E64DE"/>\n            <path d="M443.492 60.0443c1.996 0 3.613-1.6422 3.613-3.6679 0-2.0258-1.617-3.6679-3.613-3.6679-1.995 0-3.613 1.6421-3.613 3.6679 0 2.0257 1.618 3.6679 3.613 3.6679Z" fill="#949BA9"/>\n            <path d="M444.815 54.5424c.611-3.0566 1.222-6.1132 1.832-9.1698.407-2.1397.865-4.2793 1.272-6.4698.204-.917.713-2.2925.611-3.2095.051.6623-.814 1.019.102.2039.509-.4585 1.017-1.0189 1.475-1.5283 1.527-1.5283 3.054-3.1076 4.529-4.6359 2.188-2.2415 4.427-4.534 6.615-6.7755 1.629-1.6302-.916-4.1773-2.493-2.4962-2.341 2.3943-4.631 4.7377-6.971 7.1321-1.476 1.5283-2.952 3.0565-4.478 4.5339-.713.7132-1.73 1.4774-2.188 2.4453-.407.917-.458 2.1396-.662 3.0566-.407 2.0887-.814 4.2282-1.272 6.3169-.661 3.2095-1.272 6.4698-1.934 9.6793-.305 2.1905 3.155 3.1585 3.562.917Z" fill="#949BA9"/>\n            <path d="M558.035 22.0415c2.952 0 2.952-4.5849 0-4.5849-2.951 0-2.951 4.5849 0 4.5849Z" fill="url(#gradebook_b)"/>\n            <defs>\n            <linearGradient id="gradebook_a" x1="406.358" y1="69.2193" x2="408.386" y2="355.63" gradientUnits="userSpaceOnUse">\n                <stop offset=".1653" stop-color="#E3E5EA"/>\n                <stop offset=".3741" stop-color="#F4F5F7" stop-opacity=".6199"/>\n                <stop offset=".5962" stop-color="#F6F7F8" stop-opacity="0"/>\n            </linearGradient>\n            <linearGradient id="gradebook_b" x1="558.006" y1="22.0271" x2="558.065" y2="17.4425" gradientUnits="userSpaceOnUse">\n                <stop offset=".1653" stop-color="#E3E5EA"/>\n                <stop offset=".2826" stop-color="#EDEFF1" stop-opacity=".8497"/>\n                <stop offset=".4662" stop-color="#F4F5F7" stop-opacity=".6143"/>\n                <stop offset=".9455" stop-color="#F6F7F8" stop-opacity="0"/>\n            </linearGradient>\n            </defs>\n        </svg>'}}],["text",{properties:{contents:o},style:"color: #41454F;"}]]}))}));const Vl={name:Ge+"-no-gradebook",type:"element",source:Ge,className:"",title:"No Gradebook",description:"No Gradebook",visibility:!1,properties:{tag:"div",contents:["No Gradebook Data"],attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Bl(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Bl(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Bl(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","no-gradebook")},children:[],defaultStyle:"padding: 32px; text-align: center;",constraints:{childrens:[{element:"text",condition:"ALLOW"},{element:"heading",condition:"ALLOW"},{element:"svg",condition:"ALLOW"},{element:"svg-icon",condition:"ALLOW"},{element:"custom-code",condition:"ALLOW"}]},Component:ql,controls:{margin:!0,padding:!1},manualDelete:!1};function Ql(e){return Ql="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ql(e)}function Gl(){return Gl=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Gl.apply(this,arguments)}var $l=(0,$e.forwardRef)((function(e,t){var n=e.elementId,r=e.className,i=Qe.i.getCanvasElement(n);return $e.createElement("div",Gl({},Qe.i.getAllAttributes(i),{ref:t,className:r,"data-element_hide":"has-gradebook"!==Fl(i.parentId)}),$e.createElement("img",{src:"https://placehold.co/848X300?text=Gradebook Placeholder"}))}));const Jl={name:Ge+"-has-gradebook",type:"element",source:Ge,className:"",title:"Has Gradebook",description:"Has Gradebook",visibility:!1,properties:{tag:"div",attributes:function(e,t,n){return(t=function(e){var t=function(e,t){if("object"!==Ql(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==Ql(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===Ql(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}({},"data-state","has-gradebook")},children:[],constraints:{childrens:[{element:"text",condition:"ALLOW"},{element:"heading",condition:"ALLOW"},{element:"svg",condition:"ALLOW"},{element:"svg-icon",condition:"ALLOW"},{element:"custom-code",condition:"ALLOW"}]},Component:$l,controls:{margin:!0,padding:!1},manualDelete:!1};var Kl;console.log(Qe.i),Qe.i.RegisterElement(Zt),Qe.i.RegisterElement(Dt),Qe.i.RegisterElement(zt),Qe.i.RegisterElement(ot),Qe.i.RegisterElement(yt),Qe.i.RegisterElement(mt),Qe.i.RegisterElement(Ot),Qe.i.RegisterElement(jt),Qe.i.RegisterElement(tn),Qe.i.RegisterElement(Fn),Qe.i.RegisterElement(Hn),Qe.i.RegisterElement(Jn),Qe.i.RegisterElement(nr),Qe.i.RegisterElement(or),Qe.i.RegisterElement(cr),Qe.i.RegisterElement(fr),Qe.i.RegisterElement(sn),Qe.i.RegisterElement(mn),Qe.i.RegisterElement(bn),Qe.i.RegisterElement(xn),Qe.i.RegisterElement(Nn),Qe.i.RegisterElement(kn),Qe.i.RegisterElement(hr),Qe.i.RegisterElement(Er),Qe.i.RegisterElement(xr),Qe.i.RegisterElement(Zr),Qe.i.RegisterElement(Kr),Qe.i.RegisterElement(ti),Qe.i.RegisterElement(ri),Qe.i.RegisterElement(oi),Qe.i.RegisterElement(si),Qe.i.RegisterElement(ci),Qe.i.RegisterElement(pi),Qe.i.RegisterElement(Ei),Qe.i.RegisterElement(fi),Qe.i.RegisterElement(gi),Qe.i.RegisterElement(bi),Qe.i.RegisterElement(wi),Qe.i.RegisterElement(vi),Qe.i.RegisterElement(Ni),Qe.i.RegisterElement(ki),Qe.i.RegisterElement(Fi),Qe.i.RegisterElement(Bi),Qe.i.RegisterElement(no),Qe.i.RegisterElement(ao),Qe.i.RegisterElement(mo),Qe.i.RegisterElement(bo),Qe.i.RegisterElement(wo),Qe.i.RegisterElement(Eo),Qe.i.RegisterElement(Co),Qe.i.RegisterElement(Po),Qe.i.RegisterElement(Mo),Qe.i.RegisterElement(Zo),Qe.i.RegisterElement(Io),Qe.i.RegisterElement(Do),Qe.i.RegisterElement(Wo),Qe.i.RegisterElement(zo),Qe.i.RegisterElement(Qo),Qe.i.RegisterElement(Xo),Qe.i.RegisterElement(ra),Qe.i.RegisterElement(la),Qe.i.RegisterElement(ma),Qe.i.RegisterElement(da),Qe.i.RegisterElement(ya),Qe.i.RegisterElement(is),Qe.i.RegisterElement(La),Qe.i.RegisterElement(Ma),Qe.i.RegisterElement(Ia),Qe.i.RegisterElement(Da),Qe.i.RegisterElement(Wa),Qe.i.RegisterElement(Ha),Qe.i.RegisterElement(Va),Qe.i.RegisterElement(Ga),Qe.i.RegisterElement(as),Qe.i.RegisterElement(ls),Qe.i.RegisterElement(ps),Qe.i.RegisterElement(fs),Qe.i.RegisterElement(bs),Qe.i.RegisterElement(vs),Qe.i.RegisterElement(kr),Qe.i.RegisterElement(_r),Qe.i.RegisterElement(Fr),Qe.i.RegisterElement(Ur),Qe.i.RegisterElement(Hr),Qe.i.RegisterElement(Vr),Qe.i.RegisterElement(xs),Qe.i.RegisterElement(ks),Qe.i.RegisterElement(Ns),Qe.i.RegisterElement(Ws),Qe.i.RegisterElement(Us),Qe.i.RegisterElement(qs),Qe.i.RegisterElement(Gs),Qe.i.RegisterElement(Ks),Qe.i.RegisterElement(el),Qe.i.RegisterElement(rl),Qe.i.RegisterElement(al),Qe.i.RegisterElement(bl),Qe.i.RegisterElement(wl),Qe.i.RegisterElement(Cl),Qe.i.RegisterElement(Ll),Qe.i.RegisterElement(Pl),Qe.i.RegisterElement(Ml),Qe.i.RegisterElement(Zl),Qe.i.RegisterElement(Ul),Qe.i.RegisterElement(Vl),Qe.i.RegisterElement(Jl),Kl={name:"tde",title:"LMS",options:{},accessibleTypes:["full","content","view"],shortcut:""},Ve.get(wp_droip.ajaxUrl,{params:{action:"tde_get_apis",endpoint:"get-pages"}}).then((function(e){var t=e.data.data;t=t.map((function(e){return e.post_type===Ge+"-courses-template"?e.preview_url=wp_droip.siteUrl+"/courses":e.post_type===Ge+"-course-template"&&(e.preview_url=wp_droip.siteUrl+"/course"),e})),console.log({pages:t}),Qe.i.addNewPagesList(Ge,Kl,t)})).catch((function(e){console.log(e)}))},2408:(e,t)=>{var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),m=Symbol.for("react.lazy"),f=Symbol.iterator,d={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},g=Object.assign,y={};function b(e,t,n){this.props=e,this.context=t,this.refs=y,this.updater=n||d}function h(){}function v(e,t,n){this.props=e,this.context=t,this.refs=y,this.updater=n||d}b.prototype.isReactComponent={},b.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},b.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},h.prototype=b.prototype;var O=v.prototype=new h;O.constructor=v,g(O,b.prototype),O.isPureReactComponent=!0;var w=Array.isArray,S=Object.prototype.hasOwnProperty,E={current:null},j={key:!0,ref:!0,__self:!0,__source:!0};function C(e,t,r){var i,o={},a=null,s=null;if(null!=t)for(i in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(a=""+t.key),t)S.call(t,i)&&!j.hasOwnProperty(i)&&(o[i]=t[i]);var l=arguments.length-2;if(1===l)o.children=r;else if(1<l){for(var c=Array(l),u=0;u<l;u++)c[u]=arguments[u+2];o.children=c}if(e&&e.defaultProps)for(i in l=e.defaultProps)void 0===o[i]&&(o[i]=l[i]);return{$$typeof:n,type:e,key:a,ref:s,props:o,_owner:E.current}}function A(e){return"object"==typeof e&&null!==e&&e.$$typeof===n}var L=/\/+/g;function x(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function P(e,t,i,o,a){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l=!1;if(null===e)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case n:case r:l=!0}}if(l)return a=a(l=e),e=""===o?"."+x(l,0):o,w(a)?(i="",null!=e&&(i=e.replace(L,"$&/")+"/"),P(a,t,i,"",(function(e){return e}))):null!=a&&(A(a)&&(a=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(a,i+(!a.key||l&&l.key===a.key?"":(""+a.key).replace(L,"$&/")+"/")+e)),t.push(a)),1;if(l=0,o=""===o?".":o+":",w(e))for(var c=0;c<e.length;c++){var u=o+x(s=e[c],c);l+=P(s,t,i,u,a)}else if(u=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=f&&e[f]||e["@@iterator"])?e:null}(e),"function"==typeof u)for(e=u.call(e),c=0;!(s=e.next()).done;)l+=P(s=s.value,t,i,u=o+x(s,c++),a);else if("object"===s)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function R(e,t,n){if(null==e)return e;var r=[],i=0;return P(e,r,"","",(function(e){return t.call(n,e,i++)})),r}function M(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var N={current:null},Z={transition:null},T={ReactCurrentDispatcher:N,ReactCurrentBatchConfig:Z,ReactCurrentOwner:E};t.Children={map:R,forEach:function(e,t,n){R(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return R(e,(function(){t++})),t},toArray:function(e){return R(e,(function(e){return e}))||[]},only:function(e){if(!A(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=b,t.Fragment=i,t.Profiler=a,t.PureComponent=v,t.StrictMode=o,t.Suspense=u,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=T,t.cloneElement=function(e,t,r){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var i=g({},e.props),o=e.key,a=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(a=t.ref,s=E.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(c in t)S.call(t,c)&&!j.hasOwnProperty(c)&&(i[c]=void 0===t[c]&&void 0!==l?l[c]:t[c])}var c=arguments.length-2;if(1===c)i.children=r;else if(1<c){l=Array(c);for(var u=0;u<c;u++)l[u]=arguments[u+2];i.children=l}return{$$typeof:n,type:e.type,key:o,ref:a,props:i,_owner:s}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:s,_context:e},e.Consumer=e},t.createElement=C,t.createFactory=function(e){var t=C.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=A,t.lazy=function(e){return{$$typeof:m,_payload:{_status:-1,_result:e},_init:M}},t.memo=function(e,t){return{$$typeof:p,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=Z.transition;Z.transition={};try{e()}finally{Z.transition=t}},t.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")},t.useCallback=function(e,t){return N.current.useCallback(e,t)},t.useContext=function(e){return N.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return N.current.useDeferredValue(e)},t.useEffect=function(e,t){return N.current.useEffect(e,t)},t.useId=function(){return N.current.useId()},t.useImperativeHandle=function(e,t,n){return N.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return N.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return N.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return N.current.useMemo(e,t)},t.useReducer=function(e,t,n){return N.current.useReducer(e,t,n)},t.useRef=function(e){return N.current.useRef(e)},t.useState=function(e){return N.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return N.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return N.current.useTransition()},t.version="18.2.0"},7294:(e,t,n)=>{e.exports=n(2408)}},t={};function n(r){var i=t[r];if(void 0!==i)return i.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n(3994)})();