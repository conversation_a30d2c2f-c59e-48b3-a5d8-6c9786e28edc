"use strict";(self["webpackChunktutor_pro"]=self["webpackChunktutor_pro"]||[]).push([[7770],{3195:(t,e,r)=>{r.r(e);r.d(e,{default:()=>Ih});var n=r(6657);var o=r(3297);var i=r(9640);var a=r(8003);var u=r(7363);var l=r.n(u);var c=r(7536);var s=r(960);var d=r(7935);var f=r(9660);var p=r(4805);var v=r(5885);var h=r(1605);var b=r(7976);var y=r(1457);function g(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var m=l().forwardRef((function(t,e){var r=t.id,o=r===void 0?(0,y.x0)():r,i=t.name,a=t.labelCss,u=t.inputCss,l=t.label,c=l===void 0?"":l,s=t.checked,d=t.value,f=t.disabled,p=f===void 0?false:f,v=t.onChange,h=t.onBlur,b=t.isIndeterminate,g=b===void 0?false:b;var m=function t(e){v===null||v===void 0?void 0:v(!g?e.target.checked:true,e)};return(0,n.tZ)("label",{htmlFor:o,css:[x.container({disabled:p}),a,true?"":0,true?"":0]},(0,n.tZ)("input",{ref:e,id:o,name:i,type:"checkbox",value:d,checked:!!s,disabled:p,"aria-invalid":t["aria-invalid"],onChange:m,onBlur:h,css:[u,x.checkbox({label:!!c,isIndeterminate:g,disabled:p}),true?"":0,true?"":0]}),(0,n.tZ)("span",null),(0,n.tZ)("span",{css:[x.label({isDisabled:p}),a,true?"":0,true?"":0],title:c===null||c===void 0?void 0:c.toString()},c))}));var w=true?{name:"1sfig4b",styles:"cursor:not-allowed"}:0;var x={container:function t(e){var r=e.disabled,o=r===void 0?false:r;return(0,n.iv)("position:relative;display:flex;align-items:center;cursor:pointer;user-select:none;color:",h.Jv.text.title,";",o&&w,";"+(true?"":0),true?"":0)},label:function t(e){var r=e.isDisabled,o=r===void 0?false:r;return(0,n.iv)(b.c.caption(),";margin-top:",h.W0[2],";color:",h.Jv.text.title,";",o&&(0,n.iv)("color:",h.Jv.text.disable,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},checkbox:function t(e){var r=e.label,o=e.isIndeterminate,i=e.disabled;return(0,n.iv)("position:absolute;opacity:0!important;height:0;width:0;&+span{position:relative;cursor:pointer;display:inline-flex;align-items:center;",r&&(0,n.iv)("margin-right:",h.W0[10],";"+(true?"":0),true?"":0),";}&+span::before{content:'';background-color:",h.Jv.background.white,";border:1px solid ",h.Jv.stroke["default"],";border-radius:3px;width:20px;height:20px;}&:checked+span::before{background-image:url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0wLjE2NTM0NCA0Ljg5OTQ2QzAuMTEzMjM1IDQuODQ0OTcgMC4wNzE3MzQ2IDQuNzgxMTUgMC4wNDI5ODg3IDQuNzExM0MtMC4wMTQzMjk2IDQuNTU1NjQgLTAuMDE0MzI5NiA0LjM4NDQ5IDAuMDQyOTg4NyA0LjIyODg0QzAuMDcxMTU0OSA0LjE1ODY4IDAuMTEyNzIzIDQuMDk0NzUgMC4xNjUzNDQgNC4wNDA2OEwxLjAzMzgyIDMuMjAzNkMxLjA4NDkzIDMuMTQzNCAxLjE0ODkgMy4wOTU1NyAxLjIyMDk2IDMuMDYzNjlDMS4yOTAzMiAzLjAzMjEzIDEuMzY1NTQgMy4wMTU2OSAxLjQ0MTY3IDMuMDE1NDRDMS41MjQxOCAzLjAxMzgzIDEuNjA2MDUgMy4wMzAyOSAxLjY4MTU5IDMuMDYzNjlDMS43NTYyNiAzLjA5NzA3IDEuODIzODYgMy4xNDQ1NyAxLjg4MDcxIDMuMjAzNkw0LjUwMDU1IDUuODQyNjhMMTAuMTI0MSAwLjE4ODIwNUMxMC4xNzk0IDAuMTI5NTQ0IDEwLjI0NTQgMC4wODIwNTQyIDEwLjMxODQgMC4wNDgyOTA4QzEwLjM5NDEgMC4wMTU0NjYxIDEwLjQ3NTkgLTAuMDAwOTcyMDU3IDEwLjU1ODMgNC40NDIyOGUtMDVDMTAuNjM1NyAwLjAwMDQ3NTMxOCAxMC43MTIxIDAuMDE3NDc5NSAxMC43ODI0IDAuMDQ5OTI0MkMxMC44NTI3IDAuMDgyMzY4OSAxMC45MTU0IDAuMTI5NTA5IDEwLjk2NjIgMC4xODgyMDVMMTEuODM0NyAxLjAzNzM0QzExLjg4NzMgMS4wOTE0MiAxMS45Mjg4IDEuMTU1MzQgMTEuOTU3IDEuMjI1NUMxMi4wMTQzIDEuMzgxMTYgMTIuMDE0MyAxLjU1MjMxIDExLjk1NyAxLjcwNzk2QzExLjkyODMgMS43Nzc4MSAxMS44ODY4IDEuODQxNjMgMTEuODM0NyAxLjg5NjEzTDQuOTIyOCA4LjgwOTgyQzQuODcxMjkgOC44NzAyMSA0LjgwNzQ3IDguOTE4NzUgNC43MzU2NiA4Ljk1MjE1QzQuNTgyMDIgOS4wMTU5NSA0LjQwOTQ5IDkuMDE1OTUgNC4yNTU4NCA4Ljk1MjE1QzQuMTg0MDQgOC45MTg3NSA0LjEyMDIyIDguODcwMjEgNC4wNjg3MSA4LjgwOTgyTDAuMTY1MzQ0IDQuODk5NDZaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K');background-repeat:no-repeat;background-size:10px 10px;background-position:center center;border-color:transparent;background-color:",h.Jv.icon.brand,";border-radius:",h.E0[4],";",i&&(0,n.iv)("background-color:",h.Jv.icon.disable["default"],";"+(true?"":0),true?"":0),";}",o&&(0,n.iv)("&+span::before{background-image:url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='2' fill='none'%3E%3Crect width='10' height='1.5' y='.25' fill='%23fff' rx='.75'/%3E%3C/svg%3E\");background-repeat:no-repeat;background-size:10px;background-position:center center;background-color:",h.Jv.brand.blue,";border:0.5px solid ",h.Jv.stroke.white,";}"+(true?"":0),true?"":0)," ",i&&(0,n.iv)("&+span{cursor:not-allowed;&::before{border-color:",h.Jv.stroke.disable,";}}"+(true?"":0),true?"":0)," &:focus-visible{&+span{border-radius:",h.E0[2],";outline:2px solid ",h.Jv.stroke.brand,";outline-offset:1px;}}"+(true?"":0),true?"":0)}};const _=m;var Z=r(8015);var O=r(1007);var S=r(276);var k=r(2749);var j=r(4033);function E(t,e){return I(t)||W(t,e)||A(t,e)||C()}function C(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function A(t,e){if(!t)return;if(typeof t==="string")return P(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return P(t,e)}function P(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function W(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function I(t){if(Array.isArray(t))return t}var L=function t(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:"";return(0,u.useMemo)((function(){var t;if(!(0,j.$K)(e)){return true}var r=(e===null||e===void 0?void 0:e.split("."))||[],n=E(r,2),o=n[0],i=n[1];if(!(0,j.$K)(o)||!(0,j.$K)(i)){return true}var a=k.y===null||k.y===void 0?void 0:(t=k.y.visibility_control)===null||t===void 0?void 0:t[o];if(!a){return true}var u=k.y.current_user.roles;var l=u.includes("administrator")?"admin":"instructor";var c="".concat(i,"_").concat(l);if(!Object.keys(a).includes(c)){return true}return a[c]==="on"}),[e])};const D=L;var T=["visibilityKey"];function J(t,e){if(t==null)return{};var r=M(t,e);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(o=0;o<i.length;o++){n=i[o];if(e.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(t,n))continue;r[n]=t[n]}}return r}function M(t,e){if(t==null)return{};var r={};var n=Object.keys(t);var o,i;for(i=0;i<n.length;i++){o=n[i];if(e.indexOf(o)>=0)continue;r[o]=t[o]}return r}var N=function t(e){return function(t){var r=t.visibilityKey,o=J(t,T);var i=D(r);if(!i){return null}return(0,n.tZ)(e,o)}};function F(t,e){return G(t)||U(t,e)||z(t,e)||B()}function B(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function z(t,e){if(!t)return;if(typeof t==="string")return R(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return R(t,e)}function R(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function U(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function G(t){if(Array.isArray(t))return t}var Q=function t(e){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:300;var n=(0,u.useState)(e),o=F(n,2),i=o[0],a=o[1];(0,u.useEffect)((function(){var t=setTimeout((function(){a(e)}),r);return function(){clearTimeout(t)}}),[e,r]);return i};function Y(t){"@babel/helpers - typeof";return Y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Y(t)}function q(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function H(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?q(Object(r),!0).forEach((function(e){V(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):q(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function V(t,e,r){e=$(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function $(t){var e=K(t,"string");return Y(e)==="symbol"?e:String(e)}function K(t,e){if(Y(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Y(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function X(t,e){return ot(t)||nt(t,e)||et(t,e)||tt()}function tt(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function et(t,e){if(!t)return;if(typeof t==="string")return rt(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rt(t,e)}function rt(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function nt(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function ot(t){if(Array.isArray(t))return t}var it=function t(e){var r=(0,u.useState)(),n=X(r,2),o=n[0],i=n[1];var a=(0,p.cI)(e);return H(H({},a),{},{submitError:o,setSubmitError:i})};function at(t){"@babel/helpers - typeof";return at="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},at(t)}function ut(t,e){return ft(t)||dt(t,e)||ct(t,e)||lt()}function lt(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ct(t,e){if(!t)return;if(typeof t==="string")return st(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return st(t,e)}function st(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function dt(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function ft(t){if(Array.isArray(t))return t}function pt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function vt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pt(Object(r),!0).forEach((function(e){ht(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pt(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ht(t,e,r){e=bt(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function bt(t){var e=yt(t,"string");return at(e)==="symbol"?e:String(e)}function yt(t,e){if(at(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(at(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var gt={defaultValue:false};var mt=function t(e){var r=(0,u.useRef)(null);var n=vt(vt({},gt),e);var o=(0,u.useState)(n.defaultValue),i=ut(o,2),a=i[0],l=i[1];(0,u.useEffect)((function(){if(!(0,j.$K)(r.current)){return}if(r.current.scrollHeight<=r.current.clientHeight){l(false);return}var t=function t(e){var r=e.target;if(r.scrollTop+r.clientHeight>=r.scrollHeight){l(false);return}l(r.scrollTop>=0)};r.current.addEventListener("scroll",t);return function(){var e;(e=r.current)===null||e===void 0?void 0:e.removeEventListener("scroll",t)}}),[r.current]);return{ref:r,isScrolling:a}};var wt=r(4428);var xt=r(368);var _t=r(5332);var Zt=r(7855);var Ot=r(7837);var St=r(3639);var kt=function t(e){return Ot.B.get(St.Z.CATEGORIES,e?{params:{per_page:100,search:e}}:{params:{per_page:100}})};var jt=function t(e){return(0,xt.a)({queryKey:["CategoryList",e],queryFn:function t(){return kt(e).then((function(t){return t.data}))}})};var Et=function t(e){return Ot.B.post(St.Z.CATEGORIES,e)};var Ct=function t(){var e=(0,o.NL)();var r=(0,Zt.p)(),n=r.showToast;return(0,_t.D)({mutationFn:Et,onSuccess:function t(){e.invalidateQueries({queryKey:["CategoryList"]})},onError:function t(e){n({type:"danger",message:(0,y.Mo)(e)})}})};var At=r(7901);var Pt=r(4329);function Wt(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var It=function t(e){var r;var o=e.field,i=e.fieldState,u=e.children,l=e.disabled,c=l===void 0?false:l,s=e.readOnly,f=s===void 0?false:s,p=e.label,v=e.isInlineLabel,b=v===void 0?false:v,g=e.variant,m=e.loading,w=e.placeholder,x=e.helpText,_=e.isHidden,O=_===void 0?false:_,k=e.removeBorder,E=k===void 0?false:k,C=e.characterCount,A=e.isSecondary,P=A===void 0?false:A,W=e.inputStyle,I=e.onClickAiButton,L=e.isMagicAi,D=L===void 0?false:L,T=e.generateWithAi,J=T===void 0?false:T,M=e.replaceEntireLabel,N=M===void 0?false:M;var F=(0,y.x0)();var B=[Ft.input({variant:g,hasFieldError:!!i.error,removeBorder:E,readOnly:f,hasHelpText:!!x,isSecondary:P,isMagicAi:D})];if((0,j.$K)(W)){B.push(W)}var z=(0,n.tZ)("div",{css:Ft.inputWrapper},u({id:F,name:o.name,css:B,"aria-invalid":i.error?"true":"false",disabled:c,readOnly:f,placeholder:w,className:"tutor-input-field"}),m&&(0,n.tZ)("div",{css:Ft.loader},(0,n.tZ)(Z.ZP,{size:20,color:h.Jv.icon["default"]})));return(0,n.tZ)("div",{css:Ft.container({disabled:c,isHidden:O}),"data-cy":"form-field-wrapper"},(0,n.tZ)("div",{css:Ft.inputContainer(b)},(p||x)&&(0,n.tZ)("div",{css:Ft.labelContainer},p&&(0,n.tZ)("label",{htmlFor:F,css:Ft.label(b,N)},p,(0,n.tZ)(S.Z,{when:J},(0,n.tZ)("button",{type:"button",onClick:function t(){I===null||I===void 0?void 0:I()},css:Ft.aiButton},(0,n.tZ)(d.Z,{name:"magicAiColorize",width:32,height:32})))),x&&!N&&(0,n.tZ)(Pt.Z,{content:x,placement:"top",allowHTML:true},(0,n.tZ)(d.Z,{name:"info",width:20,height:20}))),C?(0,n.tZ)(Pt.Z,{placement:"right",hideOnClick:false,content:C.maxLimit-C.inputCharacter>=0?C.maxLimit-C.inputCharacter:(0,a.__)("Limit exceeded","tutor")},z):z),((r=i.error)===null||r===void 0?void 0:r.message)&&(0,n.tZ)("p",{css:Ft.errorLabel(!!i.error,b)},(0,n.tZ)(d.Z,{style:Ft.alertIcon,name:"info",width:20,height:20})," ",i.error.message))};const Lt=It;var Dt=true?{name:"jab4lt",styles:"justify-content:end"}:0;var Tt=true?{name:"1oqqdjf",styles:"border-color:transparent"}:0;var Jt=true?{name:"ilexii",styles:"border-radius:0;border:none;box-shadow:none"}:0;var Mt=true?{name:"eivff4",styles:"display:none"}:0;var Nt=true?{name:"o9ww1u",styles:"opacity:0.5"}:0;var Ft={container:function t(e){var r=e.disabled,o=e.isHidden;return(0,n.iv)("display:flex;flex-direction:column;position:relative;background:inherit;width:100%;",r&&Nt," ",o&&Mt,";"+(true?"":0),true?"":0)},inputContainer:function t(e){return(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[4],";width:100%;",e&&(0,n.iv)("flex-direction:row;align-items:center;justify-content:space-between;gap:",h.W0[12],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},input:function t(e){return(0,n.iv)("&.tutor-input-field{",b.c.body("regular"),";width:100%;border-radius:",h.E0[6],";border:1px solid ",h.Jv.stroke["default"],";padding:",h.W0[8]," ",h.W0[16],";color:",h.Jv.text.title,";appearance:textfield;",e.hasFieldError&&(0,n.iv)("border-color:",h.Jv.stroke.danger,";background-color:",h.Jv.background.status.errorFail,";"+(true?"":0),true?"":0)," ",e.readOnly&&(0,n.iv)("border-color:",h.Jv.background.disable,";background-color:",h.Jv.background.disable,";"+(true?"":0),true?"":0),";&:not(textarea){height:40px;}",e.hasHelpText&&(0,n.iv)("padding:0 ",h.W0[32]," 0 ",h.W0[12],";"+(true?"":0),true?"":0)," ",e.removeBorder&&Jt," ",e.isSecondary&&Tt," :focus{",At.i.inputFocus,";",e.isMagicAi&&(0,n.iv)("outline-color:",h.Jv.stroke.magicAi,";background-color:",h.Jv.background.magicAi[8],";"+(true?"":0),true?"":0)," ",e.hasFieldError&&(0,n.iv)("border-color:",h.Jv.stroke.danger,";"+(true?"":0),true?"":0),";}::-webkit-outer-spin-button,::-webkit-inner-spin-button{-webkit-appearance:none;margin:0;}::placeholder{",b.c.caption("regular"),";color:",h.Jv.text.hints,";",e.isSecondary&&(0,n.iv)("color:",h.Jv.text.hints,";"+(true?"":0),true?"":0),";}}"+(true?"":0),true?"":0)},errorLabel:function t(e,r){return(0,n.iv)(b.c.small(),";line-height:",h.Nv[20],";display:flex;align-items:start;margin-top:",h.W0[4],";",r&&Dt," ",e&&(0,n.iv)("color:",h.Jv.text.status.onHold,";"+(true?"":0),true?"":0)," & svg{margin-right:",h.W0[2],";transform:rotate(180deg);}"+(true?"":0),true?"":0)},labelContainer:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[4],";>div{display:flex;color:",h.Jv.color.black[30],";}"+(true?"":0),true?"":0),label:function t(e,r){return(0,n.iv)(b.c.caption(),";margin:0px;width:",r?"100%":"auto",";color:",h.Jv.text.title,";display:flex;align-items:center;gap:",h.W0[4],";",e&&(0,n.iv)(b.c.caption(),";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},aiButton:(0,n.iv)(At.i.resetButton,";width:32px;height:32px;border-radius:",h.E0[4],";display:flex;align-items:center;justify-content:center;:disabled{cursor:not-allowed;}&:focus,&:active,&:hover{background:none;}&:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";}"+(true?"":0),true?"":0),inputWrapper:true?{name:"bjn8wh",styles:"position:relative"}:0,loader:(0,n.iv)("position:absolute;top:50%;right:",h.W0[12],";transform:translateY(-50%);display:flex;"+(true?"":0),true?"":0),alertIcon:true?{name:"ozd7xs",styles:"flex-shrink:0"}:0};var Bt=r(5114);var zt=["className","variant","size","children","type","disabled","roundedFull","loading"];function Rt(){Rt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Rt.apply(this,arguments)}function Ut(t,e){if(t==null)return{};var r=Gt(t,e);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(o=0;o<i.length;o++){n=i[o];if(e.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(t,n))continue;r[n]=t[n]}}return r}function Gt(t,e){if(t==null)return{};var r={};var n=Object.keys(t);var o,i;for(i=0;i<n.length;i++){o=n[i];if(e.indexOf(o)>=0)continue;r[o]=t[o]}return r}function Qt(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Yt=l().forwardRef((function(t,e){var r=t.className,o=t.variant,i=t.size,a=t.children,u=t.type,l=u===void 0?"button":u,c=t.disabled,s=c===void 0?false:c,d=t.roundedFull,f=d===void 0?true:d,p=t.loading,v=Ut(t,zt);return(0,n.tZ)("button",Rt({type:l,ref:e,css:Vt({variant:o,size:i,rounded:f?"true":"false"}),className:r,disabled:s},v),(0,n.tZ)("span",{css:Ht.buttonSpan},p?(0,n.tZ)(Z.ZP,{size:24}):a))}));const qt=Yt;var Ht={buttonSpan:(0,n.iv)(At.i.flexCenter(),";z-index:",h.W5.positive,";"+(true?"":0),true?"":0),base:(0,n.iv)(At.i.resetButton,";",b.c.small("medium"),";display:flex;gap:",h.W0[4],";width:100%;justify-content:center;align-items:center;white-space:nowrap;position:relative;overflow:hidden;transition:box-shadow 0.5s ease;&:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";outline-offset:1px;}&:disabled{cursor:not-allowed;background:",h.Jv.action.primary.disable,";pointer-events:none;color:",h.Jv.text.disable,";border-color:",h.Jv.stroke.disable,";}"+(true?"":0),true?"":0),default:function t(e){return(0,n.iv)("background:",!e?h.Jv.ai.gradient_1:h.Jv.ai.gradient_1_rtl,";color:",h.Jv.text.white,";&::before{content:'';position:absolute;inset:0;background:",!e?h.Jv.ai.gradient_2:h.Jv.ai.gradient_2_rtl,";opacity:0;transition:opacity 0.5s ease;}&:hover::before{opacity:1;}"+(true?"":0),true?"":0)},secondary:(0,n.iv)("background-color:",h.Jv.action.secondary["default"],";color:",h.Jv.text.brand,";border-radius:",h.E0[6],";&:hover{background-color:",h.Jv.action.secondary.hover,";}"+(true?"":0),true?"":0),outline:(0,n.iv)("position:relative;&::before{content:'';position:absolute;inset:0;background:",h.Jv.ai.gradient_1,";color:",h.Jv.text.primary,";border:1px solid transparent;-webkit-mask:linear-gradient(#fff 0 0) padding-box,linear-gradient(#fff 0 0);mask:linear-gradient(#fff 0 0) padding-box,linear-gradient(#fff 0 0);-webkit-mask-composite:xor;mask-composite:exclude;}&:hover{&::before{background:",h.Jv.ai.gradient_2,";}}"+(true?"":0),true?"":0),primaryOutline:(0,n.iv)("border:1px solid ",h.Jv.brand.blue,";color:",h.Jv.brand.blue,";&:hover{background-color:",h.Jv.brand.blue,";color:",h.Jv.text.white,";}"+(true?"":0),true?"":0),primary:(0,n.iv)("background-color:",h.Jv.brand.blue,";color:",h.Jv.text.white,";"+(true?"":0),true?"":0),ghost:(0,n.iv)("background-color:transparent;color:",h.Jv.text.subdued,";border-radius:",h.E0[4],";&:hover{color:",h.Jv.text.primary,";}"+(true?"":0),true?"":0),plain:(0,n.iv)("span{background:",!O.dZ?h.Jv.text.ai.gradient:h.Jv.ai.gradient_1_rtl,";background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;&:hover{background:",!O.dZ?h.Jv.ai.gradient_2:h.Jv.ai.gradient_2_rtl,";background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;}}"+(true?"":0),true?"":0),size:{default:(0,n.iv)("height:32px;padding-inline:",h.W0[12],";padding-block:",h.W0[4],";"+(true?"":0),true?"":0),sm:(0,n.iv)("height:24px;padding-inline:",h.W0[10],";"+(true?"":0),true?"":0),icon:true?{name:"68x97p",styles:"width:32px;height:32px"}:0},rounded:{true:(0,n.iv)("border-radius:",h.E0[54],";&::before{border-radius:",h.E0[54],";}"+(true?"":0),true?"":0),false:(0,n.iv)("border-radius:",h.E0[4],";&::before{border-radius:",h.E0[4],";}"+(true?"":0),true?"":0)}};var Vt=(0,Bt.Y)({variants:{variant:{default:Ht["default"](O.dZ),primary:Ht.primary,secondary:Ht.secondary,outline:Ht.outline,primary_outline:Ht.primaryOutline,ghost:Ht.ghost,plain:Ht.plain},size:{default:Ht.size["default"],sm:Ht.size.sm,icon:Ht.size.icon},rounded:{true:Ht.rounded["true"],false:Ht.rounded["false"]}},defaultVariants:{variant:"default",size:"default",rounded:"true"}},Ht.base);var $t=r(5404);var Kt=r(7363);function Xt(){Xt=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Xt.apply(this,arguments)}function te(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var ee=6;var re=function t(e){var r;var o=e.label,i=e.rows,a=i===void 0?ee:i,l=e.columns,c=e.maxLimit,s=e.field,d=e.fieldState,f=e.disabled,p=e.readOnly,v=e.loading,h=e.placeholder,b=e.helpText,y=e.onChange,g=e.onKeyDown,m=e.isHidden,w=e.enableResize,x=w===void 0?true:w,_=e.isSecondary,Z=_===void 0?false:_,O=e.isMagicAi,S=O===void 0?false:O,k=e.inputCss,j=e.maxHeight,E=e.autoResize,C=E===void 0?false:E;var A=(r=s.value)!==null&&r!==void 0?r:"";var P=(0,u.useRef)(null);var W=undefined;if(c){W={maxLimit:c,inputCharacter:A.toString().length}}var I=function t(){if(P.current){if(j){P.current.style.maxHeight="".concat(j,"px")}P.current.style.height="auto";P.current.style.height="".concat(P.current.scrollHeight,"px")}};(0,u.useLayoutEffect)((function(){if(C){I()}}),[]);return(0,n.tZ)(Lt,{label:o,field:s,fieldState:d,disabled:f,readOnly:p,loading:v,placeholder:h,helpText:b,isHidden:m,characterCount:W,isSecondary:Z,isMagicAi:S},(function(t){return(0,n.tZ)(Kt.Fragment,null,(0,n.tZ)("div",{css:ie.container(x,k)},(0,n.tZ)("textarea",Xt({},s,t,{ref:function t(e){s.ref(e);P.current=e},style:{maxHeight:j?"".concat(j,"px"):"none"},className:"tutor-input-field",value:A,onChange:function t(e){var r=e.target.value;if(c&&r.trim().length>c){return}s.onChange(r);if(y){y(r)}if(C){I()}},onKeyDown:function t(e){g===null||g===void 0?void 0:g(e.key)},autoComplete:"off",rows:a,cols:l}))))}))};const ne=N(re);var oe=true?{name:"1dz94pb",styles:"resize:vertical"}:0;var ie={container:function t(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;var r=arguments.length>1?arguments[1]:undefined;return(0,n.iv)("position:relative;display:flex;textarea{",b.c.body(),";height:auto;padding:",h.W0[8]," ",h.W0[12],";resize:none;",e&&oe,";&.tutor-input-field{",r,";}}"+(true?"":0),true?"":0)}};var ae=function t(e){var r=e.each,n=e.children,o=e.fallback,i=o===void 0?null:o;if(r.length===0){return i}return r.map((function(t,e){return n(t,e)}))};const ue=ae;var le=function t(e){var r=e.options,o=e.onChange;return(0,n.tZ)("div",{css:ce.wrapper},(0,n.tZ)(ue,{each:r},(function(t,e){return(0,n.tZ)("button",{type:"button",key:e,onClick:function e(){return o(t.value)},css:ce.item},t.label)})))};var ce={wrapper:(0,n.iv)("display:flex;flex-direction:column;padding-block:",h.W0[8],";max-height:400px;overflow-y:auto;"+(true?"":0),true?"":0),item:(0,n.iv)(At.i.resetButton,";",b.c.caption(),";width:100%;padding:",h.W0[4]," ",h.W0[16],";color:",h.Jv.text.subdued,";display:flex;align-items:center;&:hover{background-color:",h.Jv.background.hover,";color:",h.Jv.text.title,";}"+(true?"":0),true?"":0)};function se(t,e){return he(t)||ve(t,e)||fe(t,e)||de()}function de(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function fe(t,e){if(!t)return;if(typeof t==="string")return pe(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pe(t,e)}function pe(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ve(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function he(t){if(Array.isArray(t))return t}var be=function t(e){var r=e.options,n=e.isOpen,o=e.onSelect,i=e.onClose,a=e.selectedValue;var l=(0,u.useState)(-1),c=se(l,2),s=c[0],d=c[1];var f=(0,u.useCallback)((function(t){if(!n)return;var e=function t(e,n){var o;var i=e;var a=n==="down"?1:-1;do{i+=a;if(i<0)i=r.length-1;if(i>=r.length)i=0}while(i>=0&&i<r.length&&r[i].disabled);if((o=r[i])!==null&&o!==void 0&&o.disabled){return e}return i};switch(t.key){case"ArrowDown":t.preventDefault();d((function(t){var r=e(t===-1?0:t,"down");return r}));break;case"ArrowUp":t.preventDefault();d((function(t){var r=e(t===-1?0:t,"up");return r}));break;case"Enter":t.preventDefault();t.stopPropagation();if(s>=0&&s<r.length){var a=r[s];if(!a.disabled){i();o(a)}}break;case"Escape":t.preventDefault();t.stopPropagation();i();break;default:break}}),[n,r,s,o,i]);(0,u.useEffect)((function(){if(n){if(s===-1){var t=r.findIndex((function(t){return t.value===a}));var e=t>=0?t:r.findIndex((function(t){return!t.disabled}));d(e)}document.addEventListener("keydown",f,true);return function(){return document.removeEventListener("keydown",f,true)}}}),[n,f,r,a,s]);(0,u.useEffect)((function(){if(!n){d(-1)}}),[n]);var p=(0,u.useCallback)((function(t){var e;if(!((e=r[t])!==null&&e!==void 0&&e.disabled)){d(t)}}),[r]);return{activeIndex:s,setActiveIndex:p}};function ye(t){"@babel/helpers - typeof";return ye="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ye(t)}var ge=["css"];function me(){me=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return me.apply(this,arguments)}function we(t,e){if(t==null)return{};var r=xe(t,e);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(o=0;o<i.length;o++){n=i[o];if(e.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(t,n))continue;r[n]=t[n]}}return r}function xe(t,e){if(t==null)return{};var r={};var n=Object.keys(t);var o,i;for(i=0;i<n.length;i++){o=n[i];if(e.indexOf(o)>=0)continue;r[o]=t[o]}return r}function _e(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ze(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?_e(Object(r),!0).forEach((function(e){Oe(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):_e(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Oe(t,e,r){e=Se(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Se(t){var e=ke(t,"string");return ye(e)==="symbol"?e:String(e)}function ke(t,e){if(ye(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(ye(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function je(t,e){return We(t)||Pe(t,e)||Ce(t,e)||Ee()}function Ee(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Ce(t,e){if(!t)return;if(typeof t==="string")return Ae(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ae(t,e)}function Ae(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Pe(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function We(t){if(Array.isArray(t))return t}function Ie(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Le=true?{name:"1d3w5wq",styles:"width:100%"}:0;var De=function t(e){var r;var o=e.options,i=e.field,l=e.fieldState,c=e.onChange,s=c===void 0?y.ZT:c,f=e.label,p=e.placeholder,h=p===void 0?"":p,b=e.disabled,g=e.readOnly,m=e.loading,w=e.isSearchable,x=w===void 0?false:w,_=e.isInlineLabel,Z=e.hideCaret,k=e.listLabel,E=e.isClearable,C=E===void 0?false:E,A=e.helpText,P=e.removeOptionsMinWidth,W=P===void 0?false:P,I=e.leftIcon,L=e.removeBorder,D=e.dataAttribute,T=e.isSecondary,J=T===void 0?false:T,M=e.isMagicAi,N=M===void 0?false:M,F=e.isAiOutline,B=F===void 0?false:F,z=e.selectOnFocus;var R=(0,u.useCallback)((function(){return o.find((function(t){return t.value===i.value}))||{label:"",value:"",description:""}}),[i.value,o]);var U=(0,u.useMemo)((function(){return o.some((function(t){return(0,j.$K)(t.description)}))}),[o]);var G=(0,u.useState)((r=R())===null||r===void 0?void 0:r.label),Q=je(G,2),Y=Q[0],q=Q[1];var H=(0,u.useState)(false),V=je(H,2),$=V[0],K=V[1];var X=(0,u.useState)(""),tt=je(X,2),et=tt[0],rt=tt[1];var nt=(0,u.useState)(false),ot=je(nt,2),it=ot[0],at=ot[1];var ut=(0,u.useRef)(null);var lt=(0,u.useRef)(null);var ct=(0,u.useRef)(null);var st=(0,u.useMemo)((function(){if(x){return o.filter((function(t){var e=t.label;return e.toLowerCase().includes(et.toLowerCase())}))}return o}),[et,x,o]);var dt=(0,u.useMemo)((function(){return o.find((function(t){return t.value===i.value}))}),[i.value,o]);var ft=(0,wt.l)({isOpen:it,isDropdown:true,dependencies:[st.length]}),pt=ft.triggerRef,vt=ft.triggerWidth,ht=ft.position,bt=ft.popoverRef;var yt=Ze({},(0,j.$K)(D)&&Oe({},D,true));(0,u.useEffect)((function(){var t;q((t=R())===null||t===void 0?void 0:t.label)}),[i.value,R]);(0,u.useEffect)((function(){if(it){var t;q((t=R())===null||t===void 0?void 0:t.label)}}),[R,it]);var gt=function t(e,r){r===null||r===void 0?void 0:r.stopPropagation();if(!e.disabled){i.onChange(e.value);s(e);rt("");K(false);at(false)}};var mt=be({options:st,isOpen:it,selectedValue:i.value,onSelect:gt,onClose:function t(){at(false);K(false);rt("")}}),xt=mt.activeIndex,_t=mt.setActiveIndex;(0,u.useEffect)((function(){if(it&&xt>=0&&ct.current){ct.current.scrollIntoView({block:"nearest",behavior:"smooth"})}}),[it,xt]);return(0,n.tZ)(Lt,{fieldState:l,field:i,label:f,disabled:b||o.length===0,readOnly:g,loading:m,isInlineLabel:_,helpText:A,removeBorder:L,isSecondary:J,isMagicAi:N},(function(t){var e,r,u;var c=t.css,s=we(t,ge);return(0,n.tZ)("div",{css:Fe.mainWrapper},(0,n.tZ)("div",{css:Fe.inputWrapper(B),ref:pt},(0,n.tZ)("div",{css:Fe.leftIcon},(0,n.tZ)(S.Z,{when:I},I),(0,n.tZ)(S.Z,{when:dt===null||dt===void 0?void 0:dt.icon},(function(t){return(0,n.tZ)(d.Z,{name:t,width:32,height:32})}))),(0,n.tZ)("div",{css:Le},(0,n.tZ)("input",me({},s,yt,{ref:function t(e){i.ref(e);ut.current=e},className:"tutor-input-field",css:[c,Fe.input({hasLeftIcon:!!I||!!(dt!==null&&dt!==void 0&&dt.icon),hasDescription:U,hasError:!!l.error,isMagicAi:N,isAiOutline:B}),true?"":0,true?"":0],autoComplete:"off",readOnly:g||!x,placeholder:h,value:$?et:Y,title:Y,onClick:function t(e){var r;e.stopPropagation();at((function(t){return!t}));(r=ut.current)===null||r===void 0?void 0:r.focus()},onKeyDown:function t(e){if(e.key==="Enter"){var r;e.preventDefault();at((function(t){return!t}));(r=ut.current)===null||r===void 0?void 0:r.focus()}if(e.key==="Tab"){at(false)}},onFocus:z&&x?function(t){t.target.select()}:undefined,onChange:function t(e){q(e.target.value);if(x){K(true);rt(e.target.value)}},"data-select":true})),(0,n.tZ)(S.Z,{when:U},(0,n.tZ)("span",{css:Fe.description({hasLeftIcon:!!I}),title:(e=R())===null||e===void 0?void 0:e.description},(r=R())===null||r===void 0?void 0:r.description))),!Z&&!m&&(0,n.tZ)("button",{tabIndex:-1,type:"button",css:Fe.caretButton({isOpen:it}),onClick:function t(){var e;at((function(t){return!t}));(e=ut.current)===null||e===void 0?void 0:e.focus()},disabled:b||g||o.length===0},(0,n.tZ)(d.Z,{name:"chevronDown",width:20,height:20}))),(0,n.tZ)(wt.h,{isOpen:it,onClickOutside:function t(){at(false);K(false);rt("")},onEscape:function t(){at(false);K(false);rt("")}},(0,n.tZ)("div",{css:[Fe.optionsWrapper,(u={},Oe(u,O.dZ?"right":"left",ht.left),Oe(u,"top",ht.top),Oe(u,"maxWidth",vt),u),true?"":0,true?"":0],ref:bt},(0,n.tZ)("ul",{css:[Fe.options(W),true?"":0,true?"":0]},!!k&&(0,n.tZ)("li",{css:Fe.listLabel},k),(0,n.tZ)(S.Z,{when:st.length>0,fallback:(0,n.tZ)("li",{css:Fe.emptyState},(0,a.__)("No options available","tutor"))},st.map((function(t,e){return(0,n.tZ)("li",{key:String(t.value),ref:t.value===i.value?lt:xt===e?ct:null,css:Fe.optionItem({isSelected:t.value===i.value,isActive:e===xt,isDisabled:!!t.disabled})},(0,n.tZ)("button",{type:"button",css:Fe.label,onClick:function e(r){if(!t.disabled){gt(t,r)}},disabled:t.disabled,title:t.label,onMouseOver:function t(){return _t(e)},onMouseLeave:function t(){return e!==xt&&_t(-1)},onFocus:function t(){return _t(e)},"aria-selected":xt===e},(0,n.tZ)(S.Z,{when:t.icon},(0,n.tZ)(d.Z,{name:t.icon,width:32,height:32})),(0,n.tZ)("span",null,t.label)))}))),C&&(0,n.tZ)("div",{css:Fe.clearButton({isDisabled:Y===""})},(0,n.tZ)(v.Z,{variant:"text",disabled:Y==="",icon:(0,n.tZ)(d.Z,{name:"delete"}),onClick:function t(){i.onChange(null);q("");rt("");at(false)}},(0,a.__)("Clear","tutor")))))))}))};const Te=De;var Je=true?{name:"21xn5r",styles:"transform:rotate(180deg)"}:0;var Me=true?{name:"16gsvie",styles:"min-width:200px"}:0;var Ne=true?{name:"kqjaov",styles:"position:relative;border:none;background:transparent"}:0;var Fe={mainWrapper:true?{name:"1d3w5wq",styles:"width:100%"}:0,inputWrapper:function t(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;return(0,n.iv)("width:100%;display:flex;justify-content:space-between;align-items:center;position:relative;",e&&(0,n.iv)("&::before{content:'';position:absolute;inset:0;background:",h.Jv.ai.gradient_1,";color:",h.Jv.text.primary,";border:1px solid transparent;-webkit-mask:linear-gradient(#fff 0 0) padding-box,linear-gradient(#fff 0 0);-webkit-mask-composite:xor;mask-composite:exclude;border-radius:6px;}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},leftIcon:(0,n.iv)("position:absolute;left:",h.W0[8],";",At.i.display.flex(),";align-items:center;height:100%;color:",h.Jv.icon["default"],";"+(true?"":0),true?"":0),input:function t(e){var r=e.hasLeftIcon,o=e.hasDescription,i=e.hasError,a=i===void 0?false:i,u=e.isMagicAi,l=u===void 0?false:u,c=e.isAiOutline,s=c===void 0?false:c;return(0,n.iv)("&[data-select]{",b.c.body(),";width:100%;cursor:pointer;padding-right:",h.W0[32],";",At.i.textEllipsis,";background-color:transparent;background-color:",h.Jv.background.white,";",r&&(0,n.iv)("padding-left:",h.W0[48],";"+(true?"":0),true?"":0)," ",o&&(0,n.iv)("&.tutor-input-field{height:56px;padding-bottom:",h.W0[24],";}"+(true?"":0),true?"":0)," ",a&&(0,n.iv)("background-color:",h.Jv.background.status.errorFail,";"+(true?"":0),true?"":0)," ",s&&Ne," :focus{",At.i.inputFocus,";",l&&(0,n.iv)("outline-color:",h.Jv.stroke.magicAi,";background-color:",h.Jv.background.magicAi[8],";"+(true?"":0),true?"":0)," ",a&&(0,n.iv)("border-color:",h.Jv.stroke.danger,";background-color:",h.Jv.background.status.errorFail,";"+(true?"":0),true?"":0),";}}"+(true?"":0),true?"":0)},description:function t(e){var r=e.hasLeftIcon;return(0,n.iv)(b.c.small(),";",At.i.text.ellipsis(1)," color:",h.Jv.text.hints,";position:absolute;bottom:",h.W0[8],";padding-inline:calc(",h.W0[16]," + 1px) ",h.W0[32],";",r&&(0,n.iv)("padding-left:calc(",h.W0[48]," + 1px);"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},listLabel:(0,n.iv)(b.c.body(),";color:",h.Jv.text.subdued,";min-height:40px;display:flex;align-items:center;padding-left:",h.W0[16],";"+(true?"":0),true?"":0),clearButton:function t(e){var r=e.isDisabled,o=r===void 0?false:r;return(0,n.iv)("padding:",h.W0[4]," ",h.W0[8],";border-top:1px solid ",h.Jv.stroke["default"],";&>button{padding:0;width:100%;font-size:",h.JB[12],";",!o&&(0,n.iv)("color:",h.Jv.text.title,";&:hover{text-decoration:underline;}"+(true?"":0),true?"":0),";>span{justify-content:center;}}"+(true?"":0),true?"":0)},optionsWrapper:true?{name:"1n0kzcr",styles:"position:absolute;width:100%"}:0,options:function t(e){return(0,n.iv)("z-index:",h.W5.dropdown,";background-color:",h.Jv.background.white,";list-style-type:none;box-shadow:",h.AF.popover,";padding:",h.W0[4]," 0;margin:0;max-height:500px;border-radius:",h.E0[6],";",At.i.overflowYAuto,";scrollbar-gutter:auto;",!e&&Me,";"+(true?"":0),true?"":0)},optionItem:function t(e){var r=e.isSelected,o=r===void 0?false:r,i=e.isActive,a=i===void 0?false:i,u=e.isDisabled,l=u===void 0?false:u;return(0,n.iv)(b.c.body(),";min-height:36px;height:100%;width:100%;display:flex;align-items:center;transition:background-color 0.3s ease-in-out;cursor:",l?"not-allowed":"pointer",";opacity:",l?.5:1,";",a&&(0,n.iv)("background-color:",h.Jv.background.hover,";"+(true?"":0),true?"":0)," &:hover{background-color:",!l&&h.Jv.background.hover,";}",!l&&o&&(0,n.iv)("background-color:",h.Jv.background.active,";position:relative;&::before{content:'';position:absolute;top:0;left:0;width:3px;height:100%;background-color:",h.Jv.action.primary["default"],";border-radius:0 ",h.E0[6]," ",h.E0[6]," 0;}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},label:(0,n.iv)(At.i.resetButton,";",At.i.text.ellipsis(1),";color:",h.Jv.text.title,";width:100%;height:100%;display:flex;align-items:center;gap:",h.W0[8],";margin:0 ",h.W0[12],";padding:",h.W0[6]," 0;text-align:left;line-height:",h.Nv[24],";word-break:break-all;cursor:pointer;&:hover,&:focus,&:active{background-color:transparent;color:",h.Jv.text.title,";}span{flex-shrink:0;",At.i.text.ellipsis(1)," width:100%;}"+(true?"":0),true?"":0),arrowUpDown:(0,n.iv)("color:",h.Jv.icon["default"],";display:flex;justify-content:center;align-items:center;margin-top:",h.W0[2],";"+(true?"":0),true?"":0),optionsContainer:true?{name:"1ivsou8",styles:"position:absolute;overflow:hidden auto;min-width:16px;max-width:calc(100% - 32px)"}:0,caretButton:function t(e){var r=e.isOpen,o=r===void 0?false:r;return(0,n.iv)(At.i.resetButton,";position:absolute;right:",h.W0[4],";display:flex;align-items:center;transition:transform 0.3s ease-in-out;color:",h.Jv.icon["default"],";border-radius:",h.E0[4],";padding:",h.W0[6],";height:100%;&:focus,&:active,&:hover{background:none;color:",h.Jv.icon["default"],";}&:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";}",o&&Je,";"+(true?"":0),true?"":0)},emptyState:(0,n.iv)(At.i.flexCenter(),";padding:",h.W0[8],";"+(true?"":0),true?"":0)};var Be=[{label:"English",value:"english"},{label:"简体中文",value:"simplified-chinese"},{label:"繁體中文",value:"traditional-chinese"},{label:"Español",value:"spanish"},{label:"Français",value:"french"},{label:"日本語",value:"japanese"},{label:"Deutsch",value:"german"},{label:"Português",value:"portuguese"},{label:"العربية",value:"arabic"},{label:"Русский",value:"russian"},{label:"Italiano",value:"italian"},{label:"한국어",value:"korean"},{label:"हिन्दी",value:"hindi"},{label:"Nederlands",value:"dutch"},{label:"Polski",value:"polish"},{label:"አማርኛ",value:"amharic"},{label:"Български",value:"bulgarian"},{label:"বাংলা",value:"bengali"},{label:"Čeština",value:"czech"},{label:"Dansk",value:"danish"},{label:"Ελληνικά",value:"greek"},{label:"Eesti",value:"estonian"},{label:"فارسی",value:"persian"},{label:"Filipino",value:"filipino"},{label:"Hrvatski",value:"croatian"},{label:"Magyar",value:"hungarian"},{label:"Bahasa Indonesia",value:"indonesian"},{label:"Lietuvių",value:"lithuanian"},{label:"Latviešu",value:"latvian"},{label:"Melayu",value:"malay"},{label:"Norsk",value:"norwegian"},{label:"Română",value:"romanian"},{label:"Slovenčina",value:"slovak"},{label:"Slovenščina",value:"slovenian"},{label:"Српски",value:"serbian"},{label:"Svenska",value:"swedish"},{label:"ภาษาไทย",value:"thai"},{label:"Türkçe",value:"turkish"},{label:"Українська",value:"ukrainian"},{label:"اردو",value:"urdu"},{label:"Tiếng Việt",value:"vietnamese"}];var ze=[{label:(0,a.__)("Formal","tutor"),value:"formal"},{label:(0,a.__)("Casual","tutor"),value:"casual"},{label:(0,a.__)("Professional","tutor"),value:"professional"},{label:(0,a.__)("Enthusiastic","tutor"),value:"enthusiastic"},{label:(0,a.__)("Informational","tutor"),value:"informational"},{label:(0,a.__)("Funny","tutor"),value:"funny"}];var Re=[{label:(0,a.__)("Title","tutor"),value:"title"},{label:(0,a.__)("Essay","tutor"),value:"essay"},{label:(0,a.__)("Paragraph","tutor"),value:"paragraph"},{label:(0,a.__)("Outline","tutor"),value:"outline"}];function Ue(){Ue=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Ue.apply(this,arguments)}var Ge=function t(e){var r=e.form;return(0,n.tZ)("div",{css:Qe.wrapper},(0,n.tZ)(p.Qr,{control:r.control,name:"characters",render:function t(e){return(0,n.tZ)(_o,Ue({},e,{isMagicAi:true,label:(0,a.__)("Character Limit","tutor"),type:"number"}))}}),(0,n.tZ)(p.Qr,{control:r.control,name:"language",render:function t(e){return(0,n.tZ)(Te,Ue({},e,{isMagicAi:true,label:(0,a.__)("Language","tutor"),options:Be}))}}),(0,n.tZ)(p.Qr,{control:r.control,name:"tone",render:function t(e){return(0,n.tZ)(Te,Ue({},e,{isMagicAi:true,options:ze,label:(0,a.__)("Tone","tutor")}))}}),(0,n.tZ)(p.Qr,{control:r.control,name:"format",render:function t(e){return(0,n.tZ)(Te,Ue({},e,{isMagicAi:true,label:(0,a.__)("Format","tutor"),options:Re}))}}))};var Qe={wrapper:(0,n.iv)("display:grid;grid-template-columns:repeat(2, 1fr);gap:",h.W0[16],";"+(true?"":0),true?"":0)};var Ye;function qe(t,e){if(!e){e=t.slice(0)}return Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(e)}}))}function He(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Ve=(0,u.forwardRef)((function(t,e){var r=t.width,o=r===void 0?"100%":r,i=t.height,a=i===void 0?16:i,u=t.animation,l=u===void 0?false:u,c=t.isMagicAi,s=c===void 0?false:c,d=t.isRound,f=d===void 0?false:d,p=t.animationDuration,v=p===void 0?1.6:p,h=t.className;return(0,n.tZ)("span",{ref:e,css:tr.skeleton(o,a,l,s,f,v),className:h})}));const $e=Ve;var Ke={wave:(0,n.F4)(Ye||(Ye=qe(["\n    0% {\n      transform: translateX(-100%);\n    }\n    50% {\n      transform: translateX(0%);\n    }\n    100% {\n      transform: translateX(100%);\n    }\n  "])))};var Xe=true?{name:"1q4m7z3",styles:"background:linear-gradient(89.17deg, #fef4ff 0.2%, #f9d3ff 50.09%, #fef4ff 96.31%)"}:0;var tr={skeleton:function t(e,r,o,i,a,u){return(0,n.iv)("display:block;width:",(0,j.hj)(e)?"".concat(e,"px"):e,";height:",(0,j.hj)(r)?"".concat(r,"px"):r,";border-radius:",h.E0[6],";background-color:",!i?"rgba(0, 0, 0, 0.11)":h.Jv.background.magicAi.skeleton,";position:relative;-webkit-mask-image:-webkit-radial-gradient(center, white, black);overflow:hidden;",a&&(0,n.iv)("border-radius:",h.E0.circle,";"+(true?"":0),true?"":0)," ",o&&(0,n.iv)(":after{content:'';background:linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent);position:absolute;transform:translateX(-100%);inset:0;",i&&Xe," animation:",u,"s linear 0.5s infinite normal none running ",Ke.wave,";}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}};var er=function t(){return(0,n.tZ)("div",{css:nr.container},(0,n.tZ)("div",{css:nr.wrapper},(0,n.tZ)($e,{animation:true,isMagicAi:true,width:"20%",height:"12px"}),(0,n.tZ)($e,{animation:true,isMagicAi:true,width:"100%",height:"12px"}),(0,n.tZ)($e,{animation:true,isMagicAi:true,width:"100%",height:"12px"}),(0,n.tZ)($e,{animation:true,isMagicAi:true,width:"40%",height:"12px"})),(0,n.tZ)("div",{css:nr.wrapper},(0,n.tZ)($e,{animation:true,isMagicAi:true,width:"80%",height:"12px"}),(0,n.tZ)($e,{animation:true,isMagicAi:true,width:"100%",height:"12px"}),(0,n.tZ)($e,{animation:true,isMagicAi:true,width:"80%",height:"12px"})))};const rr=er;var nr={wrapper:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[8],";"+(true?"":0),true?"":0),container:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[32],";"+(true?"":0),true?"":0)};var or=r(8811);function ir(t){"@babel/helpers - typeof";return ir="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ir(t)}function ar(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ur(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ar(Object(r),!0).forEach((function(e){lr(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ar(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function lr(t,e,r){e=cr(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function cr(t){var e=sr(t,"string");return ir(e)==="symbol"?e:String(e)}function sr(t,e){if(ir(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(ir(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var dr=function t(e){return Ot.R.post(St.Z.GENERATE_AI_IMAGE,e)};var fr=function t(){return(0,_t.D)({mutationFn:dr})};var pr=function t(e){return Ot.R.post(St.Z.MAGIC_FILL_AI_IMAGE,e).then((function(t){return t.data.data[0].b64_json}))};var vr=function t(){var e=(0,Zt.p)(),r=e.showToast;return(0,_t.D)({mutationFn:pr,onError:function t(e){r({type:"danger",message:(0,y.Mo)(e)})}})};var hr=function t(e){return Ot.R.post(St.Z.MAGIC_TEXT_GENERATION,e)};var br=function t(){var e=(0,Zt.p)(),r=e.showToast;return(0,_t.D)({mutationFn:hr,onError:function t(e){r({type:"danger",message:(0,y.Mo)(e)})}})};var yr=function t(e){return Ot.R.post(St.Z.MAGIC_AI_MODIFY_CONTENT,e)};var gr=function t(){var e=(0,Zt.p)(),r=e.showToast;return(0,_t.D)({mutationFn:yr,onError:function t(e){r({type:"danger",message:(0,y.Mo)(e)})}})};var mr=function t(e){return Ot.R.post(St.Z.USE_AI_GENERATED_IMAGE,e)};var wr=function t(){var e=(0,Zt.p)(),r=e.showToast;return(0,_t.D)({mutationFn:mr,onError:function t(e){r({type:"danger",message:(0,y.Mo)(e)})}})};var xr=function t(e){return wpAjaxInstance.post(endpoints.GENERATE_COURSE_CONTENT,e,{signal:e.signal})};var _r=function t(e){var r=useToast(),n=r.showToast;return useMutation({mutationKey:["GenerateCourseContent",e],mutationFn:xr,onError:function t(e){n({type:"danger",message:convertToErrorMessage(e)})}})};var Zr=function t(e){return wpAjaxInstance.post(endpoints.GENERATE_COURSE_CONTENT,e,{signal:e.signal})};var Or=function t(){var e=useToast(),r=e.showToast;return useMutation({mutationFn:Zr,onError:function t(e){r({type:"danger",message:convertToErrorMessage(e)})}})};var Sr=function t(e){return wpAjaxInstance.post(endpoints.GENERATE_COURSE_TOPIC_CONTENT,e,{signal:e.signal})};var kr=function t(){var e=useToast(),r=e.showToast;return useMutation({mutationFn:Sr,onError:function t(e){r({type:"danger",message:convertToErrorMessage(e)})}})};var jr=function t(e){return wpAjaxInstance.post(endpoints.SAVE_AI_GENERATED_COURSE_CONTENT,e)};var Er=function t(){var e=useToast(),r=e.showToast;var n=useQueryClient();return useMutation({mutationFn:jr,onSuccess:function t(){n.invalidateQueries({queryKey:["CourseDetails"]})},onError:function t(e){r({type:"danger",message:convertToErrorMessage(e)})}})};var Cr=function t(e){return wpAjaxInstance.post(endpoints.GENERATE_QUIZ_QUESTIONS,e,{signal:e.signal})};var Ar=function t(){var e=useToast(),r=e.showToast;return useMutation({mutationFn:Cr,onError:function t(e){r({type:"danger",message:convertToErrorMessage(e)})}})};var Pr=function t(e){return Ot.R.post(St.Z.OPEN_AI_SAVE_SETTINGS,ur({},e))};var Wr=function t(){var e=(0,Zt.p)(),r=e.showToast;return(0,_t.D)({mutationFn:Pr,onSuccess:function t(e){r({type:"success",message:e.message})},onError:function t(e){r({type:"danger",message:(0,y.Mo)(e)})}})};var Ir=r(7602);function Lr(t){"@babel/helpers - typeof";return Lr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Lr(t)}function Dr(){Dr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Dr.apply(this,arguments)}function Tr(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Tr=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(k([])));y&&y!==e&&r.call(y,i)&&(h=y);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==Lr(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function Jr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Mr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Jr(Object(r),!0).forEach((function(e){Nr(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Jr(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Nr(t,e,r){e=Fr(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Fr(t){var e=Br(t,"string");return Lr(e)==="symbol"?e:String(e)}function Br(t,e){if(Lr(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Lr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function zr(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function Rr(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){zr(i,n,o,a,u,"next",t)}function u(t){zr(i,n,o,a,u,"throw",t)}a(undefined)}))}}function Ur(t){return Yr(t)||Qr(t)||Vr(t)||Gr()}function Gr(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Qr(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Yr(t){if(Array.isArray(t))return $r(t)}function qr(t,e){return Xr(t)||Kr(t,e)||Vr(t,e)||Hr()}function Hr(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Vr(t,e){if(!t)return;if(typeof t==="string")return $r(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return $r(t,e)}function $r(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Kr(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Xr(t){if(Array.isArray(t))return t}function tn(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var en=[(0,a.__)("Mastering Digital Marketing: A Complete Guide","tutor"),(0,a.__)("The Ultimate Photoshop Course for Beginners","tutor"),(0,a.__)("Python Programming: From Zero to Hero","tutor"),(0,a.__)("Creative Writing Essentials: Unlock Your Storytelling Potential","tutor"),(0,a.__)("The Complete Guide to Web Development with React","tutor"),(0,a.__)("Master Public Speaking: Deliver Powerful Presentations","tutor"),(0,a.__)("Excel for Business: From Basics to Advanced Analytics","tutor"),(0,a.__)("Fitness Fundamentals: Build Strength and Confidence","tutor"),(0,a.__)("Photography Made Simple: Capture Stunning Shots","tutor"),(0,a.__)("Financial Freedom: Learn the Basics of Investing","tutor")];var rn=function t(e){var r=e.title,o=e.icon,i=e.closeModal,l=e.field,c=e.format,s=c===void 0?"essay":c,f=e.characters,b=f===void 0?250:f,g=e.is_html,m=g===void 0?false:g,w=e.fieldLabel,x=w===void 0?"":w,_=e.fieldPlaceholder,Z=_===void 0?"":_;var k=it({defaultValues:{prompt:"",characters:b,language:"english",tone:"formal",format:s}});var j=br();var E=gr();var C=(0,u.useState)([]),A=qr(C,2),P=A[0],W=A[1];var I=(0,u.useState)(0),L=qr(I,2),D=L[0],T=L[1];var J=(0,u.useState)(false),M=qr(J,2),N=M[0],F=M[1];var B=(0,u.useState)(null),z=qr(B,2),R=z[0],U=z[1];var G=(0,u.useRef)(null);var Q=(0,u.useRef)(null);var Y=(0,u.useMemo)((function(){return P[D]}),[P,D]);var q=k.watch("prompt");function H(t){W((function(e){return[t].concat(Ur(e))}));T(0)}function V(t,e){return $.apply(this,arguments)}function $(){$=Rr(Tr().mark((function t(e,r){var n,o,i,a;return Tr().wrap((function t(u){while(1)switch(u.prev=u.next){case 0:if(!(P.length===0)){u.next=2;break}return u.abrupt("return");case 2:n=P[D];if(!(e==="translation"&&!!r)){u.next=9;break}u.next=6;return E.mutateAsync({type:"translation",content:n,language:r,is_html:m});case 6:o=u.sent;if(o.data){H(o.data)}return u.abrupt("return");case 9:if(!(e==="change_tone"&&!!r)){u.next=15;break}u.next=12;return E.mutateAsync({type:"change_tone",content:n,tone:r,is_html:m});case 12:i=u.sent;if(i.data){H(i.data)}return u.abrupt("return");case 15:u.next=17;return E.mutateAsync({type:e,content:n,is_html:m});case 17:a=u.sent;if(a.data){H(a.data)}case 19:case"end":return u.stop()}}),t)})));return $.apply(this,arguments)}(0,u.useEffect)((function(){k.setFocus("prompt")}),[]);return(0,n.tZ)(Ir.Z,{onClose:i,title:r,icon:o,maxWidth:524},(0,n.tZ)("form",{onSubmit:k.handleSubmit(function(){var t=Rr(Tr().mark((function t(e){var r;return Tr().wrap((function t(n){while(1)switch(n.prev=n.next){case 0:n.next=2;return j.mutateAsync(Mr(Mr({},e),{},{is_html:m}));case 2:r=n.sent;if(r.data){H(r.data)}case 4:case"end":return n.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},(0,n.tZ)("div",{css:on.container},(0,n.tZ)("div",{css:on.fieldsWrapper},(0,n.tZ)(p.Qr,{control:k.control,name:"prompt",render:function t(e){return(0,n.tZ)(ne,Dr({},e,{label:x||(0,a.__)("Craft Your Course Description","tutor"),placeholder:Z||(0,a.__)("Provide a brief overview of your course topic, target audience, and key takeaways","tutor"),rows:4,isMagicAi:true}))}}),(0,n.tZ)("button",{type:"button",css:on.inspireButton,onClick:function t(){var e=en.length;var r=Math.floor(Math.random()*e);k.reset(Mr(Mr({},k.getValues()),{},{prompt:en[r]}))}},(0,n.tZ)(d.Z,{name:"bulbLine"}),(0,a.__)("Inspire Me","tutor"))),(0,n.tZ)(S.Z,{when:!j.isPending&&!E.isPending,fallback:(0,n.tZ)(rr,null)},(0,n.tZ)(S.Z,{when:P.length>0,fallback:(0,n.tZ)(Ge,{form:k})},(0,n.tZ)("div",null,(0,n.tZ)("div",{css:on.actionBar},(0,n.tZ)("div",{css:on.navigation},(0,n.tZ)(S.Z,{when:P.length>1},(0,n.tZ)(v.Z,{variant:"text",onClick:function t(){return T((function(t){return Math.max(0,t-1)}))},disabled:D===0},(0,n.tZ)(d.Z,{name:!O.dZ?"chevronLeft":"chevronRight",width:20,height:20})),(0,n.tZ)("div",{css:on.pageInfo},(0,n.tZ)("span",null,D+1)," / ",P.length),(0,n.tZ)(v.Z,{variant:"text",onClick:function t(){return T((function(t){return Math.min(P.length-1,t+1)}))},disabled:D===P.length-1},(0,n.tZ)(d.Z,{name:!O.dZ?"chevronRight":"chevronLeft",width:20,height:20})))),(0,n.tZ)(v.Z,{variant:"text",onClick:Rr(Tr().mark((function t(){var e;return Tr().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:if(!(P.length===0)){r.next=2;break}return r.abrupt("return");case 2:e=P[D];r.next=5;return(0,y.vQ)(e);case 5:F(true);setTimeout((function(){F(false)}),1500);case 7:case"end":return r.stop()}}),t)})))},(0,n.tZ)(S.Z,{when:N,fallback:(0,n.tZ)(d.Z,{name:"copy",width:20,height:20})},(0,n.tZ)(d.Z,{name:"checkFilled",width:20,height:20,style:(0,n.iv)("color:",h.Jv.text.success,"!important;"+(true?"":0),true?"":0)})))),(0,n.tZ)("div",{css:on.content,dangerouslySetInnerHTML:{__html:Y}})),(0,n.tZ)("div",{css:on.otherActions},(0,n.tZ)(qt,{variant:"outline",roundedFull:false,onClick:function t(){return V("rephrase")}},(0,a.__)("Rephrase","tutor")),(0,n.tZ)(qt,{variant:"outline",roundedFull:false,onClick:function t(){return V("make_shorter")}},(0,a.__)("Make Shorter","tutor")),(0,n.tZ)(qt,{variant:"outline",roundedFull:false,ref:G,onClick:function t(){return U("tone")}},(0,a.__)("Change Tone","tutor"),(0,n.tZ)(d.Z,{name:"chevronDown",width:16,height:16})),(0,n.tZ)(qt,{variant:"outline",roundedFull:false,ref:Q,onClick:function t(){return U("translate")}},(0,a.__)("Translate to","tutor"),(0,n.tZ)(d.Z,{name:"chevronDown",width:16,height:16})),(0,n.tZ)(qt,{variant:"outline",roundedFull:false,onClick:function t(){return V("write_as_bullets")}},(0,a.__)("Write as Bullets","tutor")),(0,n.tZ)(qt,{variant:"outline",roundedFull:false,onClick:function t(){return V("make_longer")}},(0,a.__)("Make Longer","tutor")),(0,n.tZ)(qt,{variant:"outline",roundedFull:false,onClick:function t(){return V("simplify_language")}},(0,a.__)("Simplify Language","tutor")))))),(0,n.tZ)($t.Z,{isOpen:R==="tone",triggerRef:G,closePopover:function t(){return U(null)},maxWidth:"160px",animationType:or.ru.slideUp},(0,n.tZ)(le,{options:ze,onChange:function(){var t=Rr(Tr().mark((function t(e){return Tr().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:U(null);r.next=3;return V("change_tone",e);case 3:case"end":return r.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()})),(0,n.tZ)($t.Z,{isOpen:R==="translate",triggerRef:Q,closePopover:function t(){return U(null)},maxWidth:"160px",animationType:or.ru.slideUp},(0,n.tZ)(le,{options:Be,onChange:function(){var t=Rr(Tr().mark((function t(e){return Tr().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:U(null);r.next=3;return V("translation",e);case 3:case"end":return r.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()})),(0,n.tZ)("div",{css:on.footer},(0,n.tZ)(S.Z,{when:P.length>0,fallback:(0,n.tZ)(qt,{type:"submit",disabled:j.isPending||!q||E.isPending},(0,n.tZ)(d.Z,{name:"magicWand",width:24,height:24}),(0,a.__)("Generate Now","tutor"))},(0,n.tZ)(qt,{variant:"outline",type:"submit",disabled:j.isPending||!q||E.isPending},(0,a.__)("Generate Again","tutor")),(0,n.tZ)(qt,{variant:"primary",disabled:j.isPending||P.length===0||E.isPending,onClick:function t(){l.onChange(P[D]);i()}},(0,a.__)("Use This","tutor"))))))};const nn=rn;var on={container:(0,n.iv)("padding:",h.W0[20],";display:flex;flex-direction:column;gap:",h.W0[16],";"+(true?"":0),true?"":0),fieldsWrapper:(0,n.iv)("position:relative;textarea{padding-bottom:",h.W0[40],"!important;}"+(true?"":0),true?"":0),footer:(0,n.iv)("padding:",h.W0[12]," ",h.W0[16],";display:flex;align-items:center;justify-content:end;gap:",h.W0[10],";box-shadow:0px 1px 0px 0px #e4e5e7 inset;button{width:fit-content;}"+(true?"":0),true?"":0),pageInfo:(0,n.iv)(b.c.caption(),";color:",h.Jv.text.hints,";&>span{font-weight:",h.Ue.medium,";color:",h.Jv.text.primary,";}"+(true?"":0),true?"":0),inspireButton:(0,n.iv)(At.i.resetButton,";",b.c.small(),";position:absolute;height:28px;bottom:",h.W0[12],";left:",h.W0[12],";border:1px solid ",h.Jv.stroke.brand,";border-radius:",h.E0[4],";display:flex;align-items:center;gap:",h.W0[4],";color:",h.Jv.text.brand,";padding-inline:",h.W0[12],";background-color:",h.Jv.background.white,";&:hover{background-color:",h.Jv.background.brand,";color:",h.Jv.text.white,";}&:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";outline-offset:1px;}&:disabled{background-color:",h.Jv.background.disable,";color:",h.Jv.text.disable,";}"+(true?"":0),true?"":0),navigation:(0,n.iv)("margin-left:-",h.W0[8],";display:flex;align-items:center;"+(true?"":0),true?"":0),content:(0,n.iv)(b.c.caption(),";height:180px;overflow-y:auto;background-color:",h.Jv.background.magicAi["default"],";border-radius:",h.E0[6],";padding:",h.W0[6]," ",h.W0[12],";color:",h.Jv.text.magicAi,";"+(true?"":0),true?"":0),actionBar:true?{name:"bcffy2",styles:"display:flex;align-items:center;justify-content:space-between"}:0,otherActions:(0,n.iv)("display:flex;gap:",h.W0[10],";flex-wrap:wrap;&>button{width:fit-content;}"+(true?"":0),true?"":0)};var an=r(7145);var un=r(7363);var ln={title:(0,n.tZ)(un.Fragment,null,(0,a.__)("Upgrade to Tutor LMS Pro today and experience the power of ","tutor"),(0,n.tZ)("span",{css:At.i.aiGradientText},(0,a.__)("AI Studio","tutor"))),message:(0,a.__)("Upgrade your plan to access the AI feature","tutor"),featuresTitle:(0,a.__)("Don’t miss out on this game-changing feature!","tutor"),features:[(0,a.__)("Generate a complete course outline in seconds!","tutor"),(0,a.__)("Let the AI Studio create Quizzes on your behalf and give your brain a well-deserved break.","tutor"),(0,a.__)("Generate images, customize backgrounds, and even remove unwanted objects with ease.","tutor"),(0,a.__)("Say goodbye to typos and grammar errors with AI-powered copy editing.","tutor")],footer:(0,n.tZ)(v.Z,{onClick:function t(){return window.open(k.Z.TUTOR_PRICING_PAGE,"_blank","noopener")},icon:(0,n.tZ)(d.Z,{name:"crown",width:24,height:24})},(0,a.__)("Get Tutor LMS Pro","tutor"))};var cn=function t(e){var r=e.title,o=r===void 0?ln.title:r,i=e.message,u=i===void 0?ln.message:i,l=e.featuresTitle,c=l===void 0?ln.featuresTitle:l,s=e.features,f=s===void 0?ln.features:s,p=e.closeModal,v=e.image,h=e.image2x,b=e.footer,y=b===void 0?ln.footer:b;return(0,n.tZ)(Ir.Z,{onClose:p,entireHeader:(0,n.tZ)("span",{css:dn.message},u),maxWidth:496},(0,n.tZ)("div",{css:dn.wrapper},(0,n.tZ)(S.Z,{when:o},(0,n.tZ)("h4",{css:dn.title},o)),(0,n.tZ)(S.Z,{when:v},(0,n.tZ)("img",{css:dn.image,src:v,alt:typeof o==="string"?o:(0,a.__)("Illustration"),srcSet:h?"".concat(v," ").concat(h," 2x"):undefined})),(0,n.tZ)(S.Z,{when:c},(0,n.tZ)("h6",{css:dn.featuresTiTle},c)),(0,n.tZ)(S.Z,{when:f.length},(0,n.tZ)("div",{css:dn.features},(0,n.tZ)(ue,{each:f},(function(t,e){return(0,n.tZ)("div",{key:e,css:dn.feature},(0,n.tZ)(d.Z,{name:"materialCheck",width:20,height:20,style:dn.checkIcon}),(0,n.tZ)("span",null,t))})))),(0,n.tZ)(S.Z,{when:y},y)))};const sn=cn;var dn={wrapper:(0,n.iv)("padding:0 ",h.W0[24]," ",h.W0[32]," ",h.W0[24],";",At.i.display.flex("column"),";gap:",h.W0[16],";"+(true?"":0),true?"":0),message:(0,n.iv)(b.c.small(),";color:",h.Jv.text.subdued,";padding-left:",h.W0[8],";padding-top:",h.W0[24],";padding-bottom:",h.W0[4],";"+(true?"":0),true?"":0),title:(0,n.iv)(b.c.heading6("medium"),";color:",h.Jv.text.primary,";text-wrap:pretty;"+(true?"":0),true?"":0),image:(0,n.iv)("height:270px;width:100%;object-fit:cover;object-position:center;border-radius:",h.E0[8],";"+(true?"":0),true?"":0),featuresTiTle:(0,n.iv)(b.c.body("medium"),";color:",h.Jv.text.primary,";text-wrap:pretty;"+(true?"":0),true?"":0),features:(0,n.iv)(At.i.display.flex("column"),";gap:",h.W0[4],";padding-right:",h.W0[48],";"+(true?"":0),true?"":0),feature:(0,n.iv)(At.i.display.flex(),";gap:",h.W0[12],";",b.c.small(),";color:",h.Jv.text.title,";span{text-wrap:pretty;}"+(true?"":0),true?"":0),checkIcon:(0,n.iv)("flex-shrink:0;color:",h.Jv.text.success,";"+(true?"":0),true?"":0)};var fn={text:{warning:"#D47E00",success:"#D47E00",danger:"#f44337",info:"#D47E00",primary:"#D47E00"},icon:{warning:"#FAB000",success:"#FAB000",danger:"#f55e53",info:"#FAB000",primary:"#FAB000"},background:{warning:"#FBFAE9",success:"#FBFAE9",danger:"#fdd9d7",info:"#FBFAE9",primary:"#FBFAE9"}};var pn=function t(e){var r=e.children,o=e.type,i=o===void 0?"warning":o,a=e.icon;return(0,n.tZ)("div",{css:hn.wrapper({type:i})},(0,n.tZ)(S.Z,{when:a},(function(t){return(0,n.tZ)(d.Z,{style:hn.icon({type:i}),name:t,height:24,width:24})})),(0,n.tZ)("span",null,r))};const vn=pn;var hn={wrapper:function t(e){var r=e.type;return(0,n.iv)(b.c.caption(),";display:flex;align-items:start;padding:",h.W0[8]," ",h.W0[12],";width:100%;border-radius:",h.E0.card,";gap:",h.W0[4],";background-color:",fn.background[r],";color:",fn.text[r],";"+(true?"":0),true?"":0)},icon:function t(e){var r=e.type;return(0,n.iv)("color:",fn.icon[r],";flex-shrink:0;"+(true?"":0),true?"":0)}};function bn(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var yn=true?{name:"1wge6iy",styles:"left:3px"}:0;var gn=true?{name:"c7mfxx",styles:"right:3px"}:0;var mn=true?{name:"1pf4cml",styles:"left:11px"}:0;var wn=true?{name:"ovq9sj",styles:"top:2px;left:3px;width:12px;height:12px"}:0;var xn=true?{name:"16g29gd",styles:"width:26px;height:16px"}:0;var _n={switchStyles:function t(e){return(0,n.iv)("&[data-input]{all:unset;appearance:none;border:0;width:40px;height:24px;background:",h.Jv.color.black[10],";border-radius:12px;position:relative;display:inline-block;vertical-align:middle;cursor:pointer;transition:background-color 0.25s cubic-bezier(0.785, 0.135, 0.15, 0.86);",e==="small"&&xn," &::before{display:none!important;}&:focus{border:none;outline:none;box-shadow:none;}&:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";outline-offset:1px;}&:after{content:'';position:absolute;top:3px;left:",h.W0[4],";width:18px;height:18px;background:",h.Jv.background.white,";border-radius:",h.E0.circle,";box-shadow:",h.AF["switch"],";transition:left 0.25s cubic-bezier(0.785, 0.135, 0.15, 0.86);",e==="small"&&wn,";}&:checked{background:",h.Jv.primary.main,";&:after{left:18px;",e==="small"&&mn,";}}&:disabled{pointer-events:none;filter:none;opacity:0.5;}}"+(true?"":0),true?"":0)},labelStyles:function t(e){return(0,n.iv)(b.c.caption(),";color:",e?h.Jv.text.title:h.Jv.text.subdued,";"+(true?"":0),true?"":0)},wrapperStyle:function t(e){return(0,n.iv)("display:flex;align-items:center;justify-content:space-between;width:fit-content;flex-direction:",e==="left"?"row":"row-reverse",";column-gap:",h.W0[12],";position:relative;"+(true?"":0),true?"":0)},spinner:function t(e){return(0,n.iv)("display:flex;position:absolute;top:50%;transform:translateY(-50%);",e&&gn," ",!e&&yn,";"+(true?"":0),true?"":0)}};var Zn=l().forwardRef((function(t,e){var r=t.id,o=r===void 0?(0,y.x0)():r,i=t.name,a=t.label,u=t.value,l=t.checked,c=t.disabled,s=t.loading,d=t.onChange,f=t.labelPosition,p=f===void 0?"left":f,v=t.labelCss,h=t.size,b=h===void 0?"regular":h;var g=function t(e){d===null||d===void 0?void 0:d(e.target.checked,e)};return(0,n.tZ)("div",{css:_n.wrapperStyle(p)},a&&(0,n.tZ)("label",{css:[_n.labelStyles(l||false),v,true?"":0,true?"":0],htmlFor:o},a),(0,n.tZ)("input",{ref:e,value:u?String(u):undefined,type:"checkbox",name:i,id:o,checked:!!l,disabled:c,css:_n.switchStyles(b),onChange:g,"data-input":true}),(0,n.tZ)(S.Z,{when:s},(0,n.tZ)("span",{css:_n.spinner(!!l)},(0,n.tZ)(Z.ZP,{size:b==="small"?12:20}))))}));const On=Zn;function Sn(){Sn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Sn.apply(this,arguments)}var kn=function t(e){var r=e.field,o=e.fieldState,i=e.label,a=e.disabled,u=e.loading,l=e.labelPosition,c=l===void 0?"left":l,s=e.helpText,d=e.isHidden,f=e.labelCss,p=e.onChange;return(0,n.tZ)(Lt,{label:i,field:r,fieldState:o,loading:u,helpText:s,isHidden:d,isInlineLabel:true},(function(t){return(0,n.tZ)("div",{css:En.wrapper},(0,n.tZ)(On,Sn({},r,t,{disabled:a,checked:r.value,labelCss:f,labelPosition:c,onChange:function t(){r.onChange(!r.value);p===null||p===void 0?void 0:p(!r.value)}})))}))};const jn=N(kn);var En={wrapper:(0,n.iv)("display:flex;align-items:center;justify-content:space-between;gap:",h.W0[40],";"+(true?"":0),true?"":0)};var Cn=r(733);function An(t,e){return Dn(t)||Ln(t,e)||Wn(t,e)||Pn()}function Pn(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Wn(t,e){if(!t)return;if(typeof t==="string")return In(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return In(t,e)}function In(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ln(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Dn(t){if(Array.isArray(t))return t}var Tn=function t(){return{required:{value:true,message:(0,a.__)("This field is required","tutor")}}};var Jn=function t(e){var r=e.maxValue,n=e.message;return{maxLength:{value:r,message:n||__("Max. value should be ".concat(r))}}};var Mn=function t(){return{validate:function t(e){if((e===null||e===void 0?void 0:e.amount)===undefined){return __("The field is required","tutor")}return undefined}}};var Nn=function t(e){if(!(0,Cn.Z)(new Date(e||""))){return(0,a.__)("Invalid date entered!","tutor")}return undefined};var Fn=function t(e){return{validate:function t(r){if(r&&e<r.length){return __("Maximum ".concat(e," character supported"),"tutor")}return undefined}}};var Bn=function t(e){if(!e){return undefined}var r=(0,a.__)("Invalid time entered!","tutor");var n=e.split(":"),o=An(n,2),i=o[0],u=o[1];if(!i||!u){return r}var l=u.split(" "),c=An(l,2),s=c[0],d=c[1];if(!s||!d){return r}if(i.length!==2||s.length!==2){return r}if(Number(i)<1||Number(i)>12){return r}if(Number(s)<0||Number(s)>59){return r}if(!["am","pm"].includes(d.toLowerCase())){return r}return undefined};var zn=r(7363);function Rn(t){"@babel/helpers - typeof";return Rn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rn(t)}var Un,Gn;function Qn(){Qn=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Qn.apply(this,arguments)}function Yn(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Yn=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(k([])));y&&y!==e&&r.call(y,i)&&(h=y);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==Rn(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function qn(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function Hn(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){qn(i,n,o,a,u,"next",t)}function u(t){qn(i,n,o,a,u,"throw",t)}a(undefined)}))}}var Vn=((Un=k.y.settings)===null||Un===void 0?void 0:Un.chatgpt_enable)==="on";var $n=(Gn=k.y.current_user.roles)===null||Gn===void 0?void 0:Gn.includes(O.er.ADMINISTRATOR);var Kn=function t(e){var r=e.closeModal,o=e.image,i=e.image2x;var l=it({defaultValues:{openAIApiKey:"",enable_open_ai:Vn},shouldFocusError:true});var c=Wr();var s=function(){var t=Hn(Yn().mark((function t(e){var n;return Yn().wrap((function t(o){while(1)switch(o.prev=o.next){case 0:o.next=2;return c.mutateAsync({chatgpt_api_key:e.openAIApiKey,chatgpt_enable:e.enable_open_ai?1:0});case 2:n=o.sent;if(n.status_code===200){r({action:"CONFIRM"});window.location.reload()}case 4:case"end":return o.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}();(0,u.useEffect)((function(){l.setFocus("openAIApiKey")}),[]);return(0,n.tZ)(Ir.Z,{onClose:function t(){return r({action:"CLOSE"})},title:$n?(0,a.__)("Set OpenAI API key","tutor"):undefined,entireHeader:$n?undefined:(0,n.tZ)(zn.Fragment,null," "),maxWidth:560},(0,n.tZ)("div",{css:to.wrapper({isCurrentUserAdmin:$n})},(0,n.tZ)(S.Z,{when:$n,fallback:(0,n.tZ)(zn.Fragment,null,(0,n.tZ)("img",{css:to.image,src:o,srcSet:i?"".concat(o," 1x, ").concat(i," 2x"):"".concat(o," 1x"),alt:(0,a.__)("Connect API KEY","tutor")}),(0,n.tZ)("div",null,(0,n.tZ)("div",{css:to.message},(0,a.__)("API is not connected","tutor")),(0,n.tZ)("div",{css:to.title},(0,a.__)("Please, ask your Admin to connect the API with Tutor LMS Pro.","tutor"))))},(0,n.tZ)("form",{css:to.formWrapper,onSubmit:l.handleSubmit(s)},(0,n.tZ)("div",{css:to.infoText},(0,n.tZ)("div",null,(0,a.__)("Find your Secret API key in your ","tutor"),(0,n.tZ)("a",{href:k.Z.CHATGPT_PLATFORM_URL},(0,a.__)("OpenAI User settings","tutor")),(0,a.__)(" and paste it here to connect OpenAI with your Tutor LMS website.","tutor")),(0,n.tZ)(vn,{type:"info",icon:"warning"},(0,a.__)("This page will reload after submit. Please save course information.","tutor"))),(0,n.tZ)(p.Qr,{name:"openAIApiKey",control:l.control,rules:Tn(),render:function t(e){return(0,n.tZ)(_o,Qn({},e,{type:"password",isPassword:true,label:(0,a.__)("OpenAI API key","tutor"),placeholder:(0,a.__)("Enter your OpenAI API key","tutor")}))}}),(0,n.tZ)(p.Qr,{name:"enable_open_ai",control:l.control,render:function t(e){return(0,n.tZ)(jn,Qn({},e,{label:(0,a.__)("Enable OpenAI","tutor")}))}})),(0,n.tZ)("div",{css:to.formFooter},(0,n.tZ)(v.Z,{onClick:function t(){return r({action:"CLOSE"})},variant:"text",size:"small"},(0,a.__)("Cancel","tutor")),(0,n.tZ)(v.Z,{size:"small",onClick:l.handleSubmit(s),loading:c.isPending},(0,a.__)("Save","tutor"))))))};const Xn=Kn;var to={wrapper:function t(e){var r=e.isCurrentUserAdmin;return(0,n.iv)(At.i.display.flex("column"),";gap:",h.W0[20],";",!r&&(0,n.iv)("padding:",h.W0[24],";padding-top:",h.W0[6],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},formWrapper:(0,n.iv)(At.i.display.flex("column"),";gap:",h.W0[20],";padding:",h.W0[16]," ",h.W0[16]," 0 ",h.W0[16],";"+(true?"":0),true?"":0),infoText:(0,n.iv)(b.c.small(),";",At.i.display.flex("column"),";gap:",h.W0[8],";color:",h.Jv.text.subdued,";a{",At.i.resetButton," color:",h.Jv.text.brand,";}"+(true?"":0),true?"":0),formFooter:(0,n.iv)(At.i.display.flex(),";justify-content:flex-end;gap:",h.W0[16],";border-top:1px solid ",h.Jv.stroke.divider,";padding:",h.W0[16],";"+(true?"":0),true?"":0),image:(0,n.iv)("height:310px;width:100%;object-fit:cover;object-position:center;border-radius:",h.E0[8],";"+(true?"":0),true?"":0),message:(0,n.iv)(b.c.small(),";color:",h.Jv.text.subdued,";"+(true?"":0),true?"":0),title:(0,n.iv)(b.c.heading4("medium"),";color:",h.Jv.text.primary,";margin-top:",h.W0[4],";text-wrap:pretty;"+(true?"":0),true?"":0)};const eo=r.p+"images/6d34e8c6da0e2b4bfbd21a38bf7bbaf0-generate-text-2x.webp";const ro=r.p+"images/1cc4846c27ec533c869242e997e1c783-generate-text.webp";var no=r(7363);function oo(t){"@babel/helpers - typeof";return oo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},oo(t)}var io;function ao(){ao=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return ao.apply(this,arguments)}function uo(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function lo(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?uo(Object(r),!0).forEach((function(e){co(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):uo(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function co(t,e,r){e=so(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function so(t){var e=fo(t,"string");return oo(e)==="symbol"?e:String(e)}function fo(t,e){if(oo(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(oo(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function po(t,e){return go(t)||yo(t,e)||ho(t,e)||vo()}function vo(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ho(t,e){if(!t)return;if(typeof t==="string")return bo(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return bo(t,e)}function bo(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function yo(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function go(t){if(Array.isArray(t))return t}var mo=!!k.y.tutor_pro_url;var wo=(io=k.y.settings)===null||io===void 0?void 0:io.chatgpt_key_exist;var xo=function t(e){var r;var o=e.label,i=e.type,l=i===void 0?"text":i,c=e.maxLimit,s=e.field,f=e.fieldState,p=e.disabled,h=e.readOnly,b=e.loading,g=e.placeholder,m=e.helpText,w=e.onChange,x=e.onKeyDown,_=e.isHidden,Z=e.isClearable,O=Z===void 0?false:Z,k=e.isSecondary,E=k===void 0?false:k,C=e.removeBorder,A=e.dataAttribute,P=e.isInlineLabel,W=P===void 0?false:P,I=e.isPassword,L=I===void 0?false:I,D=e.style,T=e.selectOnFocus,J=T===void 0?false:T,M=e.autoFocus,N=M===void 0?false:M,F=e.generateWithAi,B=F===void 0?false:F,z=e.isMagicAi,R=z===void 0?false:z,U=e.allowNegative,G=U===void 0?false:U,Q=e.onClickAiButton;var Y=(0,u.useState)(l),q=po(Y,2),H=q[0],V=q[1];var $=(0,an.d)(),K=$.showModal;var X=(0,u.useRef)(null);var tt=(r=s.value)!==null&&r!==void 0?r:"";var et=undefined;if(H==="number"){tt=(0,y.jv)("".concat(tt),G).replace(/(\..*)\./g,"$1")}if(c){et={maxLimit:c,inputCharacter:tt.toString().length}}var rt=lo({},(0,j.$K)(A)&&co({},A,true));var nt=function t(){if(!mo){K({component:sn,props:{image:ro,image2x:eo}})}else if(!wo){K({component:Xn,props:{image:ro,image2x:eo}})}else{K({component:nn,isMagicAi:true,props:{title:(0,a.__)("AI Studio","tutor"),icon:(0,n.tZ)(d.Z,{name:"magicAiColorize",width:24,height:24}),characters:120,field:s,fieldState:f,format:"title",is_html:false,fieldLabel:(0,a.__)("Create a Compelling Title","tutor"),fieldPlaceholder:(0,a.__)("Describe the main focus of your course in a few words","tutor")}});Q===null||Q===void 0?void 0:Q()}};return(0,n.tZ)(Lt,{label:o,field:s,fieldState:f,disabled:p,readOnly:h,loading:b,placeholder:g,helpText:m,isHidden:_,characterCount:et,isSecondary:E,removeBorder:C,isInlineLabel:W,inputStyle:D,generateWithAi:B,onClickAiButton:nt,isMagicAi:R},(function(t){return(0,n.tZ)(no.Fragment,null,(0,n.tZ)("div",{css:Zo.container(O||L)},(0,n.tZ)("input",ao({},s,t,rt,{type:H==="number"?"text":H,value:tt,autoFocus:N,onChange:function t(e){var r=e.target.value;var n=H==="number"?(0,y.jv)(r):r;s.onChange(n);if(w){w(n)}},onClick:function t(e){e.stopPropagation()},onKeyDown:function t(e){e.stopPropagation();x===null||x===void 0?void 0:x(e.key)},autoComplete:"off",ref:function t(e){s.ref(e);X.current=e},onFocus:function t(){if(!J||!X.current){return}X.current.select()}})),(0,n.tZ)(S.Z,{when:L},(0,n.tZ)("div",{css:Zo.eyeButtonWrapper},(0,n.tZ)("button",{type:"button",css:Zo.eyeButton({type:H}),onClick:function t(){return V((function(t){return t==="password"?"text":"password"}))}},(0,n.tZ)(d.Z,{name:"eye",height:24,width:24})))),(0,n.tZ)(S.Z,{when:O&&!!s.value&&H!=="password"},(0,n.tZ)("div",{css:Zo.clearButton},(0,n.tZ)(v.Z,{variant:"text",onClick:function t(){return s.onChange("")}},(0,n.tZ)(d.Z,{name:"timesAlt"}))))))}))};const _o=N(xo);var Zo={container:function t(e){return(0,n.iv)("position:relative;display:flex;input{&.tutor-input-field{",e&&"padding-right: ".concat(h.W0[36],";"),";}}"+(true?"":0),true?"":0)},clearButton:(0,n.iv)("position:absolute;right:",h.W0[4],";top:",h.W0[4],";width:32px;height:32px;background:transparent;button{padding:",h.W0[8],";border-radius:",h.E0[2],";}"+(true?"":0),true?"":0),eyeButtonWrapper:(0,n.iv)("position:absolute;display:flex;right:",h.W0[4],";top:50%;transform:translateY(-50%);"+(true?"":0),true?"":0),eyeButton:function t(e){var r=e.type;return(0,n.iv)(At.i.resetButton," ",At.i.flexCenter()," color:",h.Jv.icon["default"],";padding:",h.W0[4],";border-radius:",h.E0[2],";background:transparent;",r!=="password"&&(0,n.iv)("color:",h.Jv.icon.brand,";"+(true?"":0),true?"":0)," &:focus,&:active,&:hover{background:none;color:",h.Jv.icon["default"],";}:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";outline-offset:2px;}"+(true?"":0),true?"":0)}};var Oo=r(7363);function So(t){"@babel/helpers - typeof";return So="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},So(t)}function ko(t,e,r){e=jo(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function jo(t){var e=Eo(t,"string");return So(e)==="symbol"?e:String(e)}function Eo(t,e){if(So(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(So(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Co(){Co=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Co.apply(this,arguments)}function Ao(t,e){return Do(t)||Lo(t,e)||Wo(t,e)||Po()}function Po(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Wo(t,e){if(!t)return;if(typeof t==="string")return Io(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Io(t,e)}function Io(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Lo(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Do(t){if(Array.isArray(t))return t}function To(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Jo=function t(e){var r;var o=e.label,i=e.field,l=e.fieldState,c=e.disabled,s=e.loading,f=e.placeholder,p=e.helpText,h=e.isInlineLabel,b=e.clearable,g=e.listItemsLabel,m=e.optionsWrapperStyle;var w=(0,u.useState)(false),x=Ao(w,2),_=x[0],Z=x[1];var k=(0,u.useState)(""),j=Ao(k,2),E=j[0],C=j[1];var A=Q(E,300);var P=jt(A);var W=(0,y.TQ)((r=P.data)!==null&&r!==void 0?r:[]);var I=(0,wt.l)({isOpen:_,isDropdown:true,dependencies:[W.length]}),L=I.triggerRef,D=I.triggerWidth,T=I.position,J=I.popoverRef;(0,u.useEffect)((function(){if(!_){C("")}}),[_]);return(0,n.tZ)(Lt,{label:o,field:i,fieldState:l,disabled:c||W.length===0,loading:s,placeholder:f,helpText:p,isInlineLabel:h},(function(t){var e,r,o;return(0,n.tZ)(Oo.Fragment,null,(0,n.tZ)("div",{css:Bo.inputWrapper,ref:L},(0,n.tZ)("input",Co({},t,{type:"text",onClick:function t(e){e.stopPropagation();Z(true)},onKeyDown:function t(e){if(e.key==="Enter"){e.preventDefault();Z(true)}if(e.key==="Tab"){Z(false)}},autoComplete:"off",readOnly:true,disabled:c||W.length===0,value:i.value?(e=P.data)===null||e===void 0?void 0:(r=e.find((function(t){return t.id===i.value})))===null||r===void 0?void 0:r.name:"",placeholder:f})),(0,n.tZ)("button",{tabIndex:-1,type:"button",disabled:c||W.length===0,"aria-label":(0,a.__)("Toggle options","tutor"),css:Bo.toggleIcon(_),onClick:function t(){Z((function(t){return!t}))}},(0,n.tZ)(d.Z,{name:"chevronDown",width:20,height:20}))),(0,n.tZ)(wt.h,{isOpen:_,onClickOutside:function t(){return Z(false)},onEscape:function t(){return Z(false)}},(0,n.tZ)("div",{css:[Bo.categoryWrapper,(o={},ko(o,O.dZ?"right":"left",T.left),ko(o,"top",T.top),o),true?"":0,true?"":0],ref:J,style:{maxWidth:D}},!!g&&(0,n.tZ)("p",{css:Bo.listItemLabel},g),(0,n.tZ)("div",{css:Bo.searchInput},(0,n.tZ)("div",{css:Bo.searchIcon},(0,n.tZ)(d.Z,{name:"search",width:24,height:24})),(0,n.tZ)("input",{type:"text",placeholder:(0,a.__)("Search","tutor"),value:E,onChange:function t(e){C(e.target.value)}})),(0,n.tZ)(S.Z,{when:W.length>0,fallback:(0,n.tZ)("div",{css:Bo.notFound},(0,a.__)("No categories found.","tutor"))},(0,n.tZ)("div",{css:[Bo.options,m,true?"":0,true?"":0]},W.map((function(t){return(0,n.tZ)(No,{key:t.id,option:t,onChange:function t(e){i.onChange(e);Z(false)}})})))),b&&(0,n.tZ)("div",{css:Bo.clearButton},(0,n.tZ)(v.Z,{variant:"text",onClick:function t(){i.onChange(null);Z(false)}},(0,a.__)("Clear selection","tutor"))))))}))};const Mo=Jo;var No=function t(e){var r=e.option,o=e.onChange,i=e.level,a=i===void 0?0:i;var u=r.children.length>0;var l=function e(){if(!u){return null}return r.children.map((function(e){return(0,n.tZ)(t,{key:e.id,option:e,onChange:o,level:a+1})}))};return(0,n.tZ)("div",{css:Bo.branchItem(a)},(0,n.tZ)("button",{type:"button",onClick:function t(){return o(r.id)},title:r.name},(0,y.aV)(r.name)),l())};var Fo=true?{name:"21xn5r",styles:"transform:rotate(180deg)"}:0;var Bo={categoryWrapper:(0,n.iv)("position:absolute;background-color:",h.Jv.background.white,";box-shadow:",h.AF.popover,";border-radius:",h.E0[6],";border:1px solid ",h.Jv.stroke.border,";padding:",h.W0[8]," 0;min-width:275px;"+(true?"":0),true?"":0),options:(0,n.iv)("max-height:455px;",At.i.overflowYAuto,";"+(true?"":0),true?"":0),notFound:(0,n.iv)(At.i.display.flex(),";align-items:center;",b.c.caption("regular"),";padding:",h.W0[8]," ",h.W0[16],";color:",h.Jv.text.hints,";"+(true?"":0),true?"":0),searchInput:(0,n.iv)("position:sticky;top:0;padding:",h.W0[8]," ",h.W0[16],";input{",b.c.body("regular"),";width:100%;border-radius:",h.E0[6],";border:1px solid ",h.Jv.stroke["default"],";padding:",h.W0[4]," ",h.W0[16]," ",h.W0[4]," ",h.W0[32],";color:",h.Jv.text.title,";appearance:textfield;:focus{",At.i.inputFocus,";}}"+(true?"":0),true?"":0),searchIcon:(0,n.iv)("position:absolute;left:",h.W0[24],";top:50%;transform:translateY(-50%);color:",h.Jv.icon["default"],";display:flex;"+(true?"":0),true?"":0),branchItem:function t(e){return(0,n.iv)("position:relative;z-index:",h.W5.positive,";button{",At.i.resetButton,";",b.c.body("regular"),";",At.i.text.ellipsis(1),";color:",h.Jv.text.title,";padding-left:calc(",h.W0[24]," + ",h.W0[24]," * ",e,");line-height:",h.Nv[36],";padding-right:",h.W0[16],";width:100%;&:hover,&:focus,&:active{background-color:",h.Jv.background.hover,";color:",h.Jv.text.title,";}}"+(true?"":0),true?"":0)},toggleIcon:function t(e){return(0,n.iv)(At.i.resetButton,";position:absolute;top:",h.W0[4],";right:",h.W0[4],";display:flex;align-items:center;transition:transform 0.3s ease-in-out;color:",h.Jv.icon["default"],";padding:",h.W0[6],";&:focus,&:active,&:hover{background:none;color:",h.Jv.icon["default"],";}",e&&Fo,";"+(true?"":0),true?"":0)},inputWrapper:true?{name:"137a3a8",styles:"position:relative;input:read-only{background-color:inherit;}"}:0,clearButton:(0,n.iv)("padding:",h.W0[8]," ",h.W0[24],";box-shadow:",h.AF.dividerTop,";&>button{padding:0;}"+(true?"":0),true?"":0),listItemLabel:(0,n.iv)(b.c.caption(),";font-weight:",h.Ue.medium,";background-color:",h.Jv.background.white,";color:",h.Jv.text.hints,";padding:",h.W0[10]," ",h.W0[16],";"+(true?"":0),true?"":0),radioLabel:(0,n.iv)("line-height:",h.Nv[32],";padding-left:",h.W0[2],";"+(true?"":0),true?"":0)};var zo=r(7363);function Ro(t){"@babel/helpers - typeof";return Ro="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ro(t)}function Uo(){Uo=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Uo.apply(this,arguments)}function Go(t){return qo(t)||Yo(t)||ri(t)||Qo()}function Qo(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Yo(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function qo(t){if(Array.isArray(t))return ni(t)}function Ho(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Vo(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ho(Object(r),!0).forEach((function(e){$o(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ho(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function $o(t,e,r){e=Ko(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Ko(t){var e=Xo(t,"string");return Ro(e)==="symbol"?e:String(e)}function Xo(t,e){if(Ro(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Ro(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function ti(t,e){return ii(t)||oi(t,e)||ri(t,e)||ei()}function ei(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ri(t,e){if(!t)return;if(typeof t==="string")return ni(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ni(t,e)}function ni(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function oi(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function ii(t){if(Array.isArray(t))return t}var ai=function t(e){var r;var o=e.label,i=e.field,l=e.fieldState,c=e.disabled,s=e.loading,h=e.placeholder,b=e.helpText,g=e.optionsWrapperStyle;var m=it({shouldFocusError:true});var w=m.watch("search");var x=Q(w,300);var _=jt(x);var k=Ct();var j=(0,u.useState)(false),E=ti(j,2),C=E[0],A=E[1];var P=(0,u.useState)(false),W=ti(P,2),I=W[0],L=W[1];var D=mt(),T=D.ref,J=D.isScrolling;(0,u.useEffect)((function(){if(!_.isLoading&&(_.data||[]).length>0){L(true)}}),[_.isLoading,_.data]);(0,u.useEffect)((function(){if(C){var t=setTimeout((function(){m.setFocus("name")}),250);return function(){clearTimeout(t)}}}),[C]);var M=(0,wt.l)({isOpen:C}),N=M.triggerRef,F=M.position,B=M.popoverRef;var z=(0,y.TQ)((r=_.data)!==null&&r!==void 0?r:[]);var R=function t(){A(false);m.reset({name:"",parent:null,search:w})};var U=function t(e){if(e.name){k.mutate(Vo({name:e.name},e.parent&&{parent:e.parent}));R()}};return(0,n.tZ)(Lt,{label:o,field:i,fieldState:l,loading:s,placeholder:h,helpText:b},(function(){var t;return(0,n.tZ)(zo.Fragment,null,(0,n.tZ)("div",{css:[si.options,g,true?"":0,true?"":0]},(0,n.tZ)("div",{css:si.categoryListWrapper,ref:T},(0,n.tZ)(S.Z,{when:!c&&(I||x)},(0,n.tZ)(p.Qr,{name:"search",control:m.control,render:function t(e){return(0,n.tZ)("div",{css:si.searchInput},(0,n.tZ)("div",{css:si.searchIcon},(0,n.tZ)(d.Z,{name:"search",width:24,height:24})),(0,n.tZ)("input",{type:"text",placeholder:(0,a.__)("Search","tutor"),value:w,disabled:c||s,onChange:function t(r){e.field.onChange(r.target.value)}}))}})),(0,n.tZ)(S.Z,{when:!_.isLoading&&!s,fallback:(0,n.tZ)(Z.g4,null)},(0,n.tZ)(S.Z,{when:z.length>0,fallback:(0,n.tZ)("span",{css:si.notFound},(0,a.__)("No categories found.","tutor"))},z.map((function(t,e){return(0,n.tZ)(ci,{key:t.id,disabled:c,option:t,value:i.value,isLastChild:e===z.length-1,onChange:function t(e){i.onChange((0,f.Uy)(i.value,(function(t){if(Array.isArray(t)){return t.includes(e)?t.filter((function(t){return t!==e})):[].concat(Go(t),[e])}return[e]})))}})}))))),(0,n.tZ)(S.Z,{when:!c},(0,n.tZ)("div",{ref:N,css:si.addButtonWrapper({isActive:J,hasCategories:_.isLoading||z.length>0})},(0,n.tZ)("button",{disabled:c||s,type:"button",css:si.addNewButton,onClick:function t(){return A(true)}},(0,n.tZ)(d.Z,{width:24,height:24,name:"plus"})," ",(0,a.__)("Add","tutor"))))),(0,n.tZ)(wt.h,{isOpen:C,onClickOutside:R,onEscape:R},(0,n.tZ)("div",{css:[si.categoryFormWrapper,(t={},$o(t,O.dZ?"right":"left",F.left),$o(t,"top",F.top),t),true?"":0,true?"":0],ref:B},(0,n.tZ)(p.Qr,{name:"name",control:m.control,rules:{required:(0,a.__)("Category name is required","tutor")},render:function t(e){return(0,n.tZ)(_o,Uo({},e,{placeholder:(0,a.__)("Category name","tutor"),selectOnFocus:true}))}}),(0,n.tZ)(p.Qr,{name:"parent",control:m.control,render:function t(e){return(0,n.tZ)(Mo,Uo({},e,{placeholder:(0,a.__)("Select parent","tutor"),clearable:!!e.field.value}))}}),(0,n.tZ)("div",{css:si.categoryFormButtons},(0,n.tZ)(v.Z,{variant:"text",size:"small",onClick:R},(0,a.__)("Cancel","tutor")),(0,n.tZ)(v.Z,{variant:"secondary",size:"small",loading:k.isPending,onClick:m.handleSubmit(U)},(0,a.__)("Ok","tutor"))))))}))};const ui=N(ai);var li=function t(e){return e.children.reduce((function(e,r){return e+t(r)}),e.children.length)};var ci=function t(e){var r=e.option,o=e.value,i=e.onChange,a=e.isLastChild,u=e.disabled;var l=li(r);var c=l>0;var s=(0,y.VH)(a,l);var d=function e(){if(!c){return null}return r.children.map((function(e,a){return(0,n.tZ)(t,{key:e.id,option:e,value:o,onChange:i,isLastChild:a===r.children.length-1,disabled:u})}))};return(0,n.tZ)("div",{css:si.branchItem({leftBarHeight:s,hasParent:r.parent!==0})},(0,n.tZ)(_,{checked:Array.isArray(o)?o.includes(r.id):o===r.id,label:(0,y.aV)(r.name),onChange:function t(){i(r.id)},labelCss:si.checkboxLabel,disabled:u}),d())};var si={options:(0,n.iv)("border:1px solid ",h.Jv.stroke["default"],";border-radius:",h.E0[8],";padding:",h.W0[8]," 0;background-color:",h.Jv.bg.white,";"+(true?"":0),true?"":0),categoryListWrapper:(0,n.iv)(At.i.overflowYAuto,";max-height:208px;"+(true?"":0),true?"":0),notFound:(0,n.iv)(At.i.display.flex(),";align-items:center;",b.c.caption("regular"),";padding:",h.W0[8]," ",h.W0[16],";color:",h.Jv.text.hints,";"+(true?"":0),true?"":0),searchInput:(0,n.iv)("position:sticky;top:0;padding:",h.W0[4]," ",h.W0[16],";background-color:",h.Jv.background.white,";z-index:",h.W5.dropdown,";input{",b.c.body("regular"),";width:100%;border-radius:",h.E0[6],";border:1px solid ",h.Jv.stroke["default"],";padding:",h.W0[4]," ",h.W0[16]," ",h.W0[4]," ",h.W0[32],";color:",h.Jv.text.title,";appearance:textfield;:focus{",At.i.inputFocus,";}}"+(true?"":0),true?"":0),searchIcon:(0,n.iv)("position:absolute;left:",h.W0[24],";top:50%;transform:translateY(-50%);color:",h.Jv.icon["default"],";display:flex;"+(true?"":0),true?"":0),checkboxLabel:(0,n.iv)("line-height:1.88rem!important;span:last-of-type{",At.i.text.ellipsis(1),";}"+(true?"":0),true?"":0),branchItem:function t(e){var r=e.leftBarHeight,o=e.hasParent;return(0,n.iv)("line-height:",h.W0[32],";position:relative;z-index:",h.W5.positive,";margin-inline:",h.W0[20]," ",h.W0[16],";&:after{content:'';position:absolute;height:",r,";width:1px;left:9px;top:26px;background-color:",h.Jv.stroke.divider,";z-index:",h.W5.level,";}",o&&(0,n.iv)("&:before{content:'';position:absolute;height:1px;width:10px;left:-10px;top:",h.W0[16],";background-color:",h.Jv.stroke.divider,";z-index:",h.W5.level,";}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},addNewButton:(0,n.iv)(At.i.resetButton,";",b.c.small("medium"),";color:",h.Jv.brand.blue,";padding:0 ",h.W0[8],";display:flex;align-items:center;border-radius:",h.E0[2],";&:focus,&:active,&:hover{background:none;color:",h.Jv.brand.blue,";}&:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";outline-offset:1px;}&:disabled{color:",h.Jv.text.disable,";}"+(true?"":0),true?"":0),categoryFormWrapper:(0,n.iv)("position:absolute;background-color:",h.Jv.background.white,";box-shadow:",h.AF.popover,";border-radius:",h.E0[6],";border:1px solid ",h.Jv.stroke.border,";padding:",h.W0[16],";min-width:306px;display:flex;flex-direction:column;gap:",h.W0[12],";"+(true?"":0),true?"":0),categoryFormButtons:(0,n.iv)("display:flex;justify-content:end;gap:",h.W0[8],";"+(true?"":0),true?"":0),addButtonWrapper:function t(e){var r=e.isActive,o=r===void 0?false:r,i=e.hasCategories,a=i===void 0?false:i;return(0,n.iv)("transition:box-shadow 0.3s ease-in-out;padding-inline:",h.W0[8],";padding-block:",a?h.W0[4]:"0px",";",o&&(0,n.iv)("box-shadow:",h.AF.scrollable,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}};var di=r(5581);var fi=r.n(di);function pi(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var vi={large:"regular",regular:"small",small:"small"};var hi=function t(e){var r=e.buttonText,o=r===void 0?(0,a.__)("Upload Media","tutor"):r,i=e.infoText,u=e.size,l=u===void 0?"regular":u,c=e.value,s=e.uploadHandler,f=e.clearHandler,p=e.emptyImageCss,b=e.previewImageCss,y=e.overlayCss,g=e.replaceButtonText,m=e.loading,w=e.disabled,x=w===void 0?false:w,_=e.isClearAble,O=_===void 0?true:_;return(0,n.tZ)(S.Z,{when:!m,fallback:(0,n.tZ)("div",{css:mi.emptyMedia({size:l,isDisabled:x})},(0,n.tZ)(Z.fz,null))},(0,n.tZ)(S.Z,{when:c===null||c===void 0?void 0:c.url,fallback:(0,n.tZ)("div",{"aria-disabled":x,css:[mi.emptyMedia({size:l,isDisabled:x}),p,true?"":0,true?"":0],onClick:function t(e){e.stopPropagation();if(x){return}s()},onKeyDown:function t(e){if(!x&&e.key==="Enter"){e.preventDefault();s()}}},(0,n.tZ)(d.Z,{name:"addImage",width:32,height:32}),(0,n.tZ)(v.Z,{disabled:x,size:vi[l],variant:"secondary",buttonContentCss:mi.buttonText,"data-cy":"upload-media"},o),(0,n.tZ)(S.Z,{when:i},(0,n.tZ)("p",{css:mi.infoTexts},i)))},(function(t){return(0,n.tZ)("div",{css:[mi.previewWrapper({size:l,isDisabled:x}),b,true?"":0,true?"":0],"data-cy":"media-preview"},(0,n.tZ)("img",{src:t,alt:c===null||c===void 0?void 0:c.title,css:mi.imagePreview}),(0,n.tZ)("div",{css:[mi.hoverPreview,y,true?"":0,true?"":0],"data-hover-buttons-wrapper":true},(0,n.tZ)(v.Z,{disabled:x,variant:"secondary",size:vi[l],buttonCss:(0,n.iv)("margin-top:",O&&h.W0[16],";"+(true?"":0),true?"":0),onClick:function t(e){e.stopPropagation();s()},"data-cy":"replace-media"},g||(0,a.__)("Replace Image","tutor")),(0,n.tZ)(S.Z,{when:O},(0,n.tZ)(v.Z,{disabled:x,variant:"text",size:vi[l],onClick:function t(e){e.stopPropagation();f()},"data-cy":"clear-media"},(0,a.__)("Remove","tutor")))))})))};const bi=hi;var yi=true?{name:"1kn988u",styles:"width:168px"}:0;var gi=true?{name:"1kn988u",styles:"width:168px"}:0;var mi={emptyMedia:function t(e){var r=e.size,o=e.isDisabled;return(0,n.iv)("width:100%;height:168px;display:flex;flex-direction:column;align-items:center;justify-content:center;gap:",h.W0[8],";border:1px dashed ",h.Jv.stroke.border,";border-radius:",h.E0[8],";background-color:",h.Jv.bg.white,";overflow:hidden;cursor:",o?"not-allowed":"pointer",";",r==="small"&&gi," svg{color:",h.Jv.icon["default"],";}&:hover svg{color:",!o&&h.Jv.brand.blue,";}"+(true?"":0),true?"":0)},buttonText:(0,n.iv)("color:",h.Jv.text.brand,";"+(true?"":0),true?"":0),infoTexts:(0,n.iv)(b.c.tiny(),";color:",h.Jv.text.subdued,";text-align:center;"+(true?"":0),true?"":0),previewWrapper:function t(e){var r=e.size,o=e.isDisabled;return(0,n.iv)("width:100%;height:168px;border:1px solid ",h.Jv.stroke["default"],";border-radius:",h.E0[8],";overflow:hidden;position:relative;background-color:",h.Jv.bg.white,";",r==="small"&&yi," &:hover{[data-hover-buttons-wrapper]{display:",o?"none":"flex",";opacity:",o?0:1,";}}"+(true?"":0),true?"":0)},imagePreview:true?{name:"1obrg50",styles:"height:100%;width:100%;object-fit:contain"}:0,hoverPreview:(0,n.iv)("display:flex;flex-direction:column;justify-content:center;align-items:center;gap:",h.W0[8],";opacity:0;position:absolute;inset:0;background-color:",fi()(h.Jv.color.black.main,.6),";button:first-of-type{box-shadow:",h.AF.button,";}button:last-of-type:not(:only-of-type){color:",h.Jv.text.white,";box-shadow:none;}"+(true?"":0),true?"":0)};function wi(t,e){return Si(t)||Oi(t,e)||_i(t,e)||xi()}function xi(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _i(t,e){if(!t)return;if(typeof t==="string")return Zi(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Zi(t,e)}function Zi(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Oi(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Si(t){if(Array.isArray(t))return t}var ki=[(0,a.__)("A serene classroom setting with books and a chalkboard","tutor"),(0,a.__)("An abstract representation of innovation and creativity","tutor"),(0,a.__)("A vibrant workspace with a laptop and coffee cup","tutor"),(0,a.__)("A modern design with digital learning icons","tutor"),(0,a.__)("A futuristic cityscape with a glowing pathway","tutor"),(0,a.__)("A peaceful nature scene with soft colors","tutor"),(0,a.__)("A professional boardroom with sleek visuals","tutor"),(0,a.__)("A stack of books with warm, inviting lighting","tutor"),(0,a.__)("A dynamic collage of technology and education themes","tutor"),(0,a.__)("A bold and minimalistic design with striking colors","tutor")];var ji=l().createContext(null);var Ei=function t(){var e=(0,u.useContext)(ji);if(!e){throw new Error("useMagicImageGeneration must be used within MagicImageGenerationProvider.")}return e};var Ci=function t(e){var r=e.children,o=e.field,i=e.fieldState,a=e.onCloseModal;var l=it({defaultValues:{prompt:"",style:"none"}});var c=(0,u.useState)("generation"),s=wi(c,2),d=s[0],f=s[1];var v=(0,u.useState)(""),h=wi(v,2),b=h[0],y=h[1];var g=(0,u.useState)([null,null,null,null]),m=wi(g,2),w=m[0],x=m[1];var _=(0,u.useCallback)((function(t){f(t)}),[]);return(0,n.tZ)(ji.Provider,{value:{state:d,onDropdownMenuChange:_,images:w,setImages:x,currentImage:b,setCurrentImage:y,field:o,fieldState:i,onCloseModal:a}},(0,n.tZ)(p.RV,l,r))};var Ai=function t(e){var r=e.field,o=e.fieldState,i=e.label,a=e.options,u=a===void 0?[]:a,l=e.disabled;return(0,n.tZ)(Lt,{field:r,fieldState:o,label:i,disabled:l},(function(){return(0,n.tZ)("div",{css:Wi.wrapper},u.map((function(t,e){return(0,n.tZ)("button",{type:"button",key:e,css:Wi.item(r.value===t.value),onClick:function e(){r.onChange(t.value)},disabled:l},(0,n.tZ)("img",{src:t.image,alt:t.label,width:64,height:64}),(0,n.tZ)("p",null,t.label))})))}))};const Pi=Ai;var Wi={wrapper:(0,n.iv)("display:grid;grid-template-columns:repeat(4, minmax(64px, 1fr));gap:",h.W0[12],";margin-top:",h.W0[4],";"+(true?"":0),true?"":0),item:function t(e){return(0,n.iv)(At.i.resetButton,";display:flex;flex-direction:column;gap:",h.W0[4],";align-items:center;width:100%;cursor:pointer;input{appearance:none;}p{",b.c.small(),";width:100%;",At.i.textEllipsis,";color:",h.Jv.text.subdued,";text-align:center;}&:hover,&:focus-visible{",!e&&(0,n.iv)("img{border-color:",h.Jv.stroke.hover,";}"+(true?"":0),true?"":0),";}img{border-radius:",h.E0[6],";border:2px solid ",h.Jv.stroke.border,";outline:2px solid transparent;outline-offset:2px;transition:border-color 0.3s ease;",e&&(0,n.iv)("outline-color:",h.Jv.stroke.magicAi,";"+(true?"":0),true?"":0),";}"+(true?"":0),true?"":0)}};const Ii=r.p+"images/56f20c93d8e28423f724fe4e914fbd21-3d.png";const Li=r.p+"images/7a53b07b7f13e48b7b7b47dff35d9946-black-and-white.png";const Di=r.p+"images/9613f2a35fc147cbde38998fc279f6e9-concept.png";const Ti=r.p+"images/ff5a8a3d6c18c02f00d659da3824176b-dreamy.png";const Ji=r.p+"images/bff40839481a6e109932774fea006137-filmic.png";const Mi=r.p+"images/dec5e33b385ba1a7c841dde2b6c1a5af-illustration.png";const Ni=r.p+"images/83571e85f649c56b82349466a5b4c844-neon.png";const Fi=r.p+"images/9dcf3f4907036dd08b31bf2a7181bed0-none.jpg";const Bi=r.p+"images/fc8edfd709e8f6ed349b59a0f0a00647-painting.png";const zi=r.p+"images/32925d4873712d856f4abc340b3334cb-photo.png";const Ri=r.p+"images/fb8df26f9102747dfafc31d912d6d074-retro.png";const Ui=r.p+"images/7c935ca7690aecae8c42142d8cec660e-sketch.png";function Gi(t,e){t.lineTo(e.x,e.y);t.stroke()}function Qi(t,e){var r=e.x-t.x;var n=e.y-t.y;return Math.sqrt(r*r+n*n)}function Yi(t){var e=atob(t.split(",")[1]);var r=t.split(",")[0].split(":")[1].split(";")[0];var n=new ArrayBuffer(e.length);var o=new Uint8Array(n);for(var i=0;i<e.length;i++){o[i]=e.charCodeAt(i)}return new Blob([n],{type:r})}function qi(t,e){var r=Yi(t);var n=document.createElement("a");n.href=URL.createObjectURL(r);n.download=e;document.body.appendChild(n);n.click();document.body.removeChild(n)}function Hi(t,e){var r=document.createElement("canvas");r.width=1024;r.height=1024;var n=r.getContext("2d");n===null||n===void 0?void 0:n.putImageData(t,0,0);n===null||n===void 0?void 0:n.drawImage(r,0,0,1024,1024);return new Promise((function(t){r.toBlob((function(r){if(!r){t(null);return}t(new File([r],e,{type:"image/png"}))}))}))}var Vi=function t(e){if(e&&typeof e!=="function"&&e.current){var r=e.current;var n=r.getContext("2d");return{canvas:r,context:n}}return{canvas:null,context:null}};var $i=function t(e){return e.toDataURL("image/png")};var Ki=r(7363);function Xi(t){"@babel/helpers - typeof";return Xi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Xi(t)}var ta;function ea(t,e){if(!e){e=t.slice(0)}return Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(e)}}))}function ra(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ra=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(k([])));y&&y!==e&&r.call(y,i)&&(h=y);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==Xi(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function na(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function oa(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){na(i,n,o,a,u,"next",t)}function u(t){na(i,n,o,a,u,"throw",t)}a(undefined)}))}}function ia(t,e){return sa(t)||ca(t,e)||ua(t,e)||aa()}function aa(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ua(t,e){if(!t)return;if(typeof t==="string")return la(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return la(t,e)}function la(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ca(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function sa(t){if(Array.isArray(t))return t}function da(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var fa=[{label:(0,a.__)("Magic Fill","tutor"),value:"magic-fill",icon:(0,n.tZ)(d.Z,{name:"magicWand",width:24,height:24})},{label:(0,a.__)("Download","tutor"),value:"download",icon:(0,n.tZ)(d.Z,{name:"download",width:24,height:24})}];var pa=function t(e){var r=e.src,o=e.loading,i=e.index;var l=(0,u.useRef)(null);var c=(0,u.useState)(false),s=ia(c,2),f=s[0],p=s[1];var v=Ei(),h=v.onDropdownMenuChange,b=v.setCurrentImage,g=v.onCloseModal,m=v.field;var w=wr();if(o||!r){return(0,n.tZ)("div",{css:ma.loader(i+1)})}return(0,n.tZ)(Ki.Fragment,null,(0,n.tZ)("div",{css:ma.image({isActive:w.isPending})},(0,n.tZ)("img",{src:r,alt:(0,a.__)("Generated Image","tutor")}),(0,n.tZ)("div",{"data-actions":true},(0,n.tZ)("div",{css:ma.useButton},(0,n.tZ)(qt,{variant:"primary",disabled:w.isPending,onClick:oa(ra().mark((function t(){var e;return ra().wrap((function t(n){while(1)switch(n.prev=n.next){case 0:if(r){n.next=2;break}return n.abrupt("return");case 2:n.next=4;return w.mutateAsync({image:r});case 4:e=n.sent;if(e.data){m.onChange(e.data);g()}case 6:case"end":return n.stop()}}),t)}))),loading:w.isPending},(0,n.tZ)(d.Z,{name:"download",width:24,height:24}),(0,a.__)("Use This","tutor"))),(0,n.tZ)(qt,{variant:"primary",size:"icon",css:ma.threeDots,ref:l,onClick:function t(){return p(true)}},(0,n.tZ)(d.Z,{name:"threeDotsVertical",width:24,height:24})))),(0,n.tZ)($t.Z,{triggerRef:l,isOpen:f,closePopover:function t(){p(false)},animationType:or.ru.slideDown,maxWidth:"160px"},(0,n.tZ)("div",{css:ma.dropdownOptions},(0,n.tZ)(ue,{each:fa},(function(t,e){return(0,n.tZ)("button",{type:"button",key:e,css:ma.dropdownItem,onClick:function e(){switch(t.value){case"magic-fill":{b(r);h(t.value);break}case"download":{var n="".concat((0,y.x0)(),".png");qi(r,n);break}default:break}p(false)}},t.icon,t.label)})))))};var va=(0,n.F4)(ta||(ta=ea(["\n\t\t0% {\n      opacity: 0.3;\n    }\n\t\t25% {\n\t\t\topacity: 0.5;\n\t\t}\n    50% {\n      opacity: 0.7;\n    }\n\t\t75% {\n\t\t\topacity: 0.5;\n\t\t}\n    100% {\n      opacity: 0.3;\n    }\n"])));var ha=true?{name:"net8hi",styles:"background-position:bottom right;animation-delay:1s"}:0;var ba=true?{name:"15lylfh",styles:"background-position:bottom left;animation-delay:1.5s"}:0;var ya=true?{name:"1ooocct",styles:"background-position:top right;animation-delay:0.5s"}:0;var ga=true?{name:"1auq8ax",styles:"background-position:top left"}:0;var ma={loader:function t(e){return(0,n.iv)("border-radius:",h.E0[12],";background:linear-gradient(\n      73.09deg,\n      #ff9645 18.05%,\n      #ff6471 30.25%,\n      #cf6ebd 55.42%,\n      #a477d1 71.66%,\n      #3e64de 97.9%\n    );position:relative;width:100%;height:100%;background-size:612px 612px;opacity:0.3;transition:opacity 0.5s ease;animation:",va," 2s linear infinite;",e===1&&ga," ",e===2&&ya," ",e===3&&ba," ",e===4&&ha,";"+(true?"":0),true?"":0)},image:function t(e){var r=e.isActive;return(0,n.iv)("width:100%;height:100%;overflow:hidden;border-radius:",h.E0[12],";position:relative;outline:2px solid transparent;outline-offset:2px;transition:border-radius 0.3s ease;[data-actions]{opacity:0;transition:opacity 0.3s ease;}img{position:absolute;top:0;left:0;width:100%;height:100%;object-fit:cover;}",r&&(0,n.iv)("outline-color:",h.Jv.stroke.brand,";[data-actions]{opacity:1;}"+(true?"":0),true?"":0)," &:hover,&:focus-within{outline-color:",h.Jv.stroke.brand,";[data-actions]{opacity:1;}}"+(true?"":0),true?"":0)},threeDots:(0,n.iv)("position:absolute;top:",h.W0[8],";right:",h.W0[8],";border-radius:",h.E0[4],";"+(true?"":0),true?"":0),useButton:(0,n.iv)("position:absolute;left:50%;bottom:",h.W0[12],";transform:translateX(-50%);button{display:inline-flex;align-items:center;gap:",h.W0[4],";}"+(true?"":0),true?"":0),dropdownOptions:(0,n.iv)("display:flex;flex-direction:column;padding-block:",h.W0[8],";"+(true?"":0),true?"":0),dropdownItem:(0,n.iv)(b.c.small(),";",At.i.resetButton,";height:40px;display:flex;gap:",h.W0[10],";align-items:center;transition:background-color 0.3s ease;color:",h.Jv.text.title,";padding-inline:",h.W0[8],";cursor:pointer;svg{color:",h.Jv.icon["default"],";}&:hover{background-color:",h.Jv.background.hover,";}"+(true?"":0),true?"":0)};var wa={wrapper:(0,n.iv)("min-width:1000px;display:grid;grid-template-columns:1fr 330px;",h.Uo.tablet,"{min-width:auto;grid-template-columns:1fr;width:100%;}"+(true?"":0),true?"":0),left:(0,n.iv)("display:flex;justify-content:center;align-items:center;background-color:#f7f7f7;z-index:",h.W5.level,";"+(true?"":0),true?"":0),right:(0,n.iv)("padding:",h.W0[20],";display:flex;flex-direction:column;align-items:space-between;z-index:",h.W5.positive,";"+(true?"":0),true?"":0),rightFooter:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[8],";margin-top:auto;padding-top:80px;"+(true?"":0),true?"":0)};function xa(t){"@babel/helpers - typeof";return xa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},xa(t)}function _a(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Za(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?_a(Object(r),!0).forEach((function(e){Oa(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):_a(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Oa(t,e,r){e=Sa(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Sa(t){var e=ka(t,"string");return xa(e)==="symbol"?e:String(e)}function ka(t,e){if(xa(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(xa(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function ja(){ja=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return ja.apply(this,arguments)}function Ea(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Ea=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(k([])));y&&y!==e&&r.call(y,i)&&(h=y);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==xa(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function Ca(t){return Wa(t)||Pa(t)||Ja(t)||Aa()}function Aa(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Pa(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function Wa(t){if(Array.isArray(t))return Ma(t)}function Ia(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function La(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Ia(i,n,o,a,u,"next",t)}function u(t){Ia(i,n,o,a,u,"throw",t)}a(undefined)}))}}function Da(t,e){return Fa(t)||Na(t,e)||Ja(t,e)||Ta()}function Ta(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Ja(t,e){if(!t)return;if(typeof t==="string")return Ma(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ma(t,e)}function Ma(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Na(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Fa(t){if(Array.isArray(t))return t}var Ba=[{label:(0,a.__)("None","tutor"),value:"none",image:Fi},{label:(0,a.__)("Photo","tutor"),value:"photo",image:zi},{label:(0,a.__)("Neon","tutor"),value:"neon",image:Ni},{label:(0,a.__)("3D","tutor"),value:"3d",image:Ii},{label:(0,a.__)("Painting","tutor"),value:"painting",image:Bi},{label:(0,a.__)("Sketch","tutor"),value:"sketch",image:Ui},{label:(0,a.__)("Concept","tutor"),value:"concept_art",image:Di},{label:(0,a.__)("Illustration","tutor"),value:"illustration",image:Mi},{label:(0,a.__)("Dreamy","tutor"),value:"dreamy",image:Ti},{label:(0,a.__)("Filmic","tutor"),value:"filmic",image:Ji},{label:(0,a.__)("Retro","tutor"),value:"retrowave",image:Ri},{label:(0,a.__)("Black & White","tutor"),value:"black-and-white",image:Li}];var za=function t(){var e=(0,p.cI)({defaultValues:{style:"none",prompt:""}});var r=Ei(),o=r.images,i=r.setImages;var l=fr();var c=(0,Zt.p)(),s=c.showToast;var f=(0,u.useState)(o.every((function(t){return t===null}))),v=Da(f,2),h=v[0],b=v[1];var y=(0,u.useState)([false,false,false,false]),g=Da(y,2),m=g[0],w=g[1];var x=e.watch("style");var _=e.watch("prompt");var Z=!x||!_;var O=o.some(j.$K);(0,u.useEffect)((function(){if(l.isError){s({type:"danger",message:l.error.response.data.message})}}),[l.isError]);(0,u.useEffect)((function(){e.setFocus("prompt")}),[]);return(0,n.tZ)("form",{css:wa.wrapper,onSubmit:e.handleSubmit(function(){var t=La(Ea().mark((function t(e){return Ea().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:w([true,true,true,true]);b(false);r.prev=2;r.next=5;return Promise.all(Array.from({length:4}).map((function(t,r){return l.mutateAsync(e).then((function(t){i((function(e){var n,o,i;var a=Ca(e);a[r]=(n=(o=t.data.data)===null||o===void 0?void 0:(i=o[0])===null||i===void 0?void 0:i.b64_json)!==null&&n!==void 0?n:null;return a}));w((function(t){var e=Ca(t);e[r]=false;return e}))}))["catch"]((function(t){w((function(t){var e=Ca(t);e[r]=false;return e}));throw t}))})));case 5:r.next=11;break;case 7:r.prev=7;r.t0=r["catch"](2);w([false,false,false,false]);b(true);case 11:case"end":return r.stop()}}),t,null,[[2,7]])})));return function(e){return t.apply(this,arguments)}}())},(0,n.tZ)("div",{css:wa.left},(0,n.tZ)(S.Z,{when:!h,fallback:(0,n.tZ)(d.Z,{name:"magicAiPlaceholder",width:72,height:72})},(0,n.tZ)("div",{css:Ra.images},(0,n.tZ)(ue,{each:o},(function(t,e){return(0,n.tZ)(pa,{key:e,src:t,loading:m[e],index:e})}))))),(0,n.tZ)("div",{css:wa.right},(0,n.tZ)("div",{css:Ra.fields},(0,n.tZ)("div",{css:Ra.promptWrapper},(0,n.tZ)(p.Qr,{control:e.control,name:"prompt",render:function t(e){return(0,n.tZ)(ne,ja({},e,{label:(0,a.__)("Visualize Your Course","tutor"),placeholder:(0,a.__)("Describe the image you want for your course thumbnail","tutor"),rows:4,isMagicAi:true,disabled:l.isPending,enableResize:false}))}}),(0,n.tZ)("button",{type:"button",css:Ra.inspireButton,onClick:function t(){var r=ki.length;var n=Math.floor(Math.random()*r);e.reset(Za(Za({},e.getValues()),{},{prompt:ki[n]}))},disabled:l.isPending},(0,n.tZ)(d.Z,{name:"bulbLine"}),(0,a.__)("Inspire Me","tutor"))),(0,n.tZ)(p.Qr,{control:e.control,name:"style",render:function t(e){return(0,n.tZ)(Pi,ja({},e,{label:(0,a.__)("Styles","tutor"),options:Ba,disabled:l.isPending}))}})),(0,n.tZ)("div",{css:wa.rightFooter},(0,n.tZ)(qt,{type:"submit",disabled:l.isPending||Z},(0,n.tZ)(d.Z,{name:O?"reload":"magicAi",width:24,height:24}),O?(0,a.__)("Generate Again","tutor"):(0,a.__)("Generate Now","tutor")))))};var Ra={images:(0,n.iv)("display:grid;grid-template-columns:repeat(2, minmax(150px, 1fr));grid-template-rows:repeat(2, minmax(150px, 1fr));gap:",h.W0[12],";align-self:start;padding:",h.W0[24],";width:100%;height:100%;>div{aspect-ratio:1/1;}"+(true?"":0),true?"":0),fields:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[12],";"+(true?"":0),true?"":0),promptWrapper:(0,n.iv)("position:relative;textarea{padding-bottom:",h.W0[40],"!important;}"+(true?"":0),true?"":0),inspireButton:(0,n.iv)(At.i.resetButton,";",b.c.small(),";position:absolute;height:28px;bottom:",h.W0[12],";left:",h.W0[12],";border:1px solid ",h.Jv.stroke.brand,";border-radius:",h.E0[4],";display:flex;align-items:center;gap:",h.W0[4],";color:",h.Jv.text.brand,";padding-inline:",h.W0[12],";background-color:",h.Jv.background.white,";&:hover{background-color:",h.Jv.background.brand,";color:",h.Jv.text.white,";}&:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";outline-offset:1px;}&:disabled{background-color:",h.Jv.background.disable,";color:",h.Jv.text.disable,";}"+(true?"":0),true?"":0)};function Ua(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Ga=l().forwardRef((function(t,e){var r=t.className,o=t.variant;return(0,n.tZ)("div",{className:r,ref:e,css:Ya({variant:o})})}));Ga.displayName="Separator";var Qa={horizontal:true?{name:"d6rgw1",styles:"height:1px;width:100%"}:0,vertical:true?{name:"cw4fps",styles:"height:100%;width:1px"}:0,base:(0,n.iv)("flex-shrink:0;background-color:",h.Jv.stroke.divider,";"+(true?"":0),true?"":0)};var Ya=(0,Bt.Y)({variants:{variant:{horizontal:Qa.horizontal,vertical:Qa.vertical}},defaultVariants:{variant:"horizontal"}},Qa.base);function qa(t,e){return Xa(t)||Ka(t,e)||Va(t,e)||Ha()}function Ha(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Va(t,e){if(!t)return;if(typeof t==="string")return $a(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return $a(t,e)}function $a(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Ka(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Xa(t){if(Array.isArray(t))return t}function tu(t,e,r,n){if(!e.current){return 0}var o=e.current.getBoundingClientRect();var i=o.width;var a=t-o.left;var u=Math.max(0,Math.min(a,i));var l=u/i*100;var c=Math.floor(r+l/100*(n-r));return c}var eu=function t(e){var r=e.field,o=e.fieldState,i=e.label,a=e.min,l=a===void 0?0:a,c=e.max,s=c===void 0?100:c,d=e.isMagicAi,f=d===void 0?false:d,p=e.hasBorder,v=p===void 0?false:p;var h=(0,u.useRef)(null);var b=(0,u.useState)(r.value),g=qa(b,2),m=g[0],w=g[1];var x=(0,u.useRef)(null);var _=(0,u.useRef)(null);var Z=Q(m);(0,u.useEffect)((function(){r.onChange(Z)}),[Z,r.onChange]);(0,u.useEffect)((function(){var t=false;var e=function e(r){if(r.target!==_.current){return}t=true;document.body.style.userSelect="none"};var r=function e(r){if(!t||!x.current){return}w(tu(r.clientX,x,l,s))};var n=function e(){t=false;document.body.style.userSelect="auto"};window.addEventListener("mousedown",e);window.addEventListener("mousemove",r);window.addEventListener("mouseup",n);return function(){window.removeEventListener("mousedown",e);window.removeEventListener("mousemove",r);window.removeEventListener("mouseup",n)}}),[l,s]);var O=(0,u.useMemo)((function(){return Math.floor((m-l)/(s-l)*100)}),[m,l,s]);return(0,n.tZ)(Lt,{field:r,fieldState:o,label:i,isMagicAi:f},(function(){var t;return(0,n.tZ)("div",{css:nu.wrapper(v)},(0,n.tZ)("div",{css:nu.track,ref:x,onKeyDown:y.ZT,onClick:function t(e){w(tu(e.clientX,x,l,s))}},(0,n.tZ)("div",{css:nu.fill,style:{width:"".concat(O,"%")}}),(0,n.tZ)("div",{css:nu.thumb(f),style:{left:"".concat(O,"%")},ref:_})),(0,n.tZ)("input",{type:"text",css:nu.input,value:(t=String(m))!==null&&t!==void 0?t:"",onChange:function t(e){w(Number(e.target.value))},ref:h,onFocus:function t(){var e;(e=h.current)===null||e===void 0?void 0:e.select()}}))}))};const ru=eu;var nu={wrapper:function t(e){return(0,n.iv)("display:grid;grid-template-columns:1fr 45px;gap:",h.W0[20],";align-items:center;",e&&(0,n.iv)("border:1px solid ",h.Jv.stroke.disable,";border-radius:",h.E0[6],";padding:",h.W0[12]," ",h.W0[10]," ",h.W0[12]," ",h.W0[16],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},track:(0,n.iv)("position:relative;height:4px;background-color:",h.Jv.bg.gray20,";border-radius:",h.E0[50],";width:100%;flex-shrink:0;cursor:pointer;"+(true?"":0),true?"":0),fill:(0,n.iv)("position:absolute;left:0;top:0;height:100%;background:",h.Jv.ai.gradient_1,";width:50%;border-radius:",h.E0[50],";"+(true?"":0),true?"":0),thumb:function t(e){return(0,n.iv)("position:absolute;top:50%;transform:translate(-50%, -50%);width:20px;height:20px;border-radius:",h.E0.circle,";&::before{content:'';position:absolute;top:50%;left:50%;width:8px;height:8px;transform:translate(-50%, -50%);border-radius:",h.E0.circle,";background-color:",h.Jv.background.white,";cursor:pointer;}",e&&(0,n.iv)("background:",h.Jv.ai.gradient_1,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},input:(0,n.iv)(b.c.caption("medium"),";height:32px;border:1px solid ",h.Jv.stroke.border,";border-radius:",h.E0[6],";text-align:center;color:",h.Jv.text.primary,";&:focus-visible{",At.i.inputFocus,";}"+(true?"":0),true?"":0)};function ou(t){return uu(t)||au(t)||su(t)||iu()}function iu(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function au(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function uu(t){if(Array.isArray(t))return du(t)}function lu(t,e){return pu(t)||fu(t,e)||su(t,e)||cu()}function cu(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function su(t,e){if(!t)return;if(typeof t==="string")return du(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return du(t,e)}function du(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function fu(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function pu(t){if(Array.isArray(t))return t}function vu(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var hu=l().forwardRef((function(t,e){var r=t.src,o=t.width,i=t.height,a=t.brushSize,l=t.trackStack,c=t.pointer,s=t.setTrackStack,d=t.setPointer;var f=(0,u.useState)(false),p=lu(f,2),v=p[0],h=p[1];var b=(0,u.useState)({x:0,y:0}),y=lu(b,2),g=y[0],m=y[1];var w=(0,u.useRef)(null);var x=function t(r){var n=Vi(e),o=n.canvas,i=n.context;if(!o||!i){return}var a=o.getBoundingClientRect();var u=(r.clientX-a.left)*(o.width/a.width);var l=(r.clientY-a.top)*(o.height/a.height);i.globalCompositeOperation="destination-out";i.beginPath();i.moveTo(u,l);h(true);m({x:u,y:l})};var _=function t(r){var n=Vi(e),o=n.canvas,i=n.context;if(!o||!i||!w.current){return}var a=o.getBoundingClientRect();var u={x:(r.clientX-a.left)*(o.width/a.width),y:(r.clientY-a.top)*(o.height/a.height)};if(v){Gi(i,u)}w.current.style.left="".concat(u.x,"px");w.current.style.top="".concat(u.y,"px")};var Z=function t(r){var n=Vi(e),o=n.canvas,i=n.context;if(!i||!o){return}h(false);i.closePath();var a=o.getBoundingClientRect();var u={x:(r.clientX-a.left)*(o.width/a.width),y:(r.clientY-a.top)*(o.height/a.height)};if(Qi(g,u)===0){Gi(i,{x:u.x+1,y:u.y+1})}s((function(t){var e=t.slice(0,c);return[].concat(ou(e),[i.getImageData(0,0,1024,1024)])}));d((function(t){return t+1}))};var O=function t(){var n=Vi(e),o=n.canvas,i=n.context;if(!o||!i){return}var a=new Image;a.src=r;a.onload=function(){i.clearRect(0,0,o.width,o.height);var t=a.width/a.height;var e=o.width/o.height;var r;var n;if(e>t){n=o.height;r=o.height*t}else{r=o.width;n=o.width/t}var u=(o.width-r)/2;var c=(o.height-n)/2;i.drawImage(a,u,c,r,n);if(l.length===0){s([i.getImageData(0,0,o.width,o.height)])}};i.lineJoin="round";i.lineCap="round"};var S=function t(){if(!w.current){return}document.body.style.cursor="none";w.current.style.display="block"};var k=function t(){if(!w.current){return}document.body.style.cursor="auto";w.current.style.display="none"};(0,u.useEffect)((function(){O()}),[]);return(0,n.tZ)("div",{css:bu.wrapper},(0,n.tZ)("canvas",{ref:e,width:o,height:i,onMouseDown:x,onMouseMove:_,onMouseUp:Z,onMouseEnter:S,onMouseLeave:k}),(0,n.tZ)("div",{ref:w,css:bu.customCursor(a)}))}));var bu={wrapper:true?{name:"bjn8wh",styles:"position:relative"}:0,customCursor:function t(e){return(0,n.iv)("position:absolute;width:",e,"px;height:",e,"px;border-radius:",h.E0.circle,";background:linear-gradient(\n      73.09deg,\n      rgba(255, 150, 69, 0.4) 18.05%,\n      rgba(255, 100, 113, 0.4) 30.25%,\n      rgba(207, 110, 189, 0.4) 55.42%,\n      rgba(164, 119, 209, 0.4) 71.66%,\n      rgba(62, 100, 222, 0.4) 97.9%\n    );border:3px solid ",h.Jv.stroke.white,";pointer-events:none;transform:translate(-50%, -50%);z-index:",h.W5.highest,";display:none;"+(true?"":0),true?"":0)}};function yu(t){"@babel/helpers - typeof";return yu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},yu(t)}var gu,mu;function wu(t,e){if(!e){e=t.slice(0)}return Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(e)}}))}function xu(){xu=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return xu.apply(this,arguments)}function _u(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */_u=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(k([])));y&&y!==e&&r.call(y,i)&&(h=y);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==yu(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function Zu(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function Ou(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Zu(i,n,o,a,u,"next",t)}function u(t){Zu(i,n,o,a,u,"throw",t)}a(undefined)}))}}function Su(t,e){var r=typeof Symbol!=="undefined"&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=Eu(t))||e&&t&&typeof t.length==="number"){if(r)t=r;var n=0;var o=function t(){};return{s:o,n:function e(){if(n>=t.length)return{done:true};return{done:false,value:t[n++]}},e:function t(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i=true,a=false,u;return{s:function e(){r=r.call(t)},n:function t(){var e=r.next();i=e.done;return e},e:function t(e){a=true;u=e},f:function t(){try{if(!i&&r["return"]!=null)r["return"]()}finally{if(a)throw u}}}}function ku(t,e){return Pu(t)||Au(t,e)||Eu(t,e)||ju()}function ju(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Eu(t,e){if(!t)return;if(typeof t==="string")return Cu(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Cu(t,e)}function Cu(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Au(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Pu(t){if(Array.isArray(t))return t}function Wu(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Iu=620;var Lu=620;var Du=true?{name:"xdvdnl",styles:"margin-top:auto"}:0;var Tu=true?{name:"innv7",styles:"min-height:16px"}:0;var Ju=function t(){var e=it({defaultValues:{brush_size:40,prompt:""}});var r=vr();var o=(0,u.useRef)(null);var i=Ei(),l=i.onDropdownMenuChange,c=i.currentImage,s=i.field,f=i.onCloseModal;var v=wr();var h=Q(e.watch("brush_size",40));var b=(0,u.useState)([]),g=ku(b,2),m=g[0],w=g[1];var x=(0,u.useState)(1),_=ku(x,2),Z=_[0],O=_[1];var k=(0,u.useCallback)((function(t,e){var r;var n=(r=o.current)===null||r===void 0?void 0:r.getContext("2d");if(!n){return}var i=Su(e.slice(0,t)),a;try{for(i.s();!(a=i.n()).done;){var u=a.value;n.putImageData(u,0,0)}}catch(t){i.e(t)}finally{i.f()}}),[]);(0,u.useEffect)((function(){var t;var e=(t=o.current)===null||t===void 0?void 0:t.getContext("2d");if(!e){return}e.lineWidth=h}),[h]);(0,u.useEffect)((function(){var t=function t(e){if(e.metaKey){if(e.shiftKey&&e.key.toUpperCase()==="Z"){k(Z+1,m);O((function(t){return Math.min(t+1,m.length)}));return}if(e.key.toUpperCase()==="Z"){k(Z-1,m);O((function(t){return Math.max(t-1,1)}));return}}};window.addEventListener("keydown",t);return function(){window.removeEventListener("keydown",t)}}),[Z,m,k]);if(!c){return null}return(0,n.tZ)("form",{css:wa.wrapper,onSubmit:e.handleSubmit(function(){var t=Ou(_u().mark((function t(e){var n,i,a,u,l;return _u().wrap((function t(c){while(1)switch(c.prev=c.next){case 0:n=o.current;i=n===null||n===void 0?void 0:n.getContext("2d");if(!(!n||!i)){c.next=4;break}return c.abrupt("return");case 4:a={prompt:e.prompt,image:$i(n)};c.next=7;return r.mutateAsync(a);case 7:u=c.sent;if(u){l=new Image;l.onload=function(){n.width=Iu;n.height=Lu;i.drawImage(l,0,0,n.width,n.height);i.lineWidth=h;i.lineJoin="round";i.lineCap="round"};l.src=u}case 9:case"end":return c.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},(0,n.tZ)("div",{css:wa.left},(0,n.tZ)("div",{css:Fu.leftWrapper},(0,n.tZ)("div",{css:Fu.actionBar},(0,n.tZ)("div",{css:Fu.backButtonWrapper},(0,n.tZ)("button",{type:"button",css:Fu.backButton,onClick:function t(){return l("generation")}},(0,n.tZ)(d.Z,{name:"arrowLeft"})),(0,a.__)("Magic Fill","tutor")),(0,n.tZ)("div",{css:Fu.actions},(0,n.tZ)(qt,{variant:"ghost",disabled:m.length===0,onClick:function t(){k(1,m);w(m.slice(0,1));O(1)}},(0,a.__)("Revert to Original","tutor")),(0,n.tZ)(Ga,{variant:"vertical",css:Tu}),(0,n.tZ)("div",{css:Fu.undoRedo},(0,n.tZ)(qt,{variant:"ghost",size:"icon",disabled:Z<=1,onClick:function t(){k(Z-1,m);O((function(t){return Math.max(t-1,1)}))}},(0,n.tZ)(d.Z,{name:"undo",width:20,height:20})),(0,n.tZ)(qt,{variant:"ghost",size:"icon",disabled:Z===m.length,onClick:function t(){k(Z+1,m);O((function(t){return Math.min(t+1,m.length)}))}},(0,n.tZ)(d.Z,{name:"redo",width:20,height:20}))))),(0,n.tZ)("div",{css:Fu.canvasAndLoading},(0,n.tZ)(hu,{ref:o,width:Iu,height:Lu,src:c,brushSize:h,trackStack:m,pointer:Z,setTrackStack:w,setPointer:O}),(0,n.tZ)(S.Z,{when:r.isPending},(0,n.tZ)("div",{css:Fu.loading}))),(0,n.tZ)("div",{css:Fu.footerActions},(0,n.tZ)("div",{css:Fu.footerActionsLeft},(0,n.tZ)(qt,{variant:"secondary",onClick:function t(){var e="".concat((0,y.x0)(),".png");var r=Vi(o),n=r.canvas;if(!n)return;qi($i(n),e)}},(0,n.tZ)(d.Z,{name:"download",width:24,height:24})))))),(0,n.tZ)("div",{css:wa.right},(0,n.tZ)("div",{css:Fu.fields},(0,n.tZ)(p.Qr,{control:e.control,name:"brush_size",render:function t(e){return(0,n.tZ)(ru,xu({},e,{label:"Brush Size",min:1,max:100,isMagicAi:true,hasBorder:true}))}}),(0,n.tZ)(p.Qr,{control:e.control,name:"prompt",render:function t(e){return(0,n.tZ)(ne,xu({},e,{label:(0,a.__)("Describe the Fill","tutor"),placeholder:(0,a.__)("Write 5 words to describe...","tutor"),rows:4,isMagicAi:true}))}})),(0,n.tZ)("div",{css:[wa.rightFooter,Du,true?"":0,true?"":0]},(0,n.tZ)("div",{css:Fu.footerButtons},(0,n.tZ)(qt,{type:"submit",disabled:r.isPending||!e.watch("prompt")},(0,n.tZ)(d.Z,{name:"magicWand",width:24,height:24}),(0,a.__)("Generative Erase","tutor")),(0,n.tZ)(qt,{variant:"primary_outline",disabled:r.isPending,loading:v.isPending,onClick:Ou(_u().mark((function t(){var e,r,n;return _u().wrap((function t(i){while(1)switch(i.prev=i.next){case 0:e=Vi(o),r=e.canvas;if(r){i.next=3;break}return i.abrupt("return");case 3:i.next=5;return v.mutateAsync({image:$i(r)});case 5:n=i.sent;if(n.data){s.onChange(n.data);f()}case 7:case"end":return i.stop()}}),t)})))},(0,a.__)("Use Image","tutor"))))))};const Mu=Ju;var Nu={loading:(0,n.F4)(gu||(gu=wu(["\n    0% {\n      opacity: 0;\n    }\n    50% {\n      opacity: 0.6;\n    }\n    100% {\n      opacity: 0;\n    }\n  "]))),walker:(0,n.F4)(mu||(mu=wu(["\n    0% {\n      left: 0%;\n    }\n    100% {\n      left: 100%;\n    }\n  "])))};var Fu={canvasAndLoading:(0,n.iv)("position:relative;z-index:",h.W5.positive,";"+(true?"":0),true?"":0),loading:(0,n.iv)("position:absolute;top:0;left:0;width:100%;height:100%;background:",h.Jv.ai.gradient_1,";opacity:0.6;transition:0.5s ease opacity;animation:",Nu.loading," 1s linear infinite;z-index:0;&::before{content:'';position:absolute;top:0;left:0;width:200px;height:100%;background:linear-gradient(\n        270deg,\n        rgba(255, 255, 255, 0) 0%,\n        rgba(255, 255, 255, 0.6) 51.13%,\n        rgba(255, 255, 255, 0) 100%\n      );animation:",Nu.walker," 1s linear infinite;}"+(true?"":0),true?"":0),actionBar:true?{name:"bcffy2",styles:"display:flex;align-items:center;justify-content:space-between"}:0,fields:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[12],";"+(true?"":0),true?"":0),leftWrapper:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[8],";padding-block:",h.W0[16],";"+(true?"":0),true?"":0),footerButtons:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[8],";"+(true?"":0),true?"":0),footerActions:true?{name:"1eoy87d",styles:"display:flex;justify-content:space-between"}:0,footerActionsLeft:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[12],";"+(true?"":0),true?"":0),actions:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[16],";"+(true?"":0),true?"":0),undoRedo:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[12],";"+(true?"":0),true?"":0),backButtonWrapper:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[8],";",b.c.body("medium"),";color:",h.Jv.text.title,";"+(true?"":0),true?"":0),backButton:(0,n.iv)(At.i.resetButton,";width:24px;height:24px;border-radius:",h.E0[4],";border:1px solid ",h.Jv.stroke["default"],";display:flex;align-items:center;justify-content:center;"+(true?"":0),true?"":0),image:true?{name:"gb1um3",styles:"width:492px;height:498px;position:relative;img{position:absolute;top:0;left:0;width:100%;height:100%;object-fit:cover;}"}:0,canvasWrapper:true?{name:"bjn8wh",styles:"position:relative"}:0,customCursor:function t(e){return(0,n.iv)("position:absolute;width:",e,"px;height:",e,"px;border-radius:",h.E0.circle,";background:linear-gradient(\n      73.09deg,\n      rgba(255, 150, 69, 0.4) 18.05%,\n      rgba(255, 100, 113, 0.4) 30.25%,\n      rgba(207, 110, 189, 0.4) 55.42%,\n      rgba(164, 119, 209, 0.4) 71.66%,\n      rgba(62, 100, 222, 0.4) 97.9%\n    );border:3px solid ",h.Jv.stroke.white,";pointer-events:none;transform:translate(-50%, -50%);z-index:",h.W5.highest,";display:none;"+(true?"":0),true?"":0)}};function Bu(){var t=Ei(),e=t.state;switch(e){case"generation":return(0,n.tZ)(za,null);case"magic-fill":return(0,n.tZ)(Mu,null);default:return null}}var zu=function t(e){var r=e.title,o=e.icon,i=e.closeModal,a=e.field,u=e.fieldState;return(0,n.tZ)(Ir.Z,{onClose:i,title:r,icon:o,maxWidth:1e3},(0,n.tZ)(Ci,{field:a,fieldState:u,onCloseModal:i},(0,n.tZ)(Bu,null)))};const Ru=zu;function Uu(t){"@babel/helpers - typeof";return Uu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Uu(t)}function Gu(t){return qu(t)||Yu(t)||$u(t)||Qu()}function Qu(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Yu(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function qu(t){if(Array.isArray(t))return Ku(t)}function Hu(t,e){return tl(t)||Xu(t,e)||$u(t,e)||Vu()}function Vu(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function $u(t,e){if(!t)return;if(typeof t==="string")return Ku(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ku(t,e)}function Ku(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Xu(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function tl(t){if(Array.isArray(t))return t}function el(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function rl(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?el(Object(r),!0).forEach((function(e){nl(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):el(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function nl(t,e,r){e=ol(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function ol(t){var e=il(t,"string");return Uu(e)==="symbol"?e:String(e)}function il(t,e){if(Uu(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Uu(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var al=function t(e){var r=e.options,n=r===void 0?{}:r,o=e.onChange,i=e.initialFiles;var l=(0,Zt.p)(),c=l.showToast;var s=(0,u.useMemo)((function(){return i?Array.isArray(i)?i:[i]:[]}),[i]);var d=(0,u.useMemo)((function(){return rl(rl(rl({},n),n.type?{library:{type:n.type}}:{}),{},{multiple:n.multiple?n.multiple===true?"add":n.multiple:false})}),[n]);var f=(0,u.useState)(s),p=Hu(f,2),v=p[0],h=p[1];(0,u.useEffect)((function(){if(s&&!v.length){h(s)}}),[v,s]);var b=(0,u.useCallback)((function(){var t;if(!((t=window.wp)!==null&&t!==void 0&&t.media)){console.error("WordPress media library is not available");return}var e=window.wp.media(d);e.on("close",(function(){if(e.$el){e.$el.parent().parent().remove()}}));e.on("open",(function(){var t=e.state().get("selection");e.$el.attr("data-focus-trap","true");t.reset();v.forEach((function(e){var r=window.wp.media.attachment(e.id);if(r){r.fetch();t.add(r)}}))}));e.on("select",(function(){var t=e.state().get("selection").toJSON();var r=new Set(t.map((function(t){return t.id})));var n=v.filter((function(t){return r.has(t.id)}));var i=t.reduce((function(t,e){if(n.some((function(t){return t.id===e.id}))){return t}if(d.maxFileSize&&e.filesizeInBytes>d.maxFileSize){c({message:(0,a.sprintf)((0,a.__)("%s size exceeds the maximum allowed size","tutor"),e.title),type:"danger"});return t}var r={id:e.id,title:e.title,url:e.url,name:e.title,size:e.filesizeHumanReadable,size_bytes:e.filesizeInBytes,ext:e.filename.split(".").pop()||""};t.push(r);return t}),[]);var u=d.multiple?[].concat(Gu(n),Gu(i)):i.slice(0,1);if(d.maxFiles&&u.length>d.maxFiles){c({message:(0,a.sprintf)((0,a.__)("Cannot select more than %d files","tutor"),d.maxFiles),type:"warning"});return}h(u);o===null||o===void 0?void 0:o(d.multiple?u:u[0]||null);e.close()}));e.open()}),[d,o,v,c]);var y=(0,u.useCallback)((function(){h([]);o===null||o===void 0?void 0:o(d.multiple?[]:null)}),[d.multiple,o]);return{openMediaLibrary:b,existingFiles:v,resetFiles:y}};const ul=al;const ll=r.p+"images/e67e28356e87045281d41cd6583f5c41-generate-image-2x.webp";const cl=r.p+"images/9c13bda85170ee68f15380378d920fd1-generate-image.webp";var sl;var dl=!!k.y.tutor_pro_url;var fl=(sl=k.y.settings)===null||sl===void 0?void 0:sl.chatgpt_key_exist;var pl=function t(e){var r=e.field,o=e.fieldState,i=e.label,u=e.size,l=e.helpText,c=e.buttonText,s=c===void 0?(0,a.__)("Upload Media","tutor"):c,f=e.infoText,p=e.onChange,v=e.generateWithAi,h=v===void 0?false:v,b=e.previewImageCss,y=e.loading,g=e.onClickAiButton;var m=(0,an.d)(),w=m.showModal;var x=ul({options:{type:"image",multiple:false},onChange:function t(e){if(e&&!Array.isArray(e)){var n=e.id,o=e.url,i=e.title;r.onChange({id:n,url:o,title:i});if(p){p({id:n,url:o,title:i})}}},initialFiles:r.value}),_=x.openMediaLibrary,Z=x.resetFiles;var O=r.value;var S=function t(){_()};var k=function t(){Z();r.onChange(null);if(p){p(null)}};var j=function t(){if(!dl){w({component:sn,props:{image:cl,image2x:ll}})}else if(!fl){w({component:Xn,props:{image:cl,image2x:ll}})}else{w({component:Ru,isMagicAi:true,props:{title:(0,a.__)("AI Studio","tutor"),icon:(0,n.tZ)(d.Z,{name:"magicAiColorize",width:24,height:24}),field:r,fieldState:o}});g===null||g===void 0?void 0:g()}};return(0,n.tZ)(Lt,{label:i,field:r,fieldState:o,helpText:l,onClickAiButton:j,generateWithAi:h},(function(){return(0,n.tZ)("div",null,(0,n.tZ)(bi,{size:u,value:O,uploadHandler:S,clearHandler:k,buttonText:s,infoText:f,previewImageCss:b,loading:y}))}))};const vl=N(pl);var hl=["css"];function bl(){bl=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return bl.apply(this,arguments)}function yl(t,e){if(t==null)return{};var r=gl(t,e);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(o=0;o<i.length;o++){n=i[o];if(e.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(t,n))continue;r[n]=t[n]}}return r}function gl(t,e){if(t==null)return{};var r={};var n=Object.keys(t);var o,i;for(i=0;i<n.length;i++){o=n[i];if(e.indexOf(o)>=0)continue;r[o]=t[o]}return r}var ml=function t(e){var r=e.label,o=e.content,i=e.contentPosition,a=i===void 0?"left":i,l=e.showVerticalBar,c=l===void 0?true:l,s=e.size,d=s===void 0?"regular":s,f=e.type,p=f===void 0?"text":f,v=e.field,h=e.fieldState,b=e.disabled,y=e.readOnly,g=e.loading,m=e.placeholder,w=e.helpText,x=e.onChange,_=e.onKeyDown,Z=e.isHidden,O=e.wrapperCss,S=e.contentCss,k=e.removeBorder,j=k===void 0?false:k,E=e.selectOnFocus,C=E===void 0?false:E;var A=(0,u.useRef)(null);return(0,n.tZ)(Lt,{label:r,field:v,fieldState:h,disabled:b,readOnly:y,loading:g,placeholder:m,helpText:w,isHidden:Z,removeBorder:j},(function(t){var e;var r=t.css,i=yl(t,hl);return(0,n.tZ)("div",{css:[xl.inputWrapper(!!h.error,j),O,true?"":0,true?"":0]},a==="left"&&(0,n.tZ)("div",{css:[xl.inputLeftContent(c,d),S,true?"":0,true?"":0]},o),(0,n.tZ)("input",bl({},v,i,{type:"text",value:(e=v.value)!==null&&e!==void 0?e:"",onChange:function t(e){var r=p==="number"?e.target.value.replace(/[^0-9.]/g,"").replace(/(\..*)\./g,"$1"):e.target.value;v.onChange(r);if(x){x(r)}},onKeyDown:function t(e){return _===null||_===void 0?void 0:_(e.key)},css:[r,xl.input(a,c,d),true?"":0,true?"":0],autoComplete:"off",ref:function t(e){v.ref(e);A.current=e},onFocus:function t(){if(!C||!A.current){return}A.current.select()},"data-input":true})),a==="right"&&(0,n.tZ)("div",{css:[xl.inputRightContent(c,d),S,true?"":0,true?"":0]},o))}))};const wl=N(ml);var xl={inputWrapper:function t(e,r){return(0,n.iv)("display:flex;align-items:center;",!r&&(0,n.iv)("border:1px solid ",h.Jv.stroke["default"],";border-radius:",h.E0[6],";box-shadow:",h.AF.input,";background-color:",h.Jv.background.white,";"+(true?"":0),true?"":0)," ",e&&(0,n.iv)("border-color:",h.Jv.stroke.danger,";background-color:",h.Jv.background.status.errorFail,";"+(true?"":0),true?"":0),";&:focus-within{",At.i.inputFocus,";",e&&(0,n.iv)("border-color:",h.Jv.stroke.danger,";"+(true?"":0),true?"":0),";}"+(true?"":0),true?"":0)},input:function t(e,r,o){return(0,n.iv)("&[data-input]{",b.c.body(),";border:none;box-shadow:none;background-color:transparent;padding-",e,":0;",r&&(0,n.iv)("padding-",e,":",h.W0[10],";"+(true?"":0),true?"":0),";",o==="large"&&(0,n.iv)("font-size:",h.JB[24],";font-weight:",h.Ue.medium,";height:34px;",r&&(0,n.iv)("padding-",e,":",h.W0[12],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)," &:focus{box-shadow:none;outline:none;}}"+(true?"":0),true?"":0)},inputLeftContent:function t(e,r){return(0,n.iv)(b.c.small()," ",At.i.flexCenter()," height:40px;min-width:48px;color:",h.Jv.icon.subdued,";padding-inline:",h.W0[12],";",r==="large"&&(0,n.iv)(b.c.body(),";"+(true?"":0),true?"":0)," ",e&&(0,n.iv)("border-right:1px solid ",h.Jv.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},inputRightContent:function t(e,r){return(0,n.iv)(b.c.small()," ",At.i.flexCenter()," height:40px;min-width:48px;color:",h.Jv.icon.subdued,";padding-inline:",h.W0[12],";",r==="large"&&(0,n.iv)(b.c.body(),";"+(true?"":0),true?"":0)," ",e&&(0,n.iv)("border-left:1px solid ",h.Jv.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}};var _l=r(5298);var Zl=r(5253);var Ol=r(7274);var Sl=r(1533);var kl=r(2352);var jl=r(4101);var El=r(7363);function Cl(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Al=function t(e){var r=e.children,o=e.onClose,i=e.title,a=e.subtitle,l=e.icon,c=e.headerChildren,s=e.entireHeader,f=e.actions,p=e.maxWidth,v=p===void 0?1218:p;(0,u.useEffect)((function(){document.body.style.overflow="hidden";return function(){document.body.style.overflow="initial"}}),[]);return(0,n.tZ)(jl.Z,null,(0,n.tZ)("div",{css:Wl.container({maxWidth:v})},(0,n.tZ)("div",{css:Wl.header({hasHeaderChildren:!!c})},(0,n.tZ)(S.Z,{when:s,fallback:(0,n.tZ)(El.Fragment,null,(0,n.tZ)("div",{css:Wl.headerContent},(0,n.tZ)("div",{css:Wl.iconWithTitle},(0,n.tZ)(S.Z,{when:l},l),(0,n.tZ)(S.Z,{when:i},(0,n.tZ)("h6",{css:Wl.title,title:typeof i==="string"?i:""},i))),(0,n.tZ)(S.Z,{when:a},(0,n.tZ)("span",{css:Wl.subtitle},a))),(0,n.tZ)("div",{css:Wl.headerChildren},(0,n.tZ)(S.Z,{when:c},c)),(0,n.tZ)("div",{css:Wl.actionsWrapper},(0,n.tZ)(S.Z,{when:f,fallback:(0,n.tZ)("button",{type:"button",css:Wl.closeButton,onClick:o},(0,n.tZ)(d.Z,{name:"times",width:14,height:14}))},f)))},s)),(0,n.tZ)("div",{css:Wl.content},(0,n.tZ)(kl.Z,null,r))))};const Pl=Al;var Wl={container:function t(e){var r=e.maxWidth;return(0,n.iv)("position:relative;background:",h.Jv.background.white,";margin:",O.oC.MARGIN_TOP,"px auto ",h.W0[24],";height:100%;max-width:",r,"px;box-shadow:",h.AF.modal,";border-radius:",h.E0[10],";overflow:hidden;bottom:0;z-index:",h.W5.modal,";width:100%;",h.Uo.smallTablet,"{width:90%;}"+(true?"":0),true?"":0)},header:function t(e){var r=e.hasHeaderChildren;return(0,n.iv)("display:grid;grid-template-columns:",r?"1fr auto 1fr":"1fr auto auto",";gap:",h.W0[8],";align-items:center;width:100%;height:",O.oC.HEADER_HEIGHT,"px;background:",h.Jv.background.white,";border-bottom:1px solid ",h.Jv.stroke.divider,";position:sticky;"+(true?"":0),true?"":0)},headerContent:(0,n.iv)("place-self:center start;display:inline-flex;align-items:center;gap:",h.W0[12],";padding-left:",h.W0[24],";",h.Uo.smallMobile,"{padding-left:",h.W0[16],";}"+(true?"":0),true?"":0),headerChildren:true?{name:"qdgqcx",styles:"place-self:center center"}:0,iconWithTitle:(0,n.iv)("display:inline-flex;align-items:center;gap:",h.W0[4],";flex-shrink:0;color:",h.Jv.icon["default"],";"+(true?"":0),true?"":0),title:(0,n.iv)(b.c.heading6("medium"),";color:",h.Jv.text.title,";text-transform:none;letter-spacing:normal;"+(true?"":0),true?"":0),subtitle:(0,n.iv)(At.i.text.ellipsis(1)," ",b.c.caption(),";color:",h.Jv.text.hints,";padding-left:",h.W0[12],";border-left:1px solid ",h.Jv.icon.hints,";"+(true?"":0),true?"":0),actionsWrapper:(0,n.iv)("place-self:center end;display:inline-flex;gap:",h.W0[16],";padding-right:",h.W0[24],";",h.Uo.smallMobile,"{padding-right:",h.W0[16],";}"+(true?"":0),true?"":0),closeButton:(0,n.iv)(At.i.resetButton,";display:inline-flex;align-items:center;justify-content:center;width:32px;height:32px;border-radius:",h.E0.circle,";background:",h.Jv.background.white,";&:focus,&:active,&:hover{background:",h.Jv.background.white,";}svg{color:",h.Jv.icon["default"],";transition:color 0.3s ease-in-out;}:hover{svg{color:",h.Jv.icon.hover,";}}:focus{box-shadow:",h.AF.focus,";}"+(true?"":0),true?"":0),content:(0,n.iv)("height:calc(100% - ",O.oC.HEADER_HEIGHT+O.oC.MARGIN_TOP,"px);background-color:",h.Jv.surface.courseBuilder,";overflow-x:hidden;",At.i.overflowYAuto,";"+(true?"":0),true?"":0)};const Il=r.p+"images/8883d834437ecd54063a38ba8ec0ef37-subscriptions-empty-state-2x.webp";const Ll=r.p+"images/026952ce6dfdf3da34dc55d99f241520-subscriptions-empty-state.webp";function Dl(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Tl=function t(e){var r=e.onCreateSubscription;return(0,n.tZ)("div",{css:Jl.wrapper},(0,n.tZ)("div",{css:Jl.banner},(0,n.tZ)("img",{src:Ll,srcSet:"".concat(Ll," ").concat(Il," 2x"),alt:(0,a.__)("Empty state banner","tutor")})),(0,n.tZ)("div",{css:Jl.content},(0,n.tZ)("h5",null,(0,a.__)("Boost Revenue with Subscriptions","tutor")),(0,n.tZ)("p",null,(0,a.__)("Offer flexible subscription plans to maximize your earnings and provide students with affordable access to your courses.","tutor"))),(0,n.tZ)("div",{css:Jl.action},(0,n.tZ)(v.Z,{variant:"secondary",icon:(0,n.tZ)(d.Z,{name:"plusSquareBrand",width:24,height:24}),onClick:r},(0,a.__)("Add Subscription","tutor"))))};var Jl={wrapper:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[32],";justify-content:center;max-width:640px;width:100%;padding-block:",h.W0[40],";margin-inline:auto;"+(true?"":0),true?"":0),content:(0,n.iv)("display:grid;gap:",h.W0[12],";text-align:center;max-width:566px;width:100%;margin:0 auto;h5{",b.c.heading5("medium"),";color:",h.Jv.text.primary,";}p{",b.c.caption(),";color:",h.Jv.text.hints,";}"+(true?"":0),true?"":0),action:true?{name:"zl1inp",styles:"display:flex;justify-content:center"}:0,banner:(0,n.iv)("width:100%;height:232px;background-color:",h.Jv.background.status.drip,";display:flex;align-items:center;justify-content:center;border-radius:",h.E0[8],";position:relative;overflow:hidden;img{position:absolute;top:0;left:0;width:100%;height:100%;object-fit:cover;}"+(true?"":0),true?"":0)};var Ml=r(3618);var Nl=r(6972);function Fl(t){"@babel/helpers - typeof";return Fl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Fl(t)}function Bl(t,e,r){e=zl(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function zl(t){var e=Rl(t,"string");return Fl(e)==="symbol"?e:String(e)}function Rl(t,e){if(Fl(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Fl(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Ul=function t(e){var r,o,i,u,l,c;var s=e.arrow,d=e.triggerRef,f=e.isOpen,p=e.title,h=e.message,b=e.onConfirmation,y=e.onCancel,g=e.isLoading,m=g===void 0?false:g,w=e.gap,x=e.maxWidth,_=e.closePopover,Z=e.animationType,S=Z===void 0?or.ru.slideLeft:Z,k=e.hideArrow,j=k===void 0?false:k,E=e.confirmButton,C=e.cancelButton,A=e.positionModifier;var P=(0,wt.l)({triggerRef:d,isOpen:f,arrow:s,gap:w,positionModifier:A}),W=P.position,I=P.triggerWidth,L=P.popoverRef;return(0,n.tZ)(wt.h,{isOpen:f,onClickOutside:_,animationType:S},(0,n.tZ)("div",{css:[Ql.wrapper(s?W.arrowPlacement:undefined,j),(r={},Bl(r,O.dZ?"right":"left",W.left),Bl(r,"top",W.top),Bl(r,"maxWidth",x!==null&&x!==void 0?x:I),r),true?"":0,true?"":0],ref:L},(0,n.tZ)("div",{css:Ql.content},(0,n.tZ)("div",{css:Ql.body},(0,n.tZ)("div",{css:Ql.title},p),(0,n.tZ)("p",{css:Ql.description},h)),(0,n.tZ)("div",{css:Ql.footer({isDelete:(o=E===null||E===void 0?void 0:E.isDelete)!==null&&o!==void 0?o:false})},(0,n.tZ)(v.Z,{variant:(i=C===null||C===void 0?void 0:C.variant)!==null&&i!==void 0?i:"text",size:"small",onClick:y!==null&&y!==void 0?y:_},(u=C===null||C===void 0?void 0:C.text)!==null&&u!==void 0?u:(0,a.__)("Cancel","tutor")),(0,n.tZ)(v.Z,{"data-cy":"confirm-button",variant:(l=E===null||E===void 0?void 0:E.variant)!==null&&l!==void 0?l:"text",onClick:function t(){b();_()},loading:m,size:"small"},(c=E===null||E===void 0?void 0:E.text)!==null&&c!==void 0?c:(0,a.__)("Ok","tutor"))))))};const Gl=Ul;var Ql={wrapper:function t(e,r){return(0,n.iv)("position:absolute;width:100%;z-index:",h.W5.dropdown,";&::before{",e&&!r&&(0,n.iv)("content:'';position:absolute;border:",h.W0[8]," solid transparent;",e==="left"&&Ql.arrowLeft," ",e==="right"&&Ql.arrowRight," ",e==="top"&&Ql.arrowTop," ",e==="bottom"&&Ql.arrowBottom,";"+(true?"":0),true?"":0),";}"+(true?"":0),true?"":0)},arrowLeft:(0,n.iv)("border-right-color:",h.Jv.surface.tutor,";top:50%;transform:translateY(-50%);left:-",h.W0[16],";"+(true?"":0),true?"":0),arrowRight:(0,n.iv)("border-left-color:",h.Jv.surface.tutor,";top:50%;transform:translateY(-50%);right:-",h.W0[16],";"+(true?"":0),true?"":0),arrowTop:(0,n.iv)("border-bottom-color:",h.Jv.surface.tutor,";left:50%;transform:translateX(-50%);top:-",h.W0[16],";"+(true?"":0),true?"":0),arrowBottom:(0,n.iv)("border-top-color:",h.Jv.surface.tutor,";left:50%;transform:translateX(-50%);bottom:-",h.W0[16],";"+(true?"":0),true?"":0),content:(0,n.iv)("background-color:",h.Jv.surface.tutor,";box-shadow:",h.AF.popover,";border-radius:",h.E0[6],";::-webkit-scrollbar{background-color:",h.Jv.surface.tutor,";width:10px;}::-webkit-scrollbar-thumb{background-color:",h.Jv.action.secondary["default"],";border-radius:",h.E0[6],";}"+(true?"":0),true?"":0),title:(0,n.iv)(b.c.small("medium"),";color:",h.Jv.text.primary,";"+(true?"":0),true?"":0),description:(0,n.iv)(b.c.small(),";color:",h.Jv.text.subdued,";"+(true?"":0),true?"":0),body:(0,n.iv)("padding:",h.W0[16]," ",h.W0[20]," ",h.W0[12],";",At.i.display.flex("column"),";gap:",h.W0[8],";"+(true?"":0),true?"":0),footer:function t(e){var r=e.isDelete,o=r===void 0?false:r;return(0,n.iv)(At.i.display.flex(),";padding:",h.W0[4]," ",h.W0[16]," ",h.W0[8],";justify-content:end;gap:",h.W0[10],";",o&&(0,n.iv)("button:last-of-type{color:",h.Jv.text.error,";}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}};var Yl=["css"];function ql(){ql=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return ql.apply(this,arguments)}function Hl(t,e){if(t==null)return{};var r=Vl(t,e);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(o=0;o<i.length;o++){n=i[o];if(e.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(t,n))continue;r[n]=t[n]}}return r}function Vl(t,e){if(t==null)return{};var r={};var n=Object.keys(t);var o,i;for(i=0;i<n.length;i++){o=n[i];if(e.indexOf(o)>=0)continue;r[o]=t[o]}return r}var $l=function t(e){var r=e.field,o=e.fieldState,i=e.disabled,a=e.value,u=e.onChange,l=e.label,c=e.description,s=e.isHidden,d=e.labelCss;return(0,n.tZ)(Lt,{field:r,fieldState:o,isHidden:s},(function(t){var e=t.css,o=Hl(t,Yl);return(0,n.tZ)("div",null,(0,n.tZ)(_,ql({},r,o,{inputCss:e,labelCss:d,value:a,disabled:i,checked:r.value,label:l,onChange:function t(){r.onChange(!r.value);if(u){u(!r.value)}}})),c&&(0,n.tZ)("p",{css:Xl.description},c))}))};const Kl=$l;var Xl={description:(0,n.iv)(b.c.small()," color:",h.Jv.text.hints,";padding-left:30px;margin-top:",h.W0[6],";"+(true?"":0),true?"":0)};var tc=r(7363);function ec(t){"@babel/helpers - typeof";return ec="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ec(t)}var rc=["css"];function nc(t,e,r){e=oc(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function oc(t){var e=ic(t,"string");return ec(e)==="symbol"?e:String(e)}function ic(t,e){if(ec(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(ec(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function ac(){ac=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return ac.apply(this,arguments)}function uc(t,e){if(t==null)return{};var r=lc(t,e);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(o=0;o<i.length;o++){n=i[o];if(e.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(t,n))continue;r[n]=t[n]}}return r}function lc(t,e){if(t==null)return{};var r={};var n=Object.keys(t);var o,i;for(i=0;i<n.length;i++){o=n[i];if(e.indexOf(o)>=0)continue;r[o]=t[o]}return r}function cc(t,e){return vc(t)||pc(t,e)||dc(t,e)||sc()}function sc(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function dc(t,e){if(!t)return;if(typeof t==="string")return fc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fc(t,e)}function fc(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function pc(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function vc(t){if(Array.isArray(t))return t}function hc(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var bc=function t(e){var r;var o=e.field,i=e.fieldState,a=e.content,l=e.contentPosition,c=l===void 0?"left":l,s=e.showVerticalBar,f=s===void 0?true:s,p=e.type,v=p===void 0?"text":p,h=e.size,b=h===void 0?"regular":h,y=e.label,g=e.placeholder,m=g===void 0?"":g,w=e.disabled,x=e.readOnly,_=e.loading,Z=e.helpText,k=e.removeOptionsMinWidth,j=k===void 0?true:k,E=e.onChange,C=e.presetOptions,A=C===void 0?[]:C,P=e.selectOnFocus,W=P===void 0?false:P,I=e.wrapperCss,L=e.contentCss,D=e.removeBorder,T=D===void 0?false:D;var J=(r=o.value)!==null&&r!==void 0?r:"";var M=(0,u.useRef)(null);var N=(0,u.useState)(false),F=cc(N,2),B=F[0],z=F[1];var R=(0,wt.l)({isOpen:B,isDropdown:true}),U=R.triggerRef,G=R.triggerWidth,Q=R.position,Y=R.popoverRef;return(0,n.tZ)(Lt,{fieldState:i,field:o,label:y,disabled:w,readOnly:x,loading:_,helpText:Z,removeBorder:T,placeholder:m},(function(t){var e;var r=t.css,u=uc(t,rc);return(0,n.tZ)(tc.Fragment,null,(0,n.tZ)("div",{css:[mc.inputWrapper(!!i.error,T),I,true?"":0,true?"":0],ref:U},a&&c==="left"&&(0,n.tZ)("div",{css:[mc.inputLeftContent(f,b),L,true?"":0,true?"":0]},a),(0,n.tZ)("input",ac({},u,{css:[r,mc.input(c,f,b),true?"":0,true?"":0],onClick:function t(){return z(true)},autoComplete:"off",readOnly:x,ref:function t(e){o.ref(e);M.current=e},onFocus:function t(){if(!W||!M.current){return}M.current.select()},value:J,onChange:function t(e){var r=v==="number"?e.target.value.replace(/[^0-9.]/g,"").replace(/(\..*)\./g,"$1"):e.target.value;o.onChange(r);if(E){E(r)}},"data-input":true})),a&&c==="right"&&(0,n.tZ)("div",{css:mc.inputRightContent(f,b)},a)),(0,n.tZ)(wt.h,{isOpen:B,onClickOutside:function t(){return z(false)},onEscape:function t(){return z(false)}},(0,n.tZ)("div",{css:[mc.optionsWrapper,(e={},nc(e,O.dZ?"right":"left",Q.left),nc(e,"top",Q.top),nc(e,"maxWidth",G),e),true?"":0,true?"":0],ref:Y},(0,n.tZ)("ul",{css:[mc.options(j),true?"":0,true?"":0]},A.map((function(t){return(0,n.tZ)("li",{key:String(t.value),css:mc.optionItem({isSelected:t.value===o.value})},(0,n.tZ)("button",{type:"button",css:mc.label,onClick:function e(){o.onChange(t.value);E===null||E===void 0?void 0:E(t.value);z(false)}},(0,n.tZ)(S.Z,{when:t.icon},(0,n.tZ)(d.Z,{name:t.icon,width:32,height:32})),(0,n.tZ)("span",null,t.label)))}))))))}))};const yc=bc;var gc=true?{name:"16gsvie",styles:"min-width:200px"}:0;var mc={mainWrapper:true?{name:"1d3w5wq",styles:"width:100%"}:0,inputWrapper:function t(e,r){return(0,n.iv)("display:flex;align-items:center;",!r&&(0,n.iv)("border:1px solid ",h.Jv.stroke["default"],";border-radius:",h.E0[6],";box-shadow:",h.AF.input,";background-color:",h.Jv.background.white,";"+(true?"":0),true?"":0)," ",e&&(0,n.iv)("border-color:",h.Jv.stroke.danger,";background-color:",h.Jv.background.status.errorFail,";"+(true?"":0),true?"":0),";&:focus-within{",At.i.inputFocus,";",e&&(0,n.iv)("border-color:",h.Jv.stroke.danger,";"+(true?"":0),true?"":0),";}"+(true?"":0),true?"":0)},input:function t(e,r,o){return(0,n.iv)("&[data-input]{",b.c.body(),";border:none;box-shadow:none;background-color:transparent;padding-",e,":0;",r&&(0,n.iv)("padding-",e,":",h.W0[10],";"+(true?"":0),true?"":0),";",o==="large"&&(0,n.iv)("font-size:",h.JB[24],";font-weight:",h.Ue.medium,";height:34px;",r&&(0,n.iv)("padding-",e,":",h.W0[12],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)," &:focus{box-shadow:none;outline:none;}}"+(true?"":0),true?"":0)},label:(0,n.iv)(At.i.resetButton,";width:100%;height:100%;display:flex;align-items:center;gap:",h.W0[8],";margin:0 ",h.W0[12],";padding:",h.W0[6]," 0;text-align:left;line-height:",h.Nv[24],";word-break:break-all;cursor:pointer;span{flex-shrink:0;}"+(true?"":0),true?"":0),optionsWrapper:true?{name:"1n0kzcr",styles:"position:absolute;width:100%"}:0,options:function t(e){return(0,n.iv)("z-index:",h.W5.dropdown,";background-color:",h.Jv.background.white,";list-style-type:none;box-shadow:",h.AF.popover,";padding:",h.W0[4]," 0;margin:0;max-height:500px;border-radius:",h.E0[6],";",At.i.overflowYAuto,";",!e&&gc,";"+(true?"":0),true?"":0)},optionItem:function t(e){var r=e.isSelected,o=r===void 0?false:r;return(0,n.iv)(b.c.body(),";min-height:36px;height:100%;width:100%;display:flex;align-items:center;transition:background-color 0.3s ease-in-out;cursor:pointer;&:hover{background-color:",h.Jv.background.hover,";}",o&&(0,n.iv)("background-color:",h.Jv.background.active,";position:relative;&::before{content:'';position:absolute;top:0;left:0;width:3px;height:100%;background-color:",h.Jv.action.primary["default"],";border-radius:0 ",h.E0[6]," ",h.E0[6]," 0;}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},inputLeftContent:function t(e,r){return(0,n.iv)(b.c.small()," ",At.i.flexCenter()," height:40px;min-width:48px;color:",h.Jv.icon.subdued,";padding-inline:",h.W0[12],";",r==="large"&&(0,n.iv)(b.c.body(),";"+(true?"":0),true?"":0)," ",e&&(0,n.iv)("border-right:1px solid ",h.Jv.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},inputRightContent:function t(e,r){return(0,n.iv)(b.c.small()," ",At.i.flexCenter()," height:40px;min-width:48px;color:",h.Jv.icon.subdued,";padding-inline:",h.W0[12],";",r==="large"&&(0,n.iv)(b.c.body(),";"+(true?"":0),true?"":0)," ",e&&(0,n.iv)("border-left:1px solid ",h.Jv.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}};var wc=r(546);var xc=r(8640);var _c=r(6877);function Zc(t){"@babel/helpers - typeof";return Zc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zc(t)}var Oc=["css"];function Sc(t,e,r){e=kc(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function kc(t){var e=jc(t,"string");return Zc(e)==="symbol"?e:String(e)}function jc(t,e){if(Zc(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Zc(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Ec(){Ec=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Ec.apply(this,arguments)}function Cc(t,e){if(t==null)return{};var r=Ac(t,e);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(o=0;o<i.length;o++){n=i[o];if(e.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(t,n))continue;r[n]=t[n]}}return r}function Ac(t,e){if(t==null)return{};var r={};var n=Object.keys(t);var o,i;for(i=0;i<n.length;i++){o=n[i];if(e.indexOf(o)>=0)continue;r[o]=t[o]}return r}function Pc(t,e){return Tc(t)||Dc(t,e)||Ic(t,e)||Wc()}function Wc(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Ic(t,e){if(!t)return;if(typeof t==="string")return Lc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Lc(t,e)}function Lc(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Dc(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Tc(t){if(Array.isArray(t))return t}function Jc(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Mc=function t(){if(!wp.date){return}var e=wp.date.format;return{formatMonthDropdown:function t(r){return e("F",r)},formatMonthCaption:function t(r){return e("F",r)},formatCaption:function t(r){return e("F",r)},formatWeekdayName:function t(r){return e("D",r)}}};var Nc=function t(e){if(!e)return undefined;return(0,Cn.Z)(new Date(e))?new Date(e.length===10?e+"T00:00:00":e):undefined};var Fc=function t(e){var r=e.label,o=e.field,i=e.fieldState,a=e.disabled,l=e.disabledBefore,c=e.disabledAfter,s=e.loading,f=e.placeholder,p=e.helpText,h=e.isClearable,b=h===void 0?true:h,y=e.onChange,g=e.dateFormat,m=g===void 0?O.E_.yearMonthDay:g;var w=(0,u.useRef)(null);var x=(0,u.useState)(false),_=Pc(x,2),Z=_[0],S=_[1];var k=Nc(o.value);var j=k?(0,wc.Z)(k,m):"";var E=(0,wt.l)({isOpen:Z,isDropdown:true}),C=E.triggerRef,A=E.position,P=E.popoverRef;var W=function t(){var e;S(false);(e=w.current)===null||e===void 0?void 0:e.focus()};var I=Nc(l);var L=Nc(c);return(0,n.tZ)(Lt,{label:r,field:o,fieldState:i,disabled:a,loading:s,placeholder:f,helpText:p},(function(t){var e,r;var i=t.css,a=Cc(t,Oc);return(0,n.tZ)("div",null,(0,n.tZ)("div",{css:zc.wrapper,ref:C},(0,n.tZ)("input",Ec({},a,{css:[i,zc.input,true?"":0,true?"":0],ref:function t(e){o.ref(e);w.current=e},type:"text",value:j,onClick:function t(e){e.stopPropagation();S((function(t){return!t}))},onKeyDown:function t(e){if(e.key==="Enter"){e.preventDefault();S((function(t){return!t}))}},autoComplete:"off","data-input":true})),(0,n.tZ)(d.Z,{name:"calendarLine",width:30,height:32,style:zc.icon}),b&&o.value&&(0,n.tZ)(v.Z,{variant:"text",buttonCss:zc.clearButton,onClick:function t(){o.onChange("")}},(0,n.tZ)(d.Z,{name:"times",width:12,height:12}))),(0,n.tZ)(wt.h,{isOpen:Z,onClickOutside:W,onEscape:W},(0,n.tZ)("div",{css:[zc.pickerWrapper,(e={},Sc(e,O.dZ?"right":"left",A.left),Sc(e,"top",A.top),e),true?"":0,true?"":0],ref:P},(0,n.tZ)(xc._W,{dir:O.dZ?"rtl":"ltr",animate:true,mode:"single",formatters:Mc(),disabled:[!!I&&{before:I},!!L&&{after:L}],selected:k,onSelect:function t(e){if(e){var r=(0,wc.Z)(e,O.E_.yearMonthDay);o.onChange(r);W();if(y){y(r)}}},showOutsideDays:true,captionLayout:"dropdown",autoFocus:true,defaultMonth:k||new Date,startMonth:I||new Date((new Date).getFullYear()-10,0),endMonth:L||new Date((new Date).getFullYear()+10,11),weekStartsOn:(r=wp.date)===null||r===void 0?void 0:r.getSettings().l10n.startOfWeek}))))}))};const Bc=Fc;var zc={wrapper:true?{name:"1wo2jxd",styles:"position:relative;&:hover,&:focus-within{&>button{opacity:1;}}"}:0,input:(0,n.iv)("&[data-input]{padding-left:",h.W0[40],";}"+(true?"":0),true?"":0),icon:(0,n.iv)("position:absolute;top:50%;left:",h.W0[8],";transform:translateY(-50%);color:",h.Jv.icon["default"],";"+(true?"":0),true?"":0),pickerWrapper:(0,n.iv)(b.c.body("regular"),";position:absolute;background-color:",h.Jv.background.white,";box-shadow:",h.AF.popover,";border-radius:",h.E0[6],";.rdp-root{--rdp-day-height:40px;--rdp-day-width:40px;--rdp-day_button-height:40px;--rdp-day_button-width:40px;--rdp-nav-height:40px;--rdp-today-color:",h.Jv.text.title,";--rdp-caption-font-size:",h.JB[18],";--rdp-accent-color:",h.Jv.action.primary["default"],";--rdp-background-color:",h.Jv.background.hover,";--rdp-accent-color-dark:",h.Jv.action.primary.active,";--rdp-background-color-dark:",h.Jv.action.primary.hover,";--rdp-selected-color:",h.Jv.text.white,";--rdp-day_button-border-radius:",h.E0.circle,";--rdp-outside-opacity:0.5;--rdp-disabled-opacity:0.25;}.rdp-months{margin:",h.W0[16],";}.rdp-month_grid{margin:0px;}.rdp-day{padding:0px;}.rdp-nav{--rdp-accent-color:",h.Jv.text.primary,";button{border-radius:",h.E0.circle,";&:hover,&:focus,&:active{background-color:",h.Jv.background.hover,";color:",h.Jv.text.primary,";}&:focus-visible:not(:disabled){--rdp-accent-color:",h.Jv.text.white,";background-color:",h.Jv.background.brand,";}}}.rdp-dropdown_root{.rdp-caption_label{padding:",h.W0[8],";}}.rdp-today{.rdp-day_button{font-weight:",h.Ue.bold,";}}.rdp-selected{color:var(--rdp-selected-color);background-color:var(--rdp-accent-color);border-radius:",h.E0.circle,";font-weight:",h.Ue.regular,";.rdp-day_button{&:hover,&:focus,&:active{background-color:var(--rdp-accent-color);color:",h.Jv.text.primary,";}&:focus-visible{outline:2px solid var(--rdp-accent-color);outline-offset:2px;}&:not(.rdp-outside){color:var(--rdp-selected-color);}}}.rdp-day_button{&:hover,&:focus,&:active{background-color:var(--rdp-background-color);color:",h.Jv.text.primary,";}&:focus-visible:not([disabled]){color:var(--rdp-selected-color);opacity:1;background-color:var(--rdp-accent-color);}}"+(true?"":0),true?"":0),clearButton:(0,n.iv)("position:absolute;top:50%;right:",h.W0[4],";transform:translateY(-50%);width:32px;height:32px;",At.i.flexCenter(),";opacity:0;transition:background-color 0.3s ease-in-out,opacity 0.3s ease-in-out;border-radius:",h.E0[2],";:hover{background-color:",h.Jv.background.hover,";}"+(true?"":0),true?"":0)};var Rc=r(91);var Uc=r(7662);var Gc=r(7573);function Qc(t){"@babel/helpers - typeof";return Qc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Qc(t)}var Yc=["css"];function qc(t,e,r){e=Hc(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Hc(t){var e=Vc(t,"string");return Qc(e)==="symbol"?e:String(e)}function Vc(t,e){if(Qc(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Qc(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function $c(){$c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return $c.apply(this,arguments)}function Kc(t,e){if(t==null)return{};var r=Xc(t,e);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(o=0;o<i.length;o++){n=i[o];if(e.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(t,n))continue;r[n]=t[n]}}return r}function Xc(t,e){if(t==null)return{};var r={};var n=Object.keys(t);var o,i;for(i=0;i<n.length;i++){o=n[i];if(e.indexOf(o)>=0)continue;r[o]=t[o]}return r}function ts(t,e){return is(t)||os(t,e)||rs(t,e)||es()}function es(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function rs(t,e){if(!t)return;if(typeof t==="string")return ns(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ns(t,e)}function ns(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function os(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function is(t){if(Array.isArray(t))return t}function as(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var us=function t(e){var r=e.label,o=e.field,i=e.fieldState,a=e.interval,l=a===void 0?30:a,c=e.disabled,s=e.loading,f=e.placeholder,p=e.helpText,h=e.isClearable,b=h===void 0?true:h;var y=(0,u.useState)(false),g=ts(y,2),m=g[0],w=g[1];var x=(0,u.useRef)(null);var _=(0,u.useMemo)((function(){var t=(0,Rc.Z)((0,Uc.Z)(new Date,0),0);var e=(0,Rc.Z)((0,Uc.Z)(new Date,23),59);var r=(0,Gc.Z)({start:t,end:e},{step:l});return r.map((function(t){return(0,wc.Z)(t,O.E_.hoursMinutes)}))}),[l]);var Z=(0,wt.l)({isOpen:m,isDropdown:true}),S=Z.triggerRef,k=Z.triggerWidth,j=Z.position,E=Z.popoverRef;var C=be({options:_.map((function(t){return{label:t,value:t}})),isOpen:m,selectedValue:o.value,onSelect:function t(e){o.onChange(e.value);w(false)},onClose:function t(){return w(false)}}),A=C.activeIndex,P=C.setActiveIndex;(0,u.useEffect)((function(){if(m&&A>=0&&x.current){x.current.scrollIntoView({block:"nearest",behavior:"smooth"})}}),[m,A]);return(0,n.tZ)(Lt,{label:r,field:o,fieldState:i,disabled:c,loading:s,placeholder:f,helpText:p},(function(t){var e,r;var i=t.css,a=Kc(t,Yc);return(0,n.tZ)("div",null,(0,n.tZ)("div",{css:cs.wrapper,ref:S},(0,n.tZ)("input",$c({},a,{ref:o.ref,css:[i,cs.input,true?"":0,true?"":0],type:"text",onClick:function t(e){e.stopPropagation();w((function(t){return!t}))},onKeyDown:function t(e){if(e.key==="Enter"){e.preventDefault();w((function(t){return!t}))}if(e.key==="Tab"){w(false)}},value:(e=o.value)!==null&&e!==void 0?e:"",onChange:function t(e){var r=e.target.value;o.onChange(r)},autoComplete:"off","data-input":true})),(0,n.tZ)(d.Z,{name:"clock",width:32,height:32,style:cs.icon}),b&&o.value&&(0,n.tZ)(v.Z,{variant:"text",buttonCss:cs.clearButton,onClick:function t(){return o.onChange("")}},(0,n.tZ)(d.Z,{name:"times",width:12,height:12}))),(0,n.tZ)(wt.h,{isOpen:m,onClickOutside:function t(){return w(false)},onEscape:function t(){return w(false)}},(0,n.tZ)("div",{css:[cs.popover,(r={},qc(r,O.dZ?"right":"left",j.left),qc(r,"top",j.top),qc(r,"maxWidth",k),r),true?"":0,true?"":0],ref:E},(0,n.tZ)("ul",{css:cs.list},_.map((function(t,e){return(0,n.tZ)("li",{key:e,css:cs.listItem,ref:A===e?x:null,"data-active":A===e},(0,n.tZ)("button",{type:"button",css:cs.itemButton,onClick:function e(){o.onChange(t);w(false)},onMouseOver:function t(){return P(e)},onMouseLeave:function t(){e!==A&&P(-1)},onFocus:function t(){return P(e)}},t))}))))))}))};const ls=us;var cs={wrapper:true?{name:"1wo2jxd",styles:"position:relative;&:hover,&:focus-within{&>button{opacity:1;}}"}:0,input:(0,n.iv)("&[data-input]{padding-left:",h.W0[40],";}"+(true?"":0),true?"":0),icon:(0,n.iv)("position:absolute;top:50%;left:",h.W0[8],";transform:translateY(-50%);color:",h.Jv.icon["default"],";"+(true?"":0),true?"":0),popover:(0,n.iv)("position:absolute;width:100%;background-color:",h.Jv.background.white,";box-shadow:",h.AF.popover,";height:380px;overflow-y:auto;border-radius:",h.E0[6],";"+(true?"":0),true?"":0),list:true?{name:"v5al3",styles:"list-style:none;padding:0;margin:0"}:0,listItem:(0,n.iv)("width:100%;height:40px;cursor:pointer;display:flex;align-items:center;transition:background-color 0.3s ease-in-out;&[data-active='true']{background-color:",h.Jv.background.hover,";}:hover{background-color:",h.Jv.background.hover,";}"+(true?"":0),true?"":0),itemButton:(0,n.iv)(At.i.resetButton,";",b.c.body(),";margin:",h.W0[4]," ",h.W0[12],";width:100%;height:100%;&:focus,&:active,&:hover{background:none;color:",h.Jv.text.primary,";}"+(true?"":0),true?"":0),clearButton:(0,n.iv)("position:absolute;top:50%;right:",h.W0[4],";transform:translateY(-50%);width:32px;height:32px;",At.i.flexCenter(),";opacity:0;transition:background-color 0.3s ease-in-out,opacity 0.3s ease-in-out;border-radius:",h.E0[2],";:hover{background-color:",h.Jv.background.hover,";}"+(true?"":0),true?"":0)};function ss(t){"@babel/helpers - typeof";return ss="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ss(t)}function ds(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function fs(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ds(Object(r),!0).forEach((function(e){ps(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ds(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ps(t,e,r){e=vs(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function vs(t){var e=hs(t,"string");return ss(e)==="symbol"?e:String(e)}function hs(t,e){if(ss(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(ss(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function bs(){bs=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return bs.apply(this,arguments)}var ys=k.y.tutor_currency;function gs(t){var e=t.index;var r=(0,p.Gc)();var o=r.watch("subscriptions.".concat(e,".offer_sale_price"));var i=r.watch("subscriptions.".concat(e,".regular_price"));var u=!!r.watch("subscriptions.".concat(e,".schedule_sale_price"));return(0,n.tZ)("div",{css:ms.wrapper},(0,n.tZ)("div",null,(0,n.tZ)(p.Qr,{control:r.control,name:"subscriptions.".concat(e,".offer_sale_price"),render:function t(e){return(0,n.tZ)(jn,bs({},e,{label:(0,a.__)("Offer sale price","tutor")}))}})),(0,n.tZ)(S.Z,{when:o},(0,n.tZ)("div",{css:ms.inputWrapper},(0,n.tZ)(p.Qr,{control:r.control,name:"subscriptions.".concat(e,".sale_price"),rules:fs(fs({},Tn()),{},{validate:function t(e){if(e&&i&&Number(e)>=Number(i)){return(0,a.__)("Sale price should be less than regular price","tutor")}if(e&&i&&Number(e)<=0){return(0,a.__)("Sale price should be greater than 0","tutor")}return undefined}}),render:function t(e){return(0,n.tZ)(wl,bs({},e,{type:"number",label:(0,a.__)("Sale Price","tutor"),content:(ys===null||ys===void 0?void 0:ys.symbol)||"$",selectOnFocus:true,contentCss:At.i.inputCurrencyStyle}))}}),(0,n.tZ)(p.Qr,{control:r.control,name:"subscriptions.".concat(e,".schedule_sale_price"),render:function t(e){return(0,n.tZ)(Kl,bs({},e,{label:(0,a.__)("Schedule the sale price","tutor")}))}}),(0,n.tZ)(S.Z,{when:u},(0,n.tZ)("div",{css:ms.datetimeWrapper},(0,n.tZ)("label",null,(0,a.__)("Sale starts from","tutor")),(0,n.tZ)("div",{css:At.i.dateAndTimeWrapper},(0,n.tZ)(p.Qr,{name:"subscriptions.".concat(e,".sale_price_from_date"),control:r.control,rules:{required:(0,a.__)("Schedule date is required","tutor")},render:function t(e){return(0,n.tZ)(Bc,bs({},e,{isClearable:false,placeholder:"yyyy-mm-dd",disabledBefore:(new Date).toISOString()}))}}),(0,n.tZ)(p.Qr,{name:"subscriptions.".concat(e,".sale_price_from_time"),control:r.control,rules:{required:(0,a.__)("Schedule time is required","tutor")},render:function t(e){return(0,n.tZ)(ls,bs({},e,{interval:60,isClearable:false,placeholder:"hh:mm A"}))}}))),(0,n.tZ)("div",{css:ms.datetimeWrapper},(0,n.tZ)("label",null,(0,a.__)("Sale ends to","tutor")),(0,n.tZ)("div",{css:At.i.dateAndTimeWrapper},(0,n.tZ)(p.Qr,{name:"subscriptions.".concat(e,".sale_price_to_date"),control:r.control,rules:{required:(0,a.__)("Schedule date is required","tutor"),validate:{checkEndDate:function t(n){var o=r.watch("subscriptions.".concat(e,".sale_price_from_date"));var i=n;if(o&&i){return new Date(o)>new Date(i)?(0,a.__)("Sales End date should be greater than start date","tutor"):undefined}return undefined}},deps:["subscriptions.".concat(e,".sale_price_from_date")]},render:function t(o){return(0,n.tZ)(Bc,bs({},o,{isClearable:false,placeholder:"yyyy-mm-dd",disabledBefore:r.watch("subscriptions.".concat(e,".sale_price_from_date"))||undefined}))}}),(0,n.tZ)(p.Qr,{name:"subscriptions.".concat(e,".sale_price_to_time"),control:r.control,rules:{required:(0,a.__)("Schedule time is required","tutor"),validate:{checkEndTime:function t(n){var o=r.watch("subscriptions.".concat(e,".sale_price_from_date"));var i=r.watch("subscriptions.".concat(e,".sale_price_from_time"));var u=r.watch("subscriptions.".concat(e,".sale_price_to_date"));var l=n;if(o&&u&&i&&l){return new Date("".concat(o," ").concat(i))>new Date("".concat(u," ").concat(l))?(0,a.__)("Sales End time should be greater than start time","tutor"):undefined}return undefined}},deps:["subscriptions.".concat(e,".sale_price_from_date"),"subscriptions.".concat(e,".sale_price_from_time"),"subscriptions.".concat(e,".sale_price_to_date")]},render:function t(e){return(0,n.tZ)(ls,bs({},e,{interval:60,isClearable:false,placeholder:"hh:mm A"}))}})))))))}var ms={wrapper:(0,n.iv)("background-color:",h.Jv.background.white,";padding:",h.W0[12],";border:1px solid ",h.Jv.stroke["default"],";border-radius:",h.E0[8],";display:flex;flex-direction:column;gap:",h.W0[20],";"+(true?"":0),true?"":0),inputWrapper:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[12],";padding:",h.W0[4],";margin:-",h.W0[4],";"+(true?"":0),true?"":0),datetimeWrapper:(0,n.iv)("label{",b.c.caption(),";color:",h.Jv.text.title,";}"+(true?"":0),true?"":0)};function ws(t){"@babel/helpers - typeof";return ws="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ws(t)}function xs(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function _s(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?xs(Object(r),!0).forEach((function(e){Zs(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):xs(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Zs(t,e,r){e=Os(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Os(t){var e=Ss(t,"string");return ws(e)==="symbol"?e:String(e)}function Ss(t,e){if(ws(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(ws(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var ks={id:"0",payment_type:"recurring",plan_type:"course",assign_id:"0",plan_name:"",recurring_value:"1",recurring_interval:"month",is_featured:false,regular_price:"0",sale_price:"0",sale_price_from_date:"",sale_price_from_time:"",sale_price_to_date:"",sale_price_to_time:"",recurring_limit:(0,a.__)("Until cancelled","tutor"),do_not_provide_certificate:false,enrollment_fee:"0",trial_value:"1",trial_interval:"day",charge_enrollment_fee:false,enable_free_trial:false,offer_sale_price:false,schedule_sale_price:false};var js=function t(e){var r,n,o,i,u,l,c,s,d,f;return{id:e.id,payment_type:(r=e.payment_type)!==null&&r!==void 0?r:"recurring",plan_type:(n=e.plan_type)!==null&&n!==void 0?n:"course",assign_id:e.assign_id,plan_name:(o=e.plan_name)!==null&&o!==void 0?o:"",recurring_value:(i=e.recurring_value)!==null&&i!==void 0?i:"0",recurring_interval:(u=e.recurring_interval)!==null&&u!==void 0?u:"month",is_featured:!!Number(e.is_featured),regular_price:(l=e.regular_price)!==null&&l!==void 0?l:"0",recurring_limit:e.recurring_limit==="0"?(0,a.__)("Until cancelled","tutor"):e.recurring_limit||"",enrollment_fee:(c=e.enrollment_fee)!==null&&c!==void 0?c:"0",trial_value:(s=e.trial_value)!==null&&s!==void 0?s:"0",trial_interval:(d=e.trial_interval)!==null&&d!==void 0?d:"day",sale_price:(f=e.sale_price)!==null&&f!==void 0?f:"0",charge_enrollment_fee:!!Number(e.enrollment_fee),enable_free_trial:!!Number(e.trial_value),offer_sale_price:!!Number(e.sale_price),schedule_sale_price:!!e.sale_price_from,do_not_provide_certificate:!Number(e.provide_certificate),sale_price_from_date:e.sale_price_from?(0,wc.Z)((0,y.nP)(e.sale_price_from),O.E_.yearMonthDay):"",sale_price_from_time:e.sale_price_from?(0,wc.Z)((0,y.nP)(e.sale_price_from),O.E_.hoursMinutes):"",sale_price_to_date:e.sale_price_to?(0,wc.Z)((0,y.nP)(e.sale_price_to),O.E_.yearMonthDay):"",sale_price_to_time:e.sale_price_to?(0,wc.Z)((0,y.nP)(e.sale_price_to),O.E_.hoursMinutes):""}};var Es=function t(e){return _s(_s(_s(_s(_s(_s({},e.id&&String(e.id)!=="0"&&{id:e.id}),{},{payment_type:e.payment_type,plan_type:e.plan_type,assign_id:e.assign_id,plan_name:e.plan_name},e.payment_type==="recurring"&&{recurring_value:e.recurring_value,recurring_interval:e.recurring_interval}),{},{regular_price:e.regular_price,recurring_limit:e.recurring_limit===(0,a.__)("Until cancelled","tutor")?"0":e.recurring_limit,is_featured:e.is_featured?"1":"0"},e.charge_enrollment_fee&&{enrollment_fee:e.enrollment_fee}),e.enable_free_trial&&{trial_value:e.trial_value,trial_interval:e.trial_interval}),{},{sale_price:e.offer_sale_price?e.sale_price:"0"},e.schedule_sale_price&&{sale_price_from:(0,y.WK)(new Date("".concat(e.sale_price_from_date," ").concat(e.sale_price_from_time))),sale_price_to:(0,y.WK)(new Date("".concat(e.sale_price_to_date," ").concat(e.sale_price_to_time)))}),{},{provide_certificate:e.do_not_provide_certificate?"0":"1"})};var Cs=function t(e){return Ot.R.post(St.Z.GET_SUBSCRIPTIONS_LIST,{object_id:e})};var As=function t(e){return(0,xt.a)({queryKey:["SubscriptionsList",e],queryFn:function t(){return Cs(e).then((function(t){return t.data}))}})};var Ps=function t(e,r){return Ot.R.post(St.Z.SAVE_SUBSCRIPTION,_s(_s({object_id:e},r.id&&{id:r.id}),r))};var Ws=function t(e){var r=(0,o.NL)();var n=(0,Zt.p)(),i=n.showToast;return(0,_t.D)({mutationFn:function t(r){return Ps(e,r)},onSuccess:function t(n){if(n.status_code===200||n.status_code===201){i({message:n.message,type:"success"});r.invalidateQueries({queryKey:["SubscriptionsList",e]})}},onError:function t(e){i({type:"danger",message:(0,y.Mo)(e)})}})};var Is=function t(e,r){return Ot.R.post(St.Z.DELETE_SUBSCRIPTION,{object_id:e,id:r})};var Ls=function t(e){var r=(0,o.NL)();var n=(0,Zt.p)(),i=n.showToast;return(0,_t.D)({mutationFn:function t(r){return Is(e,r)},onSuccess:function t(n,o){if(n.status_code===200){i({message:n.message,type:"success"});r.setQueryData(["SubscriptionsList",e],(function(t){return t.filter((function(t){return t.id!==String(o)}))}))}},onError:function t(e){i({type:"danger",message:(0,y.Mo)(e)})}})};var Ds=function t(e,r){return Ot.R.post(St.Z.DUPLICATE_SUBSCRIPTION,{object_id:e,id:r})};var Ts=function t(e){var r=(0,o.NL)();var n=(0,Zt.p)(),i=n.showToast;return(0,_t.D)({mutationFn:function t(r){return Ds(e,r)},onSuccess:function t(n){if(n.data){i({message:n.message,type:"success"});r.invalidateQueries({queryKey:["SubscriptionsList",e]})}},onError:function t(e){i({type:"danger",message:(0,y.Mo)(e)})}})};var Js=function t(e,r){return Ot.R.post(St.Z.SORT_SUBSCRIPTION,{object_id:e,plan_ids:r})};var Ms=function t(e){var r=(0,o.NL)();var n=(0,Zt.p)(),i=n.showToast;return(0,_t.D)({mutationFn:function t(r){return Js(e,r)},onSuccess:function t(n,o){if(n.status_code===200){r.setQueryData(["SubscriptionsList",e],(function(t){var e=o.map((function(t){return String(t)}));return t.sort((function(t,r){return e.indexOf(t.id)-e.indexOf(r.id)}))}));r.invalidateQueries({queryKey:["SubscriptionsList",e]})}},onError:function t(n){i({type:"danger",message:(0,y.Mo)(n)});r.invalidateQueries({queryKey:["SubscriptionsList",e]})}})};var Ns=function t(){return wpAjaxInstance.get(endpoints.GET_MEMBERSHIP_PLANS).then((function(t){return t.data}))};var Fs=function t(){return useQuery({queryKey:["MembershipPlans"],queryFn:Ns})};function Bs(t){"@babel/helpers - typeof";return Bs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Bs(t)}function zs(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Rs(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?zs(Object(r),!0).forEach((function(e){Us(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):zs(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Us(t,e,r){e=Gs(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Gs(t){var e=Qs(t,"string");return Bs(e)==="symbol"?e:String(e)}function Qs(t,e){if(Bs(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Bs(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Ys=function t(e){return(0,Ol.cP)(Rs(Rs({},e),{},{wasDragging:true}))};var qs={droppable:{strategy:_l.uN.Always}};function Hs(t){"@babel/helpers - typeof";return Hs="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Hs(t)}function Vs(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function $s(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Vs(Object(r),!0).forEach((function(e){Ks(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Vs(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ks(t,e,r){e=Xs(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Xs(t){var e=td(t,"string");return Hs(e)==="symbol"?e:String(e)}function td(t,e){if(Hs(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Hs(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function ed(){ed=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return ed.apply(this,arguments)}function rd(t){return id(t)||od(t)||dd(t)||nd()}function nd(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function od(t){if(typeof Symbol!=="undefined"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function id(t){if(Array.isArray(t))return fd(t)}function ad(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ad=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(k([])));y&&y!==e&&r.call(y,i)&&(h=y);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==Hs(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function ud(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function ld(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){ud(i,n,o,a,u,"next",t)}function u(t){ud(i,n,o,a,u,"throw",t)}a(undefined)}))}}function cd(t,e){return vd(t)||pd(t,e)||dd(t,e)||sd()}function sd(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function dd(t,e){if(!t)return;if(typeof t==="string")return fd(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fd(t,e)}function fd(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function pd(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function vd(t){if(Array.isArray(t))return t}function hd(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var bd=100;var yd=k.y.tutor_currency;function gd(t){var e;var r=t.courseId,o=t.id,i=t.toggleCollapse,l=t.bgLight,c=l===void 0?false:l,s=t.isExpanded,f=t.isOverlay,v=f===void 0?false:f;var b=(0,u.useRef)(null);var g=(0,u.useRef)(null);var m=(0,u.useRef)(null);var w=(0,p.Gc)();var x=w.watch("subscriptions");var _=x.findIndex((function(t){return t.id===o}));var O=w.watch("subscriptions.".concat(_));var k=w.formState.isDirty;var E=w.formState.errors.subscriptions?Object.keys(w.formState.errors.subscriptions[_]||{}).length:0;var C=(0,u.useState)(false),A=cd(C,2),P=A[0],W=A[1];var I=(0,u.useState)(false),L=cd(I,2),D=L[0],T=L[1];(0,u.useEffect)((function(){if(s){var t=setTimeout((function(){w.setFocus("subscriptions.".concat(_,".plan_name"))}),bd);if(_>0){var e;(e=b.current)===null||e===void 0?void 0:e.scrollIntoView({behavior:"smooth",block:"start"})}return function(){clearTimeout(t)}}}),[s]);(0,u.useEffect)((function(){var t=function t(e){if((0,j.$K)(b.current)&&!b.current.contains(e.target)){T(false)}};document.addEventListener("click",t);return function(){return document.removeEventListener("click",t)}}),[D]);var J=Ls(r);var M=Ts(r);var N=function(){var t=ld(ad().mark((function t(){var e;return ad().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:r.prev=0;r.next=3;return J.mutateAsync(Number(O.id));case 3:e=r.sent;if(e.data){W(false);if(s){i(O.id)}}r.next=10;break;case 7:r.prev=7;r.t0=r["catch"](0);console.error(r.t0);case 10:case"end":return r.stop()}}),t,null,[[0,7]])})));return function e(){return t.apply(this,arguments)}}();var F=function(){var t=ld(ad().mark((function t(){var e;return ad().wrap((function t(r){while(1)switch(r.prev=r.next){case 0:r.next=2;return M.mutateAsync(Number(O.id));case 2:e=r.sent;if(e.data){i(String(e.data))}case 4:case"end":return r.stop()}}),t)})));return function e(){return t.apply(this,arguments)}}();var B=(0,Ol.nB)({id:O.id||"",animateLayoutChanges:Ys}),z=B.attributes,R=B.listeners,U=B.setNodeRef,G=B.transform,Q=B.transition,Y=B.isDragging;var q=(0,u.useCallback)((function(t){if(t){U(t);b.current=t}}),[U]);var H=w.watch("subscriptions.".concat(_,".plan_name"));var V=w.watch("subscriptions.".concat(_,".charge_enrollment_fee"));var $=w.watch("subscriptions.".concat(_,".is_featured"));var K=w.watch("subscriptions.".concat(_,".offer_sale_price"));var X=!!w.watch("subscriptions.".concat(_,".schedule_sale_price"));var tt=(0,Nl.q_)({height:s?(e=g.current)===null||e===void 0?void 0:e.scrollHeight:0,opacity:s?1:0,overflow:"hidden",config:{duration:300,easing:function t(e){return e*(2-e)}}},[V,$,K,X,k,s,E]),et=cd(tt,2),rt=et[0],nt=et[1];(0,u.useEffect)((function(){if((0,j.$K)(g.current)){var t;nt.start({height:s?(t=g.current)===null||t===void 0?void 0:t.scrollHeight:0,opacity:s?1:0})}}),[V,$,K,X,k,s,E]);var ot=[3,6,9,12];var it=[].concat(rd(ot.map((function(t){return{label:(0,a.sprintf)((0,a.__)("%s times","tutor"),t.toString()),value:String(t)}}))),[{label:(0,a.__)("Until cancelled","tutor"),value:(0,a.__)("Until cancelled","tutor")}]);var at={transform:Ml.ux.Transform.toString(G),transition:Q,opacity:Y?.3:undefined,background:Y?h.Jv.stroke.hover:undefined};return(0,n.tZ)("form",ed({},z,{css:xd.subscription({bgLight:c,isActive:D,isDragging:v,isDeletePopoverOpen:P}),onClick:function t(){return T(true)},style:at,ref:q}),(0,n.tZ)("div",{css:xd.subscriptionHeader(s)},(0,n.tZ)("div",ed({css:xd.grabber({isFormDirty:k})},k?{}:R),(0,n.tZ)(d.Z,{"data-grabber":true,name:"threeDotsVerticalDouble",width:24,height:24}),(0,n.tZ)("button",{type:"button",css:xd.title,disabled:k,title:H,onClick:function t(){return!k&&i(O.id)}},H,(0,n.tZ)(S.Z,{when:O.is_featured},(0,n.tZ)(Pt.Z,{content:(0,a.__)("Featured","tutor"),delay:200},(0,n.tZ)(d.Z,{name:"star",width:24,height:24}))))),(0,n.tZ)("div",{css:xd.actions(s),"data-visually-hidden":true},(0,n.tZ)(S.Z,{when:!s},(0,n.tZ)(Pt.Z,{content:(0,a.__)("Edit","tutor"),delay:200},(0,n.tZ)("button",{"data-cy":"edit-subscription",type:"button",disabled:k,onClick:function t(){return!k&&i(O.id)}},(0,n.tZ)(d.Z,{name:"edit",width:24,height:24})))),(0,n.tZ)(S.Z,{when:O.isSaved},(0,n.tZ)(Pt.Z,{content:(0,a.__)("Duplicate","tutor"),delay:200},(0,n.tZ)("button",{"data-cy":"duplicate-subscription",type:"button",disabled:k,onClick:F},(0,n.tZ)(S.Z,{when:!M.isPending,fallback:(0,n.tZ)(Z.ZP,{size:24})},(0,n.tZ)(d.Z,{name:"copyPaste",width:24,height:24})))),(0,n.tZ)(Pt.Z,{content:(0,a.__)("Delete","tutor"),delay:200},(0,n.tZ)("button",{"data-cy":"delete-subscription",ref:m,type:"button",disabled:k,onClick:function t(){return W(true)}},(0,n.tZ)(d.Z,{name:"delete",width:24,height:24}))),(0,n.tZ)("button",{type:"button",disabled:k,onClick:function t(){return!k&&i(O.id)},"data-collapse-button":true,title:(0,a.__)("Collapse/expand plan","tutor")},(0,n.tZ)(d.Z,{name:"chevronDown",width:24,height:24}))))),(0,n.tZ)(Nl.q.div,{style:$s({},rt),css:xd.itemWrapper(s)},(0,n.tZ)("div",{ref:g,css:At.i.display.flex("column")},(0,n.tZ)("div",{css:xd.subscriptionContent},(0,n.tZ)(p.Qr,{control:w.control,name:"subscriptions.".concat(_,".plan_name"),rules:Tn(),render:function t(e){return(0,n.tZ)(_o,ed({},e,{placeholder:(0,a.__)("Enter plan name","tutor"),label:(0,a.__)("Plan Name","tutor")}))}}),(0,n.tZ)("div",{css:xd.inputGroup},(0,n.tZ)(p.Qr,{control:w.control,name:"subscriptions.".concat(_,".regular_price"),rules:$s($s({},Tn()),{},{validate:function t(e){if(Number(e)<=0){return(0,a.__)("Price must be greater than 0","tutor")}}}),render:function t(e){return(0,n.tZ)(wl,ed({},e,{label:(0,a.__)("Price","tutor"),content:(yd===null||yd===void 0?void 0:yd.symbol)||"$",placeholder:(0,a.__)("Plan price","tutor"),selectOnFocus:true,contentCss:At.i.inputCurrencyStyle,type:"number"}))}}),(0,n.tZ)(p.Qr,{control:w.control,name:"subscriptions.".concat(_,".recurring_value"),rules:$s($s({},Tn()),{},{validate:function t(e){if(Number(e)<1){return(0,a.__)("This value must be equal to or greater than 1","tutor")}}}),render:function t(e){return(0,n.tZ)(_o,ed({},e,{label:(0,a.__)("Billing Interval","tutor"),placeholder:(0,a.__)("12","tutor"),selectOnFocus:true,type:"number"}))}}),(0,n.tZ)(p.Qr,{control:w.control,name:"subscriptions.".concat(_,".recurring_interval"),render:function t(e){return(0,n.tZ)(Te,ed({},e,{label:(0,n.tZ)("div",null," "),options:[{label:(0,a.__)("Day(s)","tutor"),value:"day"},{label:(0,a.__)("Week(s)","tutor"),value:"week"},{label:(0,a.__)("Month(s)","tutor"),value:"month"},{label:(0,a.__)("Year(s)","tutor"),value:"year"}],removeOptionsMinWidth:true}))}}),(0,n.tZ)(p.Qr,{control:w.control,name:"subscriptions.".concat(_,".recurring_limit"),rules:$s($s({},Tn()),{},{validate:function t(e){if(e===(0,a.__)("Until cancelled","tutor")){return true}if(Number(e)<=0){return(0,a.__)("Renew plan must be greater than 0","tutor")}return true}}),render:function t(e){return(0,n.tZ)(yc,ed({},e,{label:(0,a.__)("Billing Cycles","tutor"),placeholder:(0,a.__)("Select or type times to renewing the plan","tutor"),content:e.field.value!==(0,a.__)("Until cancelled","tutor")&&(0,a.__)("Times","tutor"),contentPosition:"right",type:"number",presetOptions:it,selectOnFocus:true}))}})),(0,n.tZ)(p.Qr,{control:w.control,name:"subscriptions.".concat(_,".charge_enrollment_fee"),render:function t(e){return(0,n.tZ)(Kl,ed({},e,{label:(0,a.__)("Charge enrollment fee","tutor")}))}}),(0,n.tZ)(S.Z,{when:V},(0,n.tZ)(p.Qr,{control:w.control,name:"subscriptions.".concat(_,".enrollment_fee"),rules:$s($s({},Tn()),{},{validate:function t(e){if(Number(e)<=0){return(0,a.__)("Enrollment fee must be greater than 0","tutor")}return true}}),render:function t(e){return(0,n.tZ)(wl,ed({},e,{label:(0,a.__)("Enrollment fee","tutor"),content:(yd===null||yd===void 0?void 0:yd.symbol)||"$",placeholder:(0,a.__)("Enter enrollment fee","tutor"),selectOnFocus:true,contentCss:At.i.inputCurrencyStyle,type:"number"}))}})),(0,n.tZ)(p.Qr,{control:w.control,name:"subscriptions.".concat(_,".do_not_provide_certificate"),render:function t(e){return(0,n.tZ)(Kl,ed({},e,{label:(0,a.__)("Do not provide certificate","tutor")}))}}),(0,n.tZ)(p.Qr,{control:w.control,name:"subscriptions.".concat(_,".is_featured"),render:function t(e){return(0,n.tZ)(Kl,ed({},e,{label:(0,a.__)("Mark as featured","tutor")}))}}),(0,n.tZ)(gs,{index:_})))),(0,n.tZ)(Gl,{isOpen:P,triggerRef:m,closePopover:y.ZT,maxWidth:"258px",title:(0,a.sprintf)((0,a.__)('Delete "%s"',"tutor"),O.plan_name),message:(0,a.__)("Are you sure you want to delete this plan? This cannot be undone.","tutor"),animationType:or.ru.slideUp,arrow:"auto",hideArrow:true,isLoading:J.isPending,confirmButton:{text:(0,a.__)("Delete","tutor"),variant:"text",isDelete:true},cancelButton:{text:(0,a.__)("Cancel","tutor"),variant:"text"},onConfirmation:N,onCancel:function t(){return W(false)}}))}var md=true?{name:"21xn5r",styles:"transform:rotate(180deg)"}:0;var wd=true?{name:"21xn5r",styles:"transform:rotate(180deg)"}:0;var xd={grabber:function t(e){var r=e.isFormDirty;return(0,n.iv)("display:flex;align-items:center;gap:",h.W0[4],";",b.c.body(),";color:",h.Jv.text.hints,";width:100%;min-height:40px;[data-grabber]{color:",h.Jv.icon["default"],";cursor:",r?"not-allowed":"grab",";flex-shrink:0;}span{max-width:496px;width:100%;",At.i.textEllipsis,";}"+(true?"":0),true?"":0)},trialWrapper:(0,n.iv)("display:grid;grid-template-columns:1fr 1fr;align-items:start;gap:",h.W0[8],";"+(true?"":0),true?"":0),title:(0,n.iv)(At.i.resetButton,";display:flex;align-items:center;color:",h.Jv.text.hints,";flex-grow:1;gap:",h.W0[8],";:disabled{cursor:default;}svg{color:",h.Jv.icon.brand,";}"+(true?"":0),true?"":0),titleField:(0,n.iv)("width:100%;position:relative;input{padding-right:",h.W0[128],"!important;}"+(true?"":0),true?"":0),titleActions:(0,n.iv)("position:absolute;right:",h.W0[4],";top:50%;transform:translateY(-50%);display:flex;align-items:center;gap:",h.W0[8],";"+(true?"":0),true?"":0),subscription:function t(e){var r=e.bgLight,o=e.isActive,i=e.isDragging,a=e.isDeletePopoverOpen;return(0,n.iv)("width:100%;border:1px solid ",h.Jv.stroke["default"],";border-radius:",h.E0.card,";overflow:hidden;transition:border-color 0.3s ease;[data-visually-hidden]{opacity:",a?1:0,";transition:opacity 0.3s ease;}",r&&(0,n.iv)("background-color:",h.Jv.background.white,";"+(true?"":0),true?"":0)," ",o&&(0,n.iv)("border-color:",h.Jv.stroke.brand,";"+(true?"":0),true?"":0)," ",i&&(0,n.iv)("box-shadow:",h.AF.drag,";[data-grabber]{cursor:grabbing;}"+(true?"":0),true?"":0)," &:hover:not(:disabled){[data-visually-hidden]{opacity:1;}}",h.Uo.smallTablet,"{[data-visually-hidden]{opacity:1;}}"+(true?"":0),true?"":0)},itemWrapper:function t(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;return(0,n.iv)(e&&(0,n.iv)("background-color:",h.Jv.background.hover,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},subscriptionHeader:function t(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;return(0,n.iv)("padding:",h.W0[12]," ",h.W0[16],";display:flex;align-items:center;justify-content:space-between;",e&&(0,n.iv)("background-color:",h.Jv.background.hover,";border-bottom:1px solid ",h.Jv.stroke.border,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},subscriptionContent:(0,n.iv)("padding:",h.W0[16],";display:flex;flex-direction:column;gap:",h.W0[12],";"+(true?"":0),true?"":0),actions:function t(e){return(0,n.iv)("display:flex;align-items:center;gap:",h.W0[4],";button{width:24px;height:24px;",At.i.resetButton,";color:",h.Jv.icon["default"],";display:flex;align-items:center;justify-content:center;transition:color 0.3s ease;:disabled{cursor:not-allowed;color:",h.Jv.icon.disable.background,";}&[data-collapse-button]{transition:transform 0.3s ease;",e&&wd,";svg{width:20px;height:20px;}&:hover:not(:disabled){color:",h.Jv.icon.hover,";}}}"+(true?"":0),true?"":0)},collapse:function t(e){return(0,n.iv)("transition:transform 0.3s ease;svg{width:16px;height:16px;}",e&&md,";"+(true?"":0),true?"":0)},inputGroup:(0,n.iv)("display:grid;grid-template-columns:1fr 0.7fr 1fr 1fr;align-items:start;gap:",h.W0[8],";",h.Uo.smallMobile,"{grid-template-columns:1fr;}"+(true?"":0),true?"":0)};var _d=r(7363);function Zd(t){"@babel/helpers - typeof";return Zd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zd(t)}function Od(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */Od=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(k([])));y&&y!==e&&r.call(y,i)&&(h=y);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==Zd(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function Sd(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function kd(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Sd(i,n,o,a,u,"next",t)}function u(t){Sd(i,n,o,a,u,"throw",t)}a(undefined)}))}}function jd(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Ed(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?jd(Object(r),!0).forEach((function(e){Cd(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):jd(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Cd(t,e,r){e=Ad(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Ad(t){var e=Pd(t,"string");return Zd(e)==="symbol"?e:String(e)}function Pd(t,e){if(Zd(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Zd(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Wd(t,e){return Jd(t)||Td(t,e)||Ld(t,e)||Id()}function Id(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Ld(t,e){if(!t)return;if(typeof t==="string")return Dd(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Dd(t,e)}function Dd(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Td(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Jd(t){if(Array.isArray(t))return t}function Md(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}function Nd(t){var e;var r=t.courseId,l=t.isBundle,c=l===void 0?false:l,s=t.title,f=t.subtitle,h=t.icon,b=t.closeModal,g=t.expandedSubscriptionId,m=t.createEmptySubscriptionOnMount;var w=(0,o.NL)();var x=it({defaultValues:{subscriptions:[]},mode:"onChange"});var _=(0,p.Dq)({control:x.control,name:"subscriptions",keyName:"_id"}),Z=_.append,k=_.remove,E=_.move,C=_.fields;var A=(0,u.useState)(g||""),P=Wd(A,2),W=P[0],I=P[1];var L=(0,u.useState)(null),D=Wd(L,2),T=D[0],J=D[1];var M=!!(0,i.y)({queryKey:["SubscriptionsList",r]});var N=w.getQueryData(["SubscriptionsList",r]);var F=Ms(r);var B=Ws(r);var z=x.formState.isDirty;var R=x.getValues().subscriptions.find((function(t){return t.id===W}));var U=C.findIndex((function(t){return!t.isSaved}))!==-1?C.findIndex((function(t){return!t.isSaved})):(e=x.formState.dirtyFields.subscriptions)===null||e===void 0?void 0:e.findIndex((function(t){return(0,j.$K)(t)}));(0,u.useEffect)((function(){if(!N){return}if(C.length===0){return x.reset({subscriptions:N.map((function(t){return Ed(Ed({},js(t)),{},{isSaved:true})}))})}var t=N.map((function(t){var e=C.find((function(e){return e.id===t.id}));if(e){return Ed(Ed({},e),Ed(Ed({},js(t)),{},{isSaved:true}))}return Ed(Ed({},js(t)),{},{isSaved:true})}));x.reset({subscriptions:t})}),[N,M]);var G=function(){var t=kd(Od().mark((function t(e){var n;return Od().wrap((function t(o){while(1)switch(o.prev=o.next){case 0:o.prev=0;x.trigger();n=setTimeout(kd(Od().mark((function t(){var n,o,i;return Od().wrap((function t(a){while(1)switch(a.prev=a.next){case 0:n=x.formState.errors.subscriptions||[];if(!n.length){a.next=3;break}return a.abrupt("return");case 3:o=Es(Ed(Ed({},e),{},{id:e.isSaved?e.id:"0",assign_id:String(r),plan_type:c?"bundle":"course"}));a.next=6;return B.mutateAsync(o);case 6:i=a.sent;if(i.status_code===200||i.status_code===201){I((function(t){return t===o.id?"":o.id||""}))}case 8:case"end":return a.stop()}}),t)}))),0);return o.abrupt("return",(function(){clearTimeout(n)}));case 6:o.prev=6;o.t0=o["catch"](0);x.reset();case 9:case"end":return o.stop()}}),t,null,[[0,6]])})));return function e(r){return t.apply(this,arguments)}}();var Q=(0,_l.Dy)((0,_l.VT)(_l.we,{activationConstraint:{distance:10}}),(0,_l.VT)(_l.Lg,{coordinateGetter:Ol.is}));(0,u.useEffect)((function(){if(m){var t=(0,y.x0)();Z(Ed(Ed({},ks),{},{id:t,isSaved:false}));I(t)}}),[]);return(0,n.tZ)(p.RV,x,(0,n.tZ)(Pl,{onClose:function t(){return b({action:"CLOSE"})},icon:z?(0,n.tZ)(d.Z,{name:"warning",width:24,height:24}):h,title:z?O.iM.isAboveMobile?(0,a.__)("Unsaved Changes","tutor"):"":s,subtitle:z?s===null||s===void 0?void 0:s.toString():f,maxWidth:1218,actions:z&&(0,n.tZ)(_d.Fragment,null,(0,n.tZ)(v.Z,{variant:"text",size:"small",onClick:function t(){return R?x.reset():b({action:"CLOSE"})}},R!==null&&R!==void 0&&R.isSaved?(0,a.__)("Discard Changes","tutor"):(0,a.__)("Cancel","tutor")),(0,n.tZ)(v.Z,{"data-cy":"save-subscription",loading:B.isPending,variant:"primary",size:"small",onClick:function t(){if(U!==-1&&R){G(R)}}},R!==null&&R!==void 0&&R.isSaved?(0,a.__)("Update","tutor"):(0,a.__)("Save","tutor")))},(0,n.tZ)("div",{css:Fd.wrapper},(0,n.tZ)(S.Z,{when:C.length,fallback:(0,n.tZ)(Tl,{onCreateSubscription:function t(){var e=(0,y.x0)();Z(Ed(Ed({},ks),{},{id:e,isSaved:false}));I(e)}})},(0,n.tZ)("div",{css:Fd.container},(0,n.tZ)("div",{css:Fd.header},(0,n.tZ)("h6",null,(0,a.__)("Subscription Plans","tutor"))),(0,n.tZ)("div",{css:Fd.content},(0,n.tZ)(_l.LB,{sensors:Q,collisionDetection:_l.pE,measuring:qs,modifiers:[Zl.hg],onDragStart:function t(e){J(e.active.id)},onDragEnd:function(){var t=kd(Od().mark((function t(e){var r,n,o,i,a;return Od().wrap((function t(u){while(1)switch(u.prev=u.next){case 0:r=e.active,n=e.over;if(n){u.next=4;break}J(null);return u.abrupt("return");case 4:if(r.id!==n.id){o=C.findIndex((function(t){return t.id===r.id}));i=C.findIndex((function(t){return t.id===n.id}));a=(0,y.Ao)(C,o,i);E(o,i);F.mutateAsync(a.map((function(t){return Number(t.id)})))}J(null);case 6:case"end":return u.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()},(0,n.tZ)(Ol.Fo,{items:C,strategy:Ol.qw},(0,n.tZ)(ue,{each:C},(function(t,e){return(0,n.tZ)(gd,{key:t.id,id:t.id,courseId:r,toggleCollapse:function t(e){I((function(t){return t===e?"":e}))},onDiscard:!t.id?function(){k(e)}:y.ZT,isExpanded:T?false:W===t.id})}))),(0,Sl.createPortal)((0,n.tZ)(_l.y9,null,(0,n.tZ)(S.Z,{when:T},(function(t){return(0,n.tZ)(gd,{id:t,courseId:r,toggleCollapse:y.ZT,bgLight:true,onDiscard:y.ZT,isExpanded:false,isOverlay:true})}))),document.body)),(0,n.tZ)("div",null,(0,n.tZ)(v.Z,{variant:"secondary",icon:(0,n.tZ)(d.Z,{name:"plusSquareBrand",width:24,height:24}),disabled:z,onClick:function t(){var e=(0,y.x0)();Z(Ed(Ed({},ks),{},{id:e,isSaved:false}));I(e)}},(0,a.__)("Add New Plan","tutor")))))))))}var Fd={wrapper:true?{name:"w1atjl",styles:"width:100%;height:100%"}:0,container:(0,n.iv)("max-width:640px;width:100%;padding-block:",h.W0[40],";margin-inline:auto;display:flex;flex-direction:column;gap:",h.W0[32],";",h.Uo.smallMobile,"{padding-block:",h.W0[24],";padding-inline:",h.W0[8],";}"+(true?"":0),true?"":0),header:(0,n.iv)("display:flex;align-items:center;justify-content:space-between;h6{",b.c.heading6("medium"),";color:",h.Jv.text.primary,";text-transform:none;letter-spacing:normal;}"+(true?"":0),true?"":0),content:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[16],";"+(true?"":0),true?"":0)};var Bd=r(7363);function zd(t,e){switch(t){case"hour":return e>1?(0,a.__)("Hours","tutor"):(0,a.__)("Hour","tutor");case"day":return e>1?(0,a.__)("Days","tutor"):(0,a.__)("Day","tutor");case"week":return e>1?(0,a.__)("Weeks","tutor"):(0,a.__)("Week","tutor");case"month":return e>1?(0,a.__)("Months","tutor"):(0,a.__)("Month","tutor");case"year":return e>1?(0,a.__)("Years","tutor"):(0,a.__)("Year","tutor");case"until_cancellation":return(0,a.__)("Until Cancellation","tutor")}}function Rd(t){var e=t.subscription,r=t.courseId,o=t.isBundle;var i=(0,an.d)(),u=i.showModal;return(0,n.tZ)("div",{"data-cy":"subscription-preview-item",css:Ud.wrapper},(0,n.tZ)("div",{css:Ud.item},(0,n.tZ)("p",{css:Ud.title},e.plan_name,(0,n.tZ)(S.Z,{when:e.is_featured},(0,n.tZ)(d.Z,{style:Ud.featuredIcon,name:"star",height:20,width:20}))),(0,n.tZ)("div",{css:Ud.information},(0,n.tZ)(S.Z,{when:e.payment_type==="recurring",fallback:(0,n.tZ)("span",null,(0,a.__)("Lifetime","tutor"))},(0,n.tZ)("span",null,(0,a.sprintf)((0,a.__)("Renew every %s %s","tutor"),e.recurring_value.toString().padStart(2,"0"),zd(e.recurring_interval,Number(e.recurring_value))))),(0,n.tZ)(S.Z,{when:e.payment_type!=="onetime"},(0,n.tZ)(S.Z,{when:e.recurring_limit===(0,a.__)("Until cancelled","tutor"),fallback:(0,n.tZ)(Bd.Fragment,null,(0,n.tZ)("span",null,"•"),(0,n.tZ)("span",null,e.recurring_limit.toString().padStart(2,"0")," ",(0,a.__)("Billing Cycles","tutor")))},(0,n.tZ)("span",null,"•"),(0,n.tZ)("span",null,(0,a.__)("Until Cancellation","tutor")))))),(0,n.tZ)("button",{type:"button",css:Ud.editButton,onClick:function t(){u({component:Nd,props:{title:(0,a.__)("Manage Subscription Plans","tutor"),icon:(0,n.tZ)(d.Z,{name:"dollar-recurring",width:24,height:24}),expandedSubscriptionId:e.id,courseId:r,isBundle:o}})},"data-edit-button":true,"data-cy":"edit-subscription"},(0,n.tZ)(d.Z,{name:"pen",width:19,height:19})))}var Ud={wrapper:(0,n.iv)("display:flex;justify-content:space-between;align-items:center;background-color:",h.Jv.background.white,";padding:",h.W0[8]," ",h.W0[12],";[data-edit-button]{opacity:0;transition:opacity 0.3s ease;}&:hover{background-color:",h.Jv.background.hover,";[data-edit-button]{opacity:1;}}&:not(:last-of-type){border-bottom:1px solid ",h.Jv.stroke["default"],";}"+(true?"":0),true?"":0),item:(0,n.iv)("min-height:48px;display:flex;flex-direction:column;justify-content:center;gap:",h.W0[4],";"+(true?"":0),true?"":0),title:(0,n.iv)(b.c.caption("medium"),";color:",h.Jv.text.primary,";display:flex;align-items:center;"+(true?"":0),true?"":0),information:(0,n.iv)(b.c.small(),";color:",h.Jv.text.hints,";display:flex;align-items:center;flex-wrap:wrap;gap:",h.W0[4],";"+(true?"":0),true?"":0),featuredIcon:(0,n.iv)("color:",h.Jv.icon.brand,";"+(true?"":0),true?"":0),editButton:(0,n.iv)(At.i.resetButton,";",At.i.flexCenter(),";width:24px;height:24px;border-radius:",h.E0[4],";color:",h.Jv.icon["default"],";transition:color 0.3s ease,background 0.3s ease;&:hover{background:",h.Jv.action.secondary["default"],";color:",h.Jv.icon.brand,";}"+(true?"":0),true?"":0)};function Gd(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}function Qd(t){var e=t.courseId,r=t.isBundle,o=r===void 0?false:r;var i=As(e);var u=(0,an.d)(),l=u.showModal;if(i.isLoading){return(0,n.tZ)(Z.g4,null)}if(!i.data){return null}var c=i.data;return(0,n.tZ)("div",{css:Hd.outer},(0,n.tZ)(S.Z,{when:c.length>0},(0,n.tZ)("div",{css:Hd.header},(0,a.__)("Subscriptions","tutor"))),(0,n.tZ)("div",{css:Hd.inner({hasSubscriptions:c.length>0})},(0,n.tZ)(ue,{each:c},(function(t,r){return(0,n.tZ)(Rd,{key:r,subscription:js(t),courseId:e,isBundle:o})})),(0,n.tZ)("div",{css:Hd.emptyState({hasSubscriptions:c.length>0})},(0,n.tZ)(v.Z,{"data-cy":"add-subscription",variant:"secondary",icon:(0,n.tZ)(d.Z,{name:"dollar-recurring",width:24,height:24}),onClick:function t(){l({component:Nd,props:{title:(0,a.__)("Manage Subscription Plans","tutor"),icon:(0,n.tZ)(d.Z,{name:"dollar-recurring",width:24,height:24}),createEmptySubscriptionOnMount:true,courseId:e,isBundle:o}})}},(0,a.__)("Add Subscription","tutor")))))}const Yd=Qd;var qd=true?{name:"1e1ncky",styles:"border:none"}:0;var Hd={outer:(0,n.iv)("width:100%;display:flex;flex-direction:column;gap:",h.W0[8],";"+(true?"":0),true?"":0),inner:function t(e){var r=e.hasSubscriptions;return(0,n.iv)("background:",h.Jv.background.white,";border:1px solid ",h.Jv.stroke["default"],";border-radius:",h.E0.card,";width:100%;overflow:hidden;",!r&&qd,";"+(true?"":0),true?"":0)},header:(0,n.iv)("display:flex;align-items:center;justify-content:space-between;",b.c.body(),";color:",h.Jv.text.title,";"+(true?"":0),true?"":0),emptyState:function t(e){var r=e.hasSubscriptions;return(0,n.iv)("padding:",r?"".concat(h.W0[8]," ").concat(h.W0[12]):0,";width:100%;&>button{width:100%;}"+(true?"":0),true?"":0)}};var Vd=r(6860);var $d=r(7363);function Kd(){Kd=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Kd.apply(this,arguments)}var Xd=(0,Vd.X)();var tf=k.y.tutor_currency;var ef=function t(){var e,r,o;var u=(0,c.Gc)();var l=(0,i.y)({queryKey:["CourseBundle",Xd]});var s=Number(u.getValues("details.subtotal_raw_price")).toFixed(2)||0;var d=(0,c.qo)({control:u.control,name:"course_selling_option"});var f=[{label:(0,a.__)("One-time purchase only","tutor-pro"),value:"one_time"},{label:(0,a.__)("Subscription only","tutor-pro"),value:"subscription"},{label:(0,a.__)("Subscription & one-time purchase","tutor-pro"),value:"both"}];return(0,n.tZ)($d.Fragment,null,(0,n.tZ)(S.Z,{when:(0,y.ro)(O.AO.SUBSCRIPTION)&&((e=k.y.settings)===null||e===void 0?void 0:e.monetize_by)==="tutor"},(0,n.tZ)(c.Qr,{name:"course_selling_option",control:u.control,render:function t(e){return(0,n.tZ)(Te,Kd({},e,{label:(0,a.__)("Purchase Options","tutor-pro"),options:f}))}})),(0,n.tZ)(S.Z,{when:!["subscription"].includes(d)||((r=k.y.settings)===null||r===void 0?void 0:r.monetize_by)==="wc"},(0,n.tZ)("div",{css:nf.coursePriceWrapper},(0,n.tZ)("div",{css:nf.regularPrice},(0,n.tZ)("label",null,(0,a.__)("Regular Price","tutor-pro")),(0,n.tZ)("div",null,(tf===null||tf===void 0?void 0:tf.symbol)||"$"," ",s)),(0,n.tZ)(c.Qr,{name:"details.subtotal_raw_sale_price",control:u.control,rules:{validate:function t(e){if(!e){return true}if(Number(e)>=Number(s)){return(0,a.__)("Sale price must be less than regular price","tutor-pro")}return true}},render:function t(e){return(0,n.tZ)(wl,Kd({},e,{label:(0,a.__)("Sale Price","tutor-pro"),content:(tf===null||tf===void 0?void 0:tf.symbol)||"$",placeholder:(0,a.__)("0","tutor-pro"),type:"number",loading:!!l&&!e.field.value,selectOnFocus:true,contentCss:At.i.inputCurrencyStyle}))}}))),(0,n.tZ)(S.Z,{when:(0,y.ro)(O.AO.SUBSCRIPTION)&&((o=k.y.settings)===null||o===void 0?void 0:o.monetize_by)==="tutor"},(0,n.tZ)(Yd,{courseId:Xd,isBundle:true})))};const rf=ef;var nf={priceRadioGroup:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[36],";"+(true?"":0),true?"":0),coursePriceWrapper:(0,n.iv)("display:grid;grid-template-columns:1fr 1fr;place-items:start;gap:",h.W0[16],";"+(true?"":0),true?"":0),regularPrice:(0,n.iv)(At.i.display.flex("column"),";gap:",h.W0[4],";label{",b.c.caption(),";color:",h.Jv.text.title,";}div{",b.c.body(),";",At.i.display.flex(),";align-items:center;color:",h.Jv.text.title,";height:40px;}"+(true?"":0),true?"":0)};var of=r(460);var af=r(2391);var uf=r(2592);var lf=r(462);var cf=r(2582);var sf=r(8901);function df(){df=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return df.apply(this,arguments)}function ff(t,e){return yf(t)||bf(t,e)||vf(t,e)||pf()}function pf(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function vf(t,e){if(!t)return;if(typeof t==="string")return hf(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hf(t,e)}function hf(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function bf(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function yf(t){if(Array.isArray(t))return t}function gf(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var mf=function t(){var e,r,o,i;var l=(0,c.Gc)();var f=(0,c.qo)({name:"post_date"});var p=(e=(0,c.qo)({name:"schedule_date"}))!==null&&e!==void 0?e:"";var h=(r=(0,c.qo)({name:"schedule_time"}))!==null&&r!==void 0?r:(0,s.WU)((0,of.T)(new Date,1),O.E_.hoursMinutes);var b=(o=(0,c.qo)({name:"isScheduleEnabled"}))!==null&&o!==void 0?o:false;var y=(i=(0,c.qo)({name:"showScheduleForm"}))!==null&&i!==void 0?i:false;var g=(0,u.useState)(p&&h&&(0,af.J)(new Date("".concat(p," ").concat(h)))?(0,s.WU)(new Date("".concat(p," ").concat(h)),O.E_.yearMonthDayHourMinuteSecond24H):""),m=ff(g,2),w=m[0],x=m[1];var _=function t(){l.setValue("schedule_date","",{shouldDirty:true});l.setValue("schedule_time","",{shouldDirty:true});l.setValue("showScheduleForm",true,{shouldDirty:true})};var Z=function t(){var e=(0,uf.R)(new Date(f),new Date);l.setValue("schedule_date",e&&w?(0,s.WU)((0,lf.D)(w),O.E_.yearMonthDay):"",{shouldDirty:true});l.setValue("schedule_time",e&&w?(0,s.WU)((0,lf.D)(w),O.E_.hoursMinutes):"",{shouldDirty:true})};var k=function t(){if(!p||!h){return}l.setValue("showScheduleForm",false,{shouldDirty:true});x((0,s.WU)(new Date("".concat(p," ").concat(h)),O.E_.yearMonthDayHourMinuteSecond24H))};(0,u.useEffect)((function(){if(b&&y){l.setFocus("schedule_date")}}),[y,b]);return(0,n.tZ)("div",{css:xf.scheduleOptions},(0,n.tZ)(c.Qr,{name:"isScheduleEnabled",control:l.control,render:function t(e){return(0,n.tZ)(jn,df({},e,{label:(0,a.__)("Schedule","tutor-pro"),onChange:function t(e){if(!e&&p&&h){l.setValue("showScheduleForm",false,{shouldDirty:true})}}}))}}),b&&y&&(0,n.tZ)("div",{css:xf.formWrapper},(0,n.tZ)("div",{css:At.i.dateAndTimeWrapper},(0,n.tZ)(c.Qr,{name:"schedule_date",control:l.control,rules:{required:(0,a.__)("Schedule date is required.","tutor-pro"),validate:{invalidDateRule:Nn,futureDate:function t(e){if((0,uf.R)(new Date("".concat(e)),(0,cf.b)(new Date))){return(0,a.__)("Schedule date should be in the future.","tutor-pro")}return true}}},render:function t(e){return(0,n.tZ)(Bc,df({},e,{isClearable:false,placeholder:(0,a.__)("Select date","tutor-pro"),disabledBefore:(0,s.WU)(new Date,O.E_.yearMonthDay),onChange:function t(){l.setFocus("schedule_time")},dateFormat:O.E_.monthDayYear}))}}),(0,n.tZ)(c.Qr,{name:"schedule_time",control:l.control,rules:{required:(0,a.__)("Schedule time is required.","tutor-pro"),validate:{invalidTimeRule:Bn,futureDate:function t(e){if((0,uf.R)(new Date("".concat(l.watch("schedule_date")," ").concat(e)),new Date)){return(0,a.__)("Schedule time should be in the future.","tutor-pro")}return true}}},render:function t(e){return(0,n.tZ)(ls,df({},e,{interval:60,isClearable:false,placeholder:"hh:mm A"}))}})),(0,n.tZ)("div",{css:xf.scheduleButtonsWrapper},(0,n.tZ)(v.Z,{variant:"tertiary",size:"small",onClick:Z,disabled:!p&&!h||(0,af.J)(new Date("".concat(p," ").concat(h)))&&(0,sf.x)(new Date("".concat(p," ").concat(h)),new Date(w))},(0,a.__)("Cancel","tutor-pro")),(0,n.tZ)(v.Z,{variant:"secondary",size:"small",onClick:l.handleSubmit(k),disabled:!p||!h},(0,a.__)("Ok","tutor-pro")))),b&&!y&&(0,n.tZ)("div",{css:xf.scheduleInfoWrapper},(0,n.tZ)("div",{css:xf.scheduledFor},(0,n.tZ)("div",{css:xf.scheduleLabel},(0,a.__)("Scheduled for","tutor-pro")),(0,n.tZ)("div",{css:xf.scheduleInfoButtons},(0,n.tZ)("button",{type:"button",css:At.i.actionButton,onClick:_},(0,n.tZ)(d.Z,{name:"delete",width:24,height:24})),(0,n.tZ)("button",{type:"button",css:At.i.actionButton,onClick:function t(){l.setValue("showScheduleForm",true,{shouldDirty:true})}},(0,n.tZ)(d.Z,{name:"edit",width:24,height:24})))),(0,n.tZ)(S.Z,{when:p&&h&&(0,af.J)(new Date("".concat(p," ").concat(h)))},(0,n.tZ)("div",{css:xf.scheduleInfo},(0,a.sprintf)((0,a.__)("%s at %s","tutor-pro"),(0,s.WU)((0,lf.D)(p),O.E_.monthDayYear),h)))))};const wf=mf;var xf={scheduleOptions:(0,n.iv)("padding:",h.W0[12],";border:1px solid ",h.Jv.stroke["default"],";border-radius:",h.E0[8],";gap:",h.W0[8],";background-color:",h.Jv.bg.white,";"+(true?"":0),true?"":0),formWrapper:(0,n.iv)("margin-top:",h.W0[16],";"+(true?"":0),true?"":0),scheduleButtonsWrapper:(0,n.iv)("display:flex;gap:",h.W0[12],";margin-top:",h.W0[8],";button{width:100%;span{justify-content:center;}}"+(true?"":0),true?"":0),scheduleInfoWrapper:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[8],";margin-top:",h.W0[12],";"+(true?"":0),true?"":0),scheduledFor:true?{name:"bcffy2",styles:"display:flex;align-items:center;justify-content:space-between"}:0,scheduleLabel:(0,n.iv)(b.c.caption(),";color:",h.Jv.text.subdued,";"+(true?"":0),true?"":0),scheduleInfoButtons:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[8],";"+(true?"":0),true?"":0),scheduleInfo:(0,n.iv)(b.c.caption(),";background-color:",h.Jv.background.status.processing,";padding:",h.W0[8],";border-radius:",h.E0[4],";text-align:center;"+(true?"":0),true?"":0)};function _f(t){"@babel/helpers - typeof";return _f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_f(t)}var Zf,Of;function Sf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function kf(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Sf(Object(r),!0).forEach((function(e){jf(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Sf(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function jf(t,e,r){e=Ef(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function Ef(t){var e=Cf(t,"string");return _f(e)==="symbol"?e:String(e)}function Cf(t,e){if(_f(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(_f(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Af(){Af=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Af.apply(this,arguments)}var Pf=(0,Vd.X)();var Wf=!!k.y.tutor_pro_url;var If=((Zf=k.y.settings)===null||Zf===void 0?void 0:Zf.chatgpt_enable)==="on";var Lf=(0,y.ro)(O.AO.SUBSCRIPTION)&&((Of=k.y.settings)===null||Of===void 0?void 0:Of.membership_only_mode);var Df=[{label:(0,a.__)("Show Discount % off","tutor-pro"),value:"in_percentage"},{label:(0,a.sprintf)((0,a.__)("Show discount amount (%s)","tutor-pro"),k.y.tutor_currency.symbol),value:"in_amount"},{label:(0,a.__)("Show None","tutor-pro"),value:"none"}];var Tf=function t(){var e=(0,c.Gc)();var r=(0,i.y)({queryKey:["CourseBundle",Pf]});var o=e.watch("details.authors");var u=e.watch("post_modified");var l=e.watch("visibility");return(0,n.tZ)("div",{css:Mf.sidebar},(0,n.tZ)("div",{css:Mf.statusAndDate},(0,n.tZ)(c.Qr,{name:"visibility",control:e.control,render:function t(o){return(0,n.tZ)(Te,Af({},o,{label:(0,a.__)("Visibility","tutor-pro"),placeholder:(0,a.__)("Select visibility status","tutor-pro"),options:O.R_,leftIcon:(0,n.tZ)(d.Z,{name:"eye",width:32,height:32}),loading:!!r&&!o.field.value,onChange:function t(){e.setValue("post_password","")}}))}}),(0,n.tZ)(S.Z,{when:u},(function(t){return(0,n.tZ)("div",{css:Mf.updatedOn},(0,a.sprintf)((0,a.__)("Last updated on %s","tutor-pro"),(0,s.WU)(new Date(t),O.E_.dayMonthYear)||""))}))),(0,n.tZ)(S.Z,{when:l==="password_protected"},(0,n.tZ)(c.Qr,{name:"post_password",control:e.control,rules:{required:(0,a.__)("Password is required","tutor-pro")},render:function t(e){return(0,n.tZ)(_o,Af({},e,{label:(0,a.__)("Password","tutor-pro"),placeholder:(0,a.__)("Enter password","tutor-pro"),type:"password",isPassword:true,loading:!!r&&!e.field.value}))}})),(0,n.tZ)(wf,null),(0,n.tZ)(c.Qr,{name:"thumbnail",control:e.control,render:function t(e){return(0,n.tZ)(vl,Af({},e,{label:(0,a.__)("Featured Image","tutor-pro"),buttonText:(0,a.__)("Upload Thumbnail","tutor-pro"),infoText:(0,a.sprintf)((0,a.__)("JPEG, PNG, GIF, and WebP formats, up to %s","tutor-pro"),k.y.max_upload_size),generateWithAi:!Wf||If,loading:!!r&&!e.field.value}))}}),(0,n.tZ)(S.Z,{when:!Lf},(0,n.tZ)(rf,null)),(0,n.tZ)(c.Qr,{name:"ribbon_type",control:e.control,render:function t(e){return(0,n.tZ)(Te,Af({},e,{label:(0,a.__)("Select ribbon to display","tutor-pro"),placeholder:(0,a.__)("Select ribbon","tutor-pro"),options:Df,loading:!!r&&!e.field.value}))}}),(0,n.tZ)(c.Qr,{name:"details.categories",control:e.control,render:function t(e){var o;return(0,n.tZ)(ui,Af({},e,{field:kf(kf({},e.field),{},{value:(o=e.field.value)===null||o===void 0?void 0:o.map((function(t){return t.term_id}))}),label:(0,a.__)("Categories","tutor-pro"),disabled:true,loading:!!r&&!e.field.value}))}}),(0,n.tZ)(S.Z,{when:o.length>0},(0,n.tZ)("div",{css:Mf.labelWithContent},(0,n.tZ)("label",null,(0,a.__)("Instructors")),(0,n.tZ)("div",{css:Mf.instructorsWrapper},(0,n.tZ)(ue,{each:o},(function(t){return(0,n.tZ)("div",{key:t.user_id,css:Mf.instructor},(0,n.tZ)(S.Z,{when:t.avatar_url,fallback:(0,n.tZ)("div",{"data-avatar":true},t.display_name.charAt(0).toUpperCase())},(0,n.tZ)("img",{src:t.avatar_url,alt:t.display_name,"data-avatar":true})),(0,n.tZ)("div",null,(0,n.tZ)("div",{"data-name":"instructor-name"},t.display_name),(0,n.tZ)("div",{"data-name":"instructor-email"},t.user_email)))}))))))};const Jf=Tf;var Mf={sidebar:(0,n.iv)("border-left:1px solid ",h.Jv.stroke.divider,";min-height:calc(100vh - ",h.J9,"px);padding-left:",h.W0[32],";padding-block:",h.W0[24],";display:flex;flex-direction:column;gap:",h.W0[16],";",h.Uo.smallTablet,"{border-left:none;border-top:1px solid ",h.Jv.stroke.divider,";padding-block:",h.W0[16],";padding-left:0;}"+(true?"":0),true?"":0),statusAndDate:(0,n.iv)(At.i.display.flex("column"),";gap:",h.W0[4],";"+(true?"":0),true?"":0),updatedOn:(0,n.iv)(b.c.caption(),";color:",h.Jv.text.hints,";"+(true?"":0),true?"":0),priceRadioGroup:(0,n.iv)(At.i.display.flex(),";align-items:center;gap:",h.W0[36],";"+(true?"":0),true?"":0),coursePriceWrapper:(0,n.iv)(At.i.display.flex(),";align-items:flex-start;gap:",h.W0[16],";"+(true?"":0),true?"":0),labelWithContent:(0,n.iv)(At.i.display.flex("column"),";gap:",h.W0[4],";label{",b.c.caption(),";color:",h.Jv.text.title,";}"+(true?"":0),true?"":0),categoriesWrapper:(0,n.iv)(At.i.display.flex(),";gap:",h.W0[8],";"+(true?"":0),true?"":0),category:(0,n.iv)("padding:",h.W0[4]," ",h.W0[8],";border-radius:",h.E0[24],";background-color:",h.Jv.surface.wordpress,";",b.c.small(),";color:",h.Jv.text.title,";"+(true?"":0),true?"":0),instructorsWrapper:(0,n.iv)(At.i.display.flex("column"),";gap:",h.W0[8],";"+(true?"":0),true?"":0),instructor:(0,n.iv)(At.i.display.flex(),";align-items:center;gap:",h.W0[10],";padding:",h.W0[8]," ",h.W0[12],";border-radius:",h.E0[4],";background-color:",h.Jv.background.white,";[data-avatar]{width:40px;height:40px;",At.i.flexCenter(),";border-radius:",h.E0.circle,";border:1px solid ",h.Jv.stroke["default"],";background-color:",h.Jv.background["default"],";}[data-name='instructor-name']{",b.c.caption("medium"),";}[data-name='instructor-email']{",b.c.small(),";color:",h.Jv.text.subdued,";}"+(true?"":0),true?"":0)};var Nf=l().forwardRef((function(t,e){var r=t.children,o=t.className,i=t.bordered,a=i===void 0?false:i,u=t.wrapperCss;return(0,n.tZ)("div",{ref:e,className:o,css:[zf.wrapper(a),u,true?"":0,true?"":0]},r)}));Nf.displayName="Box";var Ff=l().forwardRef((function(t,e){var r=t.children,o=t.className,i=t.separator,a=i===void 0?false:i,u=t.tooltip;return(0,n.tZ)("div",{ref:e,className:o,css:zf.title(a)},(0,n.tZ)("span",null,r),(0,n.tZ)(S.Z,{when:u},(0,n.tZ)(Pt.Z,{content:u},(0,n.tZ)(d.Z,{name:"info",width:20,height:20}))))}));Ff.displayName="BoxTitle";var Bf=l().forwardRef((function(t,e){var r=t.children,o=t.className;return(0,n.tZ)("div",{ref:e,className:o,css:zf.subtitle},(0,n.tZ)("span",null,r))}));Bf.displayName="BoxSubtitle";var zf={wrapper:function t(e){return(0,n.iv)("background-color:",h.Jv.background.white,";border-radius:",h.E0[8],";padding:",h.W0[12]," ",h.W0[20]," ",h.W0[20],";",e&&(0,n.iv)("border:1px solid ",h.Jv.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},title:function t(e){return(0,n.iv)(b.c.body("medium"),";color:",h.Jv.text.title,";display:flex;gap:",h.W0[4],";align-items:center;",e&&(0,n.iv)("border-bottom:1px solid ",h.Jv.stroke.divider,";padding:",h.W0[12]," ",h.W0[20],";"+(true?"":0),true?"":0)," &>div{height:20px;svg{color:",h.Jv.icon.hints,";}}&>span{display:inline-block;}"+(true?"":0),true?"":0)},subtitle:(0,n.iv)(b.c.caption(),";color:",h.Jv.text.hints,";"+(true?"":0),true?"":0)};function Rf(t){"@babel/helpers - typeof";return Rf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Rf(t)}function Uf(){Uf=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Uf.apply(this,arguments)}function Gf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Qf(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Gf(Object(r),!0).forEach((function(e){Yf(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Gf(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Yf(t,e,r){e=qf(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function qf(t){var e=Hf(t,"string");return Rf(e)==="symbol"?e:String(e)}function Hf(t,e){if(Rf(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Rf(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}var Vf=function t(e){var r=e.onFilterItems;var o=it({defaultValues:{search:""}});var i=Q(o.watch("search"));(0,u.useEffect)((function(){r(Qf({},i.length>0&&{search:i}))}),[r,i]);return(0,n.tZ)(c.Qr,{control:o.control,name:"search",render:function t(e){return(0,n.tZ)(wl,Uf({},e,{content:(0,n.tZ)(d.Z,{name:"search",width:24,height:24}),placeholder:(0,a.__)("Search...","tutor-pro"),showVerticalBar:false}))}})};const $f=Vf;function Kf(t){"@babel/helpers - typeof";return Kf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Kf(t)}function Xf(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function tp(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Xf(Object(r),!0).forEach((function(e){ep(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Xf(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ep(t,e,r){e=rp(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function rp(t){var e=np(t,"string");return Kf(e)==="symbol"?e:String(e)}function np(t,e){if(Kf(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(Kf(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function op(t,e){return cp(t)||lp(t,e)||ap(t,e)||ip()}function ip(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ap(t,e){if(!t)return;if(typeof t==="string")return up(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return up(t,e)}function up(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function lp(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function cp(t){if(Array.isArray(t))return t}var sp=function t(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},r=e.limit,n=r===void 0?O.gK:r;var o=(0,u.useState)({page:1,sortProperty:"",sortDirection:undefined,filter:{}}),i=op(o,2),a=i[0],l=i[1];var c=a;var s=n*Math.max(0,c.page-1);var d=(0,u.useCallback)((function(t){l((function(e){return tp(tp({},e),t)}))}),[l]);var f=function t(e){return d({page:e})};var p=(0,u.useCallback)((function(t){return d({page:1,filter:t})}),[d]);var v=function t(e){var r={};if(e!==c.sortProperty){r={sortDirection:"asc",sortProperty:e}}else{r={sortDirection:c.sortDirection==="asc"?"desc":"asc",sortProperty:e}}d(r)};return{pageInfo:c,onPageChange:f,onColumnSort:v,offset:s,itemsPerPage:n,onFilterItems:p}};function dp(t,e){return bp(t)||hp(t,e)||pp(t,e)||fp()}function fp(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function pp(t,e){if(!t)return;if(typeof t==="string")return vp(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return vp(t,e)}function vp(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function hp(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function bp(t){if(Array.isArray(t))return t}var yp=function t(e){var r=e.currentPage,o=e.onPageChange,i=e.totalItems,l=e.itemsPerPage;var c=Math.max(Math.ceil(i/l),1);var s=(0,u.useState)(""),f=dp(s,2),p=f[0],v=f[1];(0,u.useEffect)((function(){v(r.toString())}),[r]);var h=function t(e){if(e<1||e>c){return}o(e)};return(0,n.tZ)("div",{css:mp.wrapper},(0,n.tZ)("div",{css:mp.pageStatus},(0,a.__)("Page","tutor"),(0,n.tZ)("span",null,(0,n.tZ)("input",{type:"text",css:mp.paginationInput,value:p,onChange:function t(e){var r=e.currentTarget.value;var n=r.replace(/[^0-9]/g,"");var i=Number(n);if(i>0&&i<=c){v(n);o(i)}else if(!n){v(n)}},autoComplete:"off"})),(0,a.__)("of","tutor")," ",(0,n.tZ)("span",null,c)),(0,n.tZ)("div",{css:mp.pageController},(0,n.tZ)("button",{type:"button",css:mp.paginationButton,onClick:function t(){return h(r-1)},disabled:r===1},(0,n.tZ)(d.Z,{name:!O.dZ?"chevronLeft":"chevronRight",width:32,height:32})),(0,n.tZ)("button",{type:"button",css:mp.paginationButton,onClick:function t(){return h(r+1)},disabled:r===c},(0,n.tZ)(d.Z,{name:!O.dZ?"chevronRight":"chevronLeft",width:32,height:32}))))};const gp=yp;var mp={wrapper:(0,n.iv)("display:flex;justify-content:end;align-items:center;flex-wrap:wrap;gap:",h.W0[8],";height:36px;"+(true?"":0),true?"":0),pageStatus:(0,n.iv)(b.c.body()," color:",h.Jv.text.title,";min-width:100px;"+(true?"":0),true?"":0),paginationInput:(0,n.iv)("outline:0;border:1px solid ",h.Jv.stroke["default"],";border-radius:",h.E0[6],";margin:0 ",h.W0[8],";color:",h.Jv.text.subdued,";padding:8px 12px;width:72px;&::-webkit-outer-spin-button,&::-webkit-inner-spin-button{-webkit-appearance:none;margin:",h.W0[0],";}&[type='number']{-moz-appearance:textfield;}"+(true?"":0),true?"":0),pageController:(0,n.iv)("gap:",h.W0[8],";display:flex;justify-content:center;align-items:center;height:100%;"+(true?"":0),true?"":0),paginationButton:(0,n.iv)(At.i.resetButton,";background:",h.Jv.background.white,";color:",h.Jv.icon["default"],";border-radius:",h.E0[6],";height:32px;width:32px;display:grid;place-items:center;transition:background-color 0.2s ease-in-out,color 0.3s ease-in-out;svg{color:",h.Jv.icon["default"],";}&:hover{background:",h.Jv.background["default"],";&>svg{color:",h.Jv.icon.brand,";}}&:disabled{background:",h.Jv.background.white,";&>svg{color:",h.Jv.icon.disable["default"],";}}"+(true?"":0),true?"":0)};function xp(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var _p={bodyRowSelected:h.Jv.background.active,bodyRowHover:h.Jv.background.hover};var Zp=true?{name:"1azakc",styles:"text-align:center"}:0;var Op=function t(e){var r=e.columns,o=e.data,i=e.entireHeader,a=i===void 0?null:i,u=e.headerHeight,l=u===void 0?60:u,c=e.noHeader,s=c===void 0?false:c,f=e.isStriped,p=f===void 0?false:f,v=e.isRounded,h=v===void 0?false:v,b=e.stripedBySelectedIndex,g=b===void 0?[]:b,m=e.colors,w=m===void 0?{}:m,x=e.isBordered,_=x===void 0?true:x,Z=e.loading,O=Z===void 0?false:Z,S=e.itemsPerPage,k=S===void 0?1:S,j=e.querySortProperty,E=e.querySortDirection,C=E===void 0?"asc":E,A=e.onSortClick,P=e.renderInLastRow,W=e.rowStyle;var I=function t(e,o){return(0,n.tZ)("tr",{key:e,css:[jp.tableRow({isBordered:_,isStriped:p}),jp.bodyTr({colors:w,isSelected:g.includes(e),isRounded:h}),W,true?"":0,true?"":0]},r.map((function(t,e){return(0,n.tZ)("td",{key:e,css:[jp.td,{width:t.width},true?"":0,true?"":0]},o(t))})))};var L=function t(e){var r=null;var o=e.sortProperty;if(!o){return e.Header}if(e.sortProperty===j){if(C==="asc"){r=(0,n.tZ)(d.Z,{name:"chevronDown"})}else{r=(0,n.tZ)(d.Z,{name:"chevronUp"})}}return(0,n.tZ)("button",{type:"button",css:jp.headerWithIcon,onClick:function t(){return A===null||A===void 0?void 0:A(o)}},e.Header,r&&r)};var D=function t(){if(a){return(0,n.tZ)("th",{css:jp.th,colSpan:r.length},a)}return r.map((function(t,e){if(t.Header!==null){return(0,n.tZ)("th",{key:e,css:[jp.th,{width:t.width},true?"":0,true?"":0],colSpan:t.headerColSpan},L(t))}}))};var T=function t(){if(O){return(0,y.w6)(k).map((function(t){return I(t,(function(){return(0,n.tZ)($e,{animation:true,height:20,width:"".concat((0,y.sZ)(40,80),"%")})}))}))}if(!o.length){return(0,n.tZ)("tr",{css:jp.tableRow({isBordered:false,isStriped:false})},(0,n.tZ)("td",{colSpan:r.length,css:[jp.td,Zp,true?"":0,true?"":0]},"No Data!"))}var e=o.map((function(t,e){return I(e,(function(r){return"Cell"in r?r.Cell(t,e):r.accessor(t,e)}))}));if(P){P=(0,n.tZ)("tr",{key:e.length},(0,n.tZ)("td",{css:jp.td},P));e.push(P)}return e};return(0,n.tZ)("div",{css:jp.tableContainer({isRounded:h})},(0,n.tZ)("table",{css:jp.table},!s&&(0,n.tZ)("thead",null,(0,n.tZ)("tr",{css:[jp.tableRow({isBordered:_,isStriped:p}),{height:l},true?"":0,true?"":0]},D())),(0,n.tZ)("tbody",null,T())))};const Sp=Op;var kp=true?{name:"1hr9znz",styles:":last-of-type{border-bottom:none;}"}:0;var jp={tableContainer:function t(e){var r=e.isRounded;return(0,n.iv)("display:block;width:100%;overflow-x:auto;",r&&(0,n.iv)("border:1px solid ",h.Jv.stroke.divider,";border-radius:",h.E0[6],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},headerWithIcon:(0,n.iv)(At.i.resetButton,";",b.c.body(),";color:",h.Jv.text.subdued,";display:flex;gap:",h.W0[4],";align-items:center;"+(true?"":0),true?"":0),table:true?{name:"1k58b2x",styles:"width:100%;border-collapse:collapse;border:none"}:0,tableRow:function t(e){var r=e.isBordered,o=e.isStriped;return(0,n.iv)(r&&(0,n.iv)("border-bottom:1px solid ",h.Jv.stroke.divider,";"+(true?"":0),true?"":0)," ",o&&(0,n.iv)("&:nth-of-type(even){background-color:",h.Jv.background.active,";}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},th:(0,n.iv)(b.c.body(),";background-color:",h.Jv.background.white,";color:",h.Jv.text.primary,";padding:0 ",h.W0[16],";border:none;"+(true?"":0),true?"":0),bodyTr:function t(e){var r=e.colors,o=e.isSelected,i=e.isRounded;var a=r.bodyRowDefault,u=r.bodyRowSelectedHover,l=r.bodyRowHover,c=l===void 0?_p.bodyRowHover:l,s=r.bodyRowSelected,d=s===void 0?_p.bodyRowSelected:s;return(0,n.iv)(a&&(0,n.iv)("background-color:",a,";"+(true?"":0),true?"":0)," &:hover{background-color:",o&&u?u:c,";}",o&&(0,n.iv)("background-color:",d,";"+(true?"":0),true?"":0)," ",i&&kp,";"+(true?"":0),true?"":0)},td:(0,n.iv)(b.c.body(),";padding:",h.W0[16],";border:none;"+(true?"":0),true?"":0)};var Ep=r(4139);var Cp=function t(e){return Ot.R.get(St.Z.GET_COURSE_LIST,{params:e})};var Ap=function t(e){var r=e.params,n=e.isEnabled;return(0,xt.a)({queryKey:["PrerequisiteCourses",r],queryFn:function t(){return Cp({exclude:r.exclude,limit:r.limit,offset:r.offset,filter:r.filter}).then((function(t){return t.data}))},placeholderData:Ep.Wk,enabled:n})};var Pp=function t(e){var r=e.courseId,n=e.builder;return Ot.R.post(St.Z.TUTOR_UNLINK_PAGE_BUILDER,{course_id:r,builder:n})};var Wp=function t(){return(0,_t.D)({mutationFn:Pp})};const Ip=r.p+"images/4d4615923a6630682b98f437e34c40a0-course-placeholder.png";var Lp=r(7363);function Dp(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Tp=function t(e){var r;var o=e.onSelectClick,i=e.selectedCourseIds;var u=sp(),l=u.pageInfo,c=u.onPageChange,s=u.itemsPerPage,d=u.offset,f=u.onFilterItems;var p=Ap({params:{offset:d,limit:s,filter:l.filter,excludedIds:[]},isEnabled:true});var h=[{Header:(0,n.tZ)("div",{css:Mp.tableLabel},(0,a.__)("Name","tutor-pro")),Cell:function t(e){var r=i.includes(e.id);return(0,n.tZ)("div",{css:Mp.courseItemWrapper},(0,n.tZ)("img",{src:e.image||Ip,css:Mp.thumbnail,alt:(0,a.__)("Course item","tutor-pro")}),(0,n.tZ)("div",{css:Mp.title({isSelected:r})},e.title,r&&(0,n.tZ)("span",{css:Mp.selectedBadge},(0,a.__)("Already Selected","tutor-pro"))))}},{Header:(0,n.tZ)("div",{css:Mp.tablePriceLabel},(0,a.__)("Price","tutor-pro")),Cell:function t(e){var r=i.includes(e.id);return(0,n.tZ)("div",{css:Mp.priceWrapper},(0,n.tZ)(S.Z,{when:!r},(0,n.tZ)("div",{"data-button":true},(0,n.tZ)(v.Z,{"data-cy":"select-course",size:"small",onClick:function t(){return o(e)}},(0,a.__)("Select","tutor-pro")))),(0,n.tZ)("div",{css:Mp.price,"data-price":!r},(0,n.tZ)(S.Z,{when:e.is_purchasable,fallback:(0,a.__)("Free","tutor-pro")},(0,n.tZ)("span",null,e.sale_price?e.sale_price:e.regular_price),e.sale_price&&(0,n.tZ)("span",{css:Mp.discountPrice},e.regular_price))))}}];if(p.isLoading){return(0,n.tZ)(Z.g4,null)}if(!p.data){return(0,n.tZ)("div",{css:Mp.errorMessage},(0,a.__)("Something went wrong","tutor-pro"))}return(0,n.tZ)(Lp.Fragment,null,(0,n.tZ)("div",{css:Mp.tableActions},(0,n.tZ)($f,{onFilterItems:f})),(0,n.tZ)("div",{css:Mp.tableWrapper},(0,n.tZ)(Sp,{columns:h,data:(r=p.data.results)!==null&&r!==void 0?r:[],itemsPerPage:s,loading:p.isFetching||p.isRefetching})),(0,n.tZ)("div",{css:Mp.paginatorWrapper},(0,n.tZ)(gp,{currentPage:l.page,onPageChange:c,totalItems:p.data.total_items,itemsPerPage:s})))};const Jp=Tp;var Mp={tableLabel:true?{name:"1flj9lk",styles:"text-align:left"}:0,tablePriceLabel:true?{name:"2qga7i",styles:"text-align:right"}:0,tableActions:(0,n.iv)("padding:",h.W0[20],";"+(true?"":0),true?"":0),tableWrapper:true?{name:"fmm6ld",styles:"max-height:calc(100vh - 350px);overflow:auto;tr{&:hover{[data-button]{display:block;}[data-price='true']{display:none;}}}"}:0,paginatorWrapper:(0,n.iv)("margin:",h.W0[20]," ",h.W0[16],";"+(true?"":0),true?"":0),courseItemWrapper:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[16],";"+(true?"":0),true?"":0),bundleBadge:(0,n.iv)(b.c.tiny(),";display:inline-block;padding:0px ",h.W0[8],";background-color:#9342e7;color:",h.Jv.text.white,";border-radius:",h.E0[40],";"+(true?"":0),true?"":0),subscriptionBadge:(0,n.iv)(b.c.tiny(),";display:flex;align-items:center;width:fit-content;padding:0px ",h.W0[6]," 0px ",h.W0[4],";background-color:",h.Jv.color.warning[90],";color:",h.Jv.text.white,";border-radius:",h.E0[40],";"+(true?"":0),true?"":0),selectedBadge:(0,n.iv)("margin-left:",h.W0[4],";",b.c.tiny(),";padding:",h.W0[4]," ",h.W0[8],";background-color:",h.Jv.background.disable,";color:",h.Jv.text.title,";border-radius:",h.E0[2],";white-space:nowrap;"+(true?"":0),true?"":0),title:function t(e){var r=e.isSelected,o=r===void 0?false:r;return(0,n.iv)(b.c.caption(),";color:",h.Jv.text.primary,";",At.i.text.ellipsis(2),";text-wrap:pretty;",o&&(0,n.iv)("color:",h.Jv.text.disable,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},thumbnail:(0,n.iv)("width:48px;height:48px;border-radius:",h.E0[4],";object-fit:cover;object-position:center;"+(true?"":0),true?"":0),priceWrapper:true?{name:"1al764r",styles:"min-width:200px;text-align:right;[data-button]{display:none;}"}:0,price:(0,n.iv)(b.c.caption(),";display:flex;gap:",h.W0[4],";justify-content:end;"+(true?"":0),true?"":0),startingFrom:(0,n.iv)("color:",h.Jv.text.hints,";"+(true?"":0),true?"":0),discountPrice:(0,n.iv)("text-decoration:line-through;color:",h.Jv.text.subdued,";"+(true?"":0),true?"":0),errorMessage:true?{name:"1tw8cl2",styles:"height:100px;display:flex;align-items:center;justify-content:center"}:0};function Np(t){var e=t.title,r=t.closeModal,o=t.actions,i=t.onSelect,a=t.selectedCourseIds;var u=function t(e){i===null||i===void 0?void 0:i(e);r({action:"CONFIRM"})};return(0,n.tZ)(Ir.Z,{onClose:function t(){return r({action:"CLOSE"})},title:e,actions:o,maxWidth:720},(0,n.tZ)(Jp,{onSelectClick:u,selectedCourseIds:a}))}const Fp=Np;var Bp=function t(e){var r=e.onAddCourse,o=e.selectedCourses,i=e.totalEnrolled,u=i===void 0?0:i;var l=(0,an.d)(),c=l.showModal;return(0,n.tZ)("div",{css:Rp.wrapper},(0,n.tZ)("div",{css:Rp.left},(0,n.tZ)("div",{css:b.c.body("medium")},o.length>1?(0,a.sprintf)((0,a.__)("%d Courses selected","tutor-pro"),o.length):(0,a.sprintf)((0,a.__)("%d Course selected","tutor-pro"),o.length))),(0,n.tZ)(Pt.Z,{content:(0,a.__)("You cannot add/remove course(s) from a course bundle with enrolled students as it may disrupt the learning experience.","tutor-pro"),delay:200,disabled:u<=0},(0,n.tZ)(v.Z,{"data-cy":"add-course",variant:"secondary",isOutlined:true,icon:(0,n.tZ)(d.Z,{name:"plusSquareBrand",width:24,height:24}),buttonCss:Rp.addCourseButton,disabled:u>0,onClick:function t(){c({component:Fp,props:{title:(0,a.__)("Add Course","tutor-pro"),onSelect:function t(e){r(e)},selectedCourseIds:o.map((function(t){return t.id}))}})}},(0,a.__)("Add Course","tutor-pro"))))};const zp=Bp;var Rp={wrapper:(0,n.iv)("display:flex;justify-content:space-between;align-items:center;padding:0 ",h.W0[20]," ",h.W0[12]," ",h.W0[20],";border-bottom:1px solid ",h.Jv.stroke.divider,";"+(true?"":0),true?"":0),left:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[8],";"+(true?"":0),true?"":0),addCourseButton:(0,n.iv)("outline:1px solid ",h.Jv.stroke.border,";&:hover{outline:1px solid ",h.Jv.stroke.border,";}"+(true?"":0),true?"":0)};var Up=r(9752);var Gp=r(2339);var Qp=r(5587);var Yp=r(4285);function qp(){qp=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return qp.apply(this,arguments)}function Hp(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Vp=function t(e){var r=e.course,o=e.index,i=e.onRemove,u=e.isOverlay;var l=(0,Qp.nB)({id:r.id}),s=l.attributes,f=l.listeners,p=l.setNodeRef,v=l.transform,b=l.transition,y=l.isDragging;var g=(0,c.Gc)();var m=g.watch("total_enrolled");var w={transform:Yp.ux.Transform.toString(v),transition:b,opacity:y?.3:1,background:y?h.Jv.stroke.hover:undefined};return(0,n.tZ)("div",qp({},s,{ref:p,style:w,css:Xp.wrapper({isOverlay:u,isRemoveBtnDisabled:Number(m)>0})}),(0,n.tZ)("div",{css:Xp.left},(0,n.tZ)("button",qp({},f,{"data-drag-button":true,css:At.i.resetButton}),(0,n.tZ)(d.Z,{name:"dragVertical",width:24,height:24})),(0,n.tZ)("span",{"data-index":true},o),(0,n.tZ)("img",{src:r.image,alt:r.title}),(0,n.tZ)("p",null,r.title)),(0,n.tZ)("div",{css:Xp.right},(0,n.tZ)(Pt.Z,{content:(0,a.__)("You cannot remove course(s) from a course bundle with enrolled students as it may disrupt the learning experience.","tutor-pro"),delay:200,disabled:m<=0},(0,n.tZ)("button",{"data-cy":"remove-course","data-cross-button":true,css:At.i.resetButton,disabled:m>0,onClick:function t(){return i===null||i===void 0?void 0:i()}},(0,n.tZ)(d.Z,{name:"cross",width:24,height:24}))),(0,n.tZ)(S.Z,{when:r.is_purchasable,fallback:(0,n.tZ)("span",{"data-price":true,css:Xp.price({hasSalePrice:false})},(0,a.__)("Free","tutor-pro"))},(0,n.tZ)(S.Z,{when:r.sale_price,fallback:(0,n.tZ)("span",{"data-price":true,css:Xp.price({hasSalePrice:false})},r.regular_price)},(0,n.tZ)("span",{"data-price":true,css:Xp.price({hasSalePrice:true})},r.regular_price),(0,n.tZ)("span",{"data-price":true,css:Xp.price({hasSalePrice:false})},r.sale_price)))))};const $p=Vp;var Kp=true?{name:"1sfig4b",styles:"cursor:not-allowed"}:0;var Xp={wrapper:function t(e){var r=e.isOverlay,o=r===void 0?false:r,i=e.isRemoveBtnDisabled,a=i===void 0?false:i;return(0,n.iv)(At.i.display.flex(),";justify-content:space-between;align-items:center;padding:",h.W0[16]," ",h.W0[20],";border-bottom:1px solid ",h.Jv.stroke.divider,";gap:",h.W0[28],";background-color:",h.Jv.background.white,";[data-drag-button]{cursor:grab;display:none;color:",h.Jv.icon.hints,";}[data-cross-button]{display:none;color:",h.Jv.color.black[50],";transition:color 0.3s ease-in-out;",a&&Kp," &:hover{color:",!a&&h.Jv.color.black[100],";}}",o&&(0,n.iv)("box-shadow:",h.AF.drag,";border-bottom:none;border-radius:",h.E0.card,";background-color:",h.Jv.background.hover,";cursor:grabbing;"+(true?"":0),true?"":0)," &:hover{background-color:",h.Jv.background.hover,";[data-index],[data-price]{display:none;}[data-drag-button],[data-cross-button]{display:block;}}"+(true?"":0),true?"":0)},left:(0,n.iv)(At.i.display.flex(),";align-items:center;gap:",h.W0[16],";img{width:86px;height:",h.W0[48],";object-fit:cover;object-position:center;border-radius:",h.E0[2],";flex-shrink:0;}p,span{",b.c.caption(),";",At.i.text.ellipsis(2),";}span{flex-shrink:0;width:",h.W0[24],";",At.i.flexCenter(),";}"+(true?"":0),true?"":0),right:(0,n.iv)(At.i.display.flex(),";align-items:center;justify-content:flex-end;flex-shrink:0;max-width:120px;width:100%;gap:",h.W0[8],";"+(true?"":0),true?"":0),price:function t(e){var r=e.hasSalePrice,o=r===void 0?false:r;return(0,n.iv)(b.c.caption(),";color:",o?h.Jv.text.subdued:h.Jv.text.primary,";text-decoration:",o?"line-through":"none",";"+(true?"":0),true?"":0)}};function tv(t,e){return iv(t)||ov(t,e)||rv(t,e)||ev()}function ev(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function rv(t,e){if(!t)return;if(typeof t==="string")return nv(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nv(t,e)}function nv(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function ov(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function iv(t){if(Array.isArray(t))return t}var av=function t(e){var r=e.courses,o=e.onRemove,i=e.onSort;var a=(0,u.useState)(null),l=tv(a,2),c=l[0],s=l[1];var d=(0,u.useMemo)((function(){return r.find((function(t){return t.id===c}))}),[c,r]);var f=(0,Up.Dy)((0,Up.VT)(Up.we,{activationConstraint:{distance:10}}),(0,Up.VT)(Up.Lg,{coordinateGetter:Qp.is}));return(0,n.tZ)(Up.LB,{sensors:f,collisionDetection:Up.pE,measuring:qs,modifiers:[Gp.hg],onDragStart:function t(e){s(e.active.id)},onDragEnd:function t(e){var n=e.active,o=e.over;if(!o||n.id===o.id){s(null);return}var a=r.findIndex((function(t){return t.id===n.id}));var u=r.findIndex((function(t){return t.id===o.id}));i(a,u)}},(0,n.tZ)(Qp.Fo,{items:r,strategy:Qp.qw},(0,n.tZ)(ue,{each:r},(function(t,e){return(0,n.tZ)($p,{key:t.id,course:t,index:e+1,onRemove:function t(){return o(e)}})}))),(0,Sl.createPortal)((0,n.tZ)(Up.y9,null,(0,n.tZ)(S.Z,{when:d},(function(t){return(0,n.tZ)($p,{course:t,index:0,onRemove:y.ZT,isOverlay:true})}))),document.body))};const uv=av;var lv=function t(){var e=(0,c.Gc)();var r=e.watch("details.overview");var o={total_duration:"clock",total_quizzes:"questionCircle",total_video_contents:"videoCamera",total_resources:"download"};var i={total_duration:(0,a.__)("Minutes Total Duration","tutor-pro"),total_quizzes:(0,a.__)("Quiz Papers","tutor-pro"),total_video_contents:(0,a.__)("Lesson Content","tutor-pro"),total_resources:(0,a.__)("Downloadable Resources","tutor-pro")};return(0,n.tZ)("div",{css:sv.wrapper},(0,n.tZ)("div",{css:sv.title},(0,a.__)("Selection Overview","tutor-pro")),(0,n.tZ)("div",{css:sv.overview},Object.keys(o).map((function(t){var e=r[t];return(0,n.tZ)("div",{css:sv.overviewItem,key:t},(0,n.tZ)(d.Z,{name:o[t],width:32,height:32}),(0,n.tZ)("span",null,t==="total_duration"?String(e).replace(/:\d{2}$/,""):e),(0,n.tZ)("span",null,i[t]))}))))};const cv=lv;var sv={wrapper:(0,n.iv)("padding:",h.W0[12]," ",h.W0[20]," 0 ",h.W0[20],";"+(true?"":0),true?"":0),title:(0,n.iv)(b.c.body("medium"),";padding-bottom:",h.W0[12],";"+(true?"":0),true?"":0),overview:(0,n.iv)("display:grid;grid-template-columns:1fr 1fr;gap:",h.W0[4],";"+(true?"":0),true?"":0),overviewItem:(0,n.iv)(At.i.display.flex(),";gap:",h.W0[8],";align-items:center;",b.c.caption(),";svg{color:",h.Jv.icon["default"],";flex-shrink:0;}span:first-of-type:not(:only-of-type){font-weight:",h.Ue.semiBold,";flex-shrink:0;}"+(true?"":0),true?"":0)};var dv=r(9776);const fv=r.p+"images/a0821d0a40df52034db17af368a7a7bc-bundle-empty-state.webp";function pv(t){"@babel/helpers - typeof";return pv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},pv(t)}function vv(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */vv=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(k([])));y&&y!==e&&r.call(y,i)&&(h=y);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==pv(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function hv(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function bv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hv(Object(r),!0).forEach((function(e){yv(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hv(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function yv(t,e,r){e=gv(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function gv(t){var e=mv(t,"string");return pv(e)==="symbol"?e:String(e)}function mv(t,e){if(pv(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(pv(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function wv(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function xv(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){wv(i,n,o,a,u,"next",t)}function u(t){wv(i,n,o,a,u,"throw",t)}a(undefined)}))}}var _v=(0,Vd.X)();var Zv=function t(e){var r=e.loading;var o=(0,c.Gc)();var i=(0,an.d)(),u=i.showModal;var l=(0,c.Dq)({control:o.control,name:"details.courses",keyName:"_id"}),s=l.fields,f=l.append,p=l.remove,h=l.move;var y=o.watch("total_enrolled");var g=(0,dv.F3)();var m=function(){var t=xv(vv().mark((function t(e){var r,n;return vv().wrap((function t(i){while(1)switch(i.prev=i.next){case 0:f(e);i.next=3;return g.mutateAsync({ID:_v,course_id:e.id,user_action:"add_course"});case 3:r=i.sent;if(r.data){o.setValue("details",bv(bv({},r.data),{},{subtotal_raw_sale_price:(n=r.data.subtotal_raw_price)!==null&&n!==void 0?n:""}))}case 5:case"end":return i.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}();var w=function(){var t=xv(vv().mark((function t(e){var r,n,i;return vv().wrap((function t(a){while(1)switch(a.prev=a.next){case 0:p(e);r=s[e];if(r){a.next=4;break}return a.abrupt("return");case 4:a.next=6;return g.mutateAsync({ID:_v,course_id:r.id,user_action:"remove_course"});case 6:n=a.sent;if(n.data){o.setValue("details",bv(bv({},n.data),{},{subtotal_raw_sale_price:(i=n.data.subtotal_raw_price)!==null&&i!==void 0?i:""}))}case 8:case"end":return a.stop()}}),t)})));return function e(r){return t.apply(this,arguments)}}();return(0,n.tZ)("div",{css:Sv.wrapper,"data-cy":"course-selection"},(0,n.tZ)("label",{css:b.c.caption()},(0,a.__)("Courses","tutor-pro")),(0,n.tZ)(Nf,{wrapperCss:Sv.boxWrapper},(0,n.tZ)(S.Z,{when:!r,fallback:(0,n.tZ)(Z.g4,null)},(0,n.tZ)(S.Z,{when:s.length>0,fallback:(0,n.tZ)("div",{css:Sv.emptyState},(0,n.tZ)("img",{src:fv,alt:(0,a.__)("Empty State","tutor-pro")}),(0,n.tZ)("p",null,(0,a.__)("No Courses Added Yet","tutor-pro")),(0,n.tZ)(Pt.Z,{content:(0,a.__)("You cannot add/remove course(s) from a course bundle with enrolled students as it may disrupt the learning experience.","tutor-pro"),delay:200,disabled:Number(y)<=0},(0,n.tZ)(v.Z,{"data-cy":"add-course",variant:"secondary",isOutlined:true,icon:(0,n.tZ)(d.Z,{name:"plusSquareBrand",width:24,height:24}),css:Sv.addCourseButton,disabled:Number(y)>0,onClick:function t(){u({component:Fp,props:{title:(0,a.__)("Add Course","tutor-pro"),onSelect:m,selectedCourseIds:s.map((function(t){return t.id}))}})}},(0,a.__)("Add Course","tutor-pro"))))},(0,n.tZ)(zp,{onAddCourse:m,selectedCourses:s,totalEnrolled:y}),(0,n.tZ)(uv,{courses:s,onRemove:w,onSort:h}),(0,n.tZ)(cv,null)))))};const Ov=Zv;var Sv={wrapper:(0,n.iv)(At.i.display.flex("column"),";gap:",h.W0[6],";"+(true?"":0),true?"":0),boxWrapper:(0,n.iv)("padding-inline:0;border:1px solid ",h.Jv.stroke.divider,";"+(true?"":0),true?"":0),emptyState:(0,n.iv)(At.i.display.flex("column"),";gap:",h.W0[12],";align-items:center;padding-block:",h.W0[32],";img{max-width:60px;width:100%;object-fit:contain;object-position:center;}p{",b.c.body("medium"),";}"+(true?"":0),true?"":0),addCourseButton:(0,n.iv)("outline:1px solid ",h.Jv.stroke.border,";&:hover{outline:1px solid ",h.Jv.stroke.border,";}"+(true?"":0),true?"":0)};var kv=r(7363);function jv(){jv=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return jv.apply(this,arguments)}function Ev(t,e){return Iv(t)||Wv(t,e)||Av(t,e)||Cv()}function Cv(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Av(t,e){if(!t)return;if(typeof t==="string")return Pv(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Pv(t,e)}function Pv(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Wv(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Iv(t){if(Array.isArray(t))return t}var Lv=function t(e){var r=e.field,o=e.fieldState,i=e.label,l=i===void 0?"":i,c=e.baseURL,s=e.onChange;var f=r.value,p=f===void 0?"":f;var h="".concat(c,"/").concat(p);var b=(0,u.useState)(false),g=Ev(b,2),m=g[0],w=g[1];var x=(0,u.useState)(h),_=Ev(x,2),Z=_[0],O=_[1];var S="".concat(c,"/");var k=(0,u.useState)(p),j=Ev(k,2),E=j[0],C=j[1];(0,u.useEffect)((function(){if(c){O("".concat(c,"/").concat(p))}if(p){C(p)}}),[c,p]);return(0,n.tZ)(Lt,{field:r,fieldState:o},(function(t){return(0,n.tZ)("div",{css:Dv.aliasWrapper},l&&(0,n.tZ)("label",{css:Dv.label},l,": "),(0,n.tZ)("div",{css:Dv.linkWrapper},!m?(0,n.tZ)(kv.Fragment,null,(0,n.tZ)("a",{href:Z,target:"_blank",css:Dv.link,title:Z,rel:"noreferrer"},Z),(0,n.tZ)("button",{css:Dv.iconWrapper,type:"button",onClick:function t(){return w((function(t){return!t}))}},(0,n.tZ)(d.Z,{name:"edit",width:24,height:24,style:Dv.editIcon}))):(0,n.tZ)(kv.Fragment,null,(0,n.tZ)("span",{css:Dv.prefix,title:S},S),(0,n.tZ)("div",{css:Dv.editWrapper},(0,n.tZ)("input",jv({},t,{className:"tutor-input-field",css:Dv.editable,type:"text",value:E,onChange:function t(e){return C(e.target.value)},autoComplete:"off"})),(0,n.tZ)(v.Z,{variant:"secondary",isOutlined:true,size:"small",buttonCss:Dv.saveBtn,onClick:function t(){w(false);r.onChange((0,y.k6)(E.replace(c,"")));s===null||s===void 0?void 0:s((0,y.k6)(E.replace(c,"")))}},(0,a.__)("Save","tutor")),(0,n.tZ)(v.Z,{buttonContentCss:Dv.cancelButton,variant:"text",size:"small",onClick:function t(){w(false);C(p)}},(0,a.__)("Cancel","tutor"))))))}))};var Dv={aliasWrapper:(0,n.iv)("display:flex;min-height:32px;align-items:center;gap:",h.W0[4],";",h.Uo.smallMobile,"{flex-direction:column;gap:",h.W0[4],";align-items:flex-start;}"+(true?"":0),true?"":0),label:(0,n.iv)("flex-shrink:0;",b.c.caption(),";color:",h.Jv.text.subdued,";margin:0px;"+(true?"":0),true?"":0),linkWrapper:(0,n.iv)("display:flex;align-items:center;width:fit-content;font-size:",h.JB[14],";",h.Uo.smallMobile,"{gap:",h.W0[4],";flex-wrap:wrap;}"+(true?"":0),true?"":0),link:(0,n.iv)(b.c.caption(),";color:",h.Jv.text.subdued,";text-decoration:none;",At.i.text.ellipsis(1)," max-width:fit-content;word-break:break-all;"+(true?"":0),true?"":0),iconWrapper:(0,n.iv)(At.i.resetButton," margin-left:",h.W0[8],";height:24px;width:24px;background-color:",h.Jv.background.white,";border-radius:",h.E0[4],";:focus{",At.i.inputFocus,";}"+(true?"":0),true?"":0),editIcon:(0,n.iv)("color:",h.Jv.icon["default"],";:hover{color:",h.Jv.icon.brand,";}"+(true?"":0),true?"":0),prefix:(0,n.iv)(b.c.caption()," color:",h.Jv.text.subdued,";",At.i.text.ellipsis(1)," word-break:break-all;max-width:fit-content;"+(true?"":0),true?"":0),editWrapper:(0,n.iv)("margin-left:",h.W0[2],";display:flex;align-items:center;width:fit-content;"+(true?"":0),true?"":0),editable:(0,n.iv)("&.tutor-input-field{",b.c.caption()," background:",h.Jv.background.white,";width:208px;height:32px;border:1px solid ",h.Jv.stroke["default"],";padding:",h.W0[8]," ",h.W0[12],";border-radius:",h.E0.input,";margin-right:",h.W0[8],";outline:none;&:focus{border-color:",h.Jv.stroke["default"],";box-shadow:none;outline:2px solid ",h.Jv.stroke.brand,";outline-offset:1px;}}"+(true?"":0),true?"":0),saveBtn:(0,n.iv)("flex-shrink:0;margin-right:",h.W0[8],";"+(true?"":0),true?"":0),cancelButton:(0,n.iv)("color:",h.Jv.text.brand,";"+(true?"":0),true?"":0)};const Tv=Lv;function Jv(t,e){return zv(t)||Bv(t,e)||Nv(t,e)||Mv()}function Mv(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Nv(t,e){if(!t)return;if(typeof t==="string")return Fv(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Fv(t,e)}function Fv(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Bv(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function zv(t){if(Array.isArray(t))return t}var Rv=!!k.y.tutor_pro_url;if(!window.wp.editor.getDefaultSettings){window.wp.editor.getDefaultSettings=function(){return{}}}function Uv(t,e,r,n,o,i,u,l,c,s,d){var f=s||(n?"bold italic underline | image | ".concat(Rv?"codesample":""):"formatselect bold italic underline | bullist numlist | blockquote | alignleft aligncenter alignright | link unlink | wp_more ".concat(Rv?" codesample":""," | wp_adv"));var p=d||"strikethrough hr | forecolor pastetext removeformat | charmap | outdent indent | undo redo | wp_help | fullscreen | tutor_button | undoRedoDropdown";f=c?f:f.replaceAll(" | "," ");return{tinymce:{wpautop:true,menubar:false,autoresize_min_height:u||200,autoresize_max_height:l||500,wp_autoresize_on:true,browser_spellcheck:!i,convert_urls:false,end_container_on_empty_block:true,entities:"38,amp,60,lt,62,gt",entity_encoding:"raw",fix_list_elements:true,indent:false,relative_urls:0,remove_script_host:0,plugins:"charmap,colorpicker,hr,lists,image,media,paste,tabfocus,textcolor,fullscreen,wordpress,wpautoresize,wpeditimage,wpemoji,wpgallery,wplink,wpdialogs,wptextpattern,wpview".concat(Rv?",codesample":""),skin:"light",skin_url:"".concat(k.y.site_url,"/wp-content/plugins/tutor/assets/lib/tinymce/light"),submit_patch:true,link_context_toolbar:false,theme:"modern",toolbar:!i,toolbar1:f,toolbar2:n?false:p,content_css:"".concat(k.y.site_url,"/wp-includes/css/dashicons.min.css,").concat(k.y.site_url,"/wp-includes/js/tinymce/skins/wordpress/wp-content.css,").concat(k.y.site_url,"/wp-content/plugins/tutor/assets/lib/tinymce/light/content.min.css"),statusbar:!i,branding:false,setup:function u(l){l.on("init",(function(){if(t&&!i){l.getBody().focus()}if(i){l.setMode("readonly");var e=l.contentDocument.querySelector(".mce-content-body");e.style.backgroundColor="transparent";setTimeout((function(){var t=e.scrollHeight;if(t){l.iframeElement.style.height="".concat(t,"px")}}),500)}}));if(!n){l.addButton("tutor_button",{text:(0,a.__)("Tutor ShortCode","tutor"),icon:false,type:"menubutton",menu:[{text:(0,a.__)("Student Registration Form","tutor"),onclick:function t(){l.insertContent("[tutor_student_registration_form]")}},{text:(0,a.__)("Instructor Registration Form","tutor"),onclick:function t(){l.insertContent("[tutor_instructor_registration_form]")}},{text:(0,a._x)("Courses","tinyMCE button courses","tutor"),onclick:function t(){l.windowManager.open({title:(0,a.__)("Courses Shortcode","tutor"),body:[{type:"textbox",name:"id",label:(0,a.__)("Course id, separate by (,) comma","tutor"),value:""},{type:"textbox",name:"exclude_ids",label:(0,a.__)("Exclude Course IDS","tutor"),value:""},{type:"textbox",name:"category",label:(0,a.__)("Category IDS","tutor"),value:""},{type:"listbox",name:"orderby",label:(0,a._x)("Order By","tinyMCE button order by","tutor"),onselect:function t(){},values:[{text:"ID",value:"ID"},{text:"title",value:"title"},{text:"rand",value:"rand"},{text:"date",value:"date"},{text:"menu_order",value:"menu_order"},{text:"post__in",value:"post__in"}]},{type:"listbox",name:"order",label:(0,a._x)("Order","tinyMCE button order","tutor"),onselect:function t(){},values:[{text:"DESC",value:"DESC"},{text:"ASC",value:"ASC"}]},{type:"textbox",name:"count",label:(0,a.__)("Count","tutor"),value:"6"}],onsubmit:function t(e){l.insertContent('[tutor_course id="'.concat(e.data.id,'" exclude_ids="').concat(e.data.exclude_ids,'" category="').concat(e.data.category,'" orderby="').concat(e.data.orderby,'" order="').concat(e.data.order,'" count="').concat(e.data.count,'"]'))}})}}]})}l.on("change keyup paste",(function(){e(l.getContent())}));l.on("focus",(function(){r(true)}));l.on("blur",(function(){return r(false)}));l.on("FullscreenStateChanged",(function(t){var e=document.getElementById("tutor-course-builder");var r=document.getElementById("tutor-course-bundle-builder-root");var n=e||r;if(n){if(t.state){n.style.position="relative";n.style.zIndex="100000"}else{n.removeAttribute("style")}}o===null||o===void 0?void 0:o(t.state)}))},wp_keep_scroll_position:false,wpeditimage_html5_captions:true},mediaButtons:!n&&!i,drag_drop_upload:true,quicktags:n||i?false:{buttons:["strong","em","block","del","ins","img","ul","ol","li","code","more","close"]}}}var Gv=function t(e){var r=e.value,o=r===void 0?"":r,i=e.onChange,a=e.isMinimal,l=e.autoFocus,c=l===void 0?false:l,s=e.onFullScreenChange,d=e.readonly,f=d===void 0?false:d,p=e.min_height,v=e.max_height,h=e.toolbar1,b=e.toolbar2;var g=(0,u.useRef)(null);var m=(0,u.useRef)((0,y.x0)()),w=m.current;var x=(0,u.useState)(c),_=Jv(x,2),Z=_[0],S=_[1];var k=function t(e){var r=e.target;i(r.value)};var j=(0,u.useCallback)((function(t){var e=window,r=e.tinymce;if(!r||Z){return}var n=window.tinymce.get(w);if(n){if(t!==n.getContent()){n.setContent(t)}}}),[w,Z]);(0,u.useEffect)((function(){j(o)}),[o]);(0,u.useEffect)((function(){if(typeof window.wp!=="undefined"&&window.wp.editor){window.wp.editor.remove(w);window.wp.editor.initialize(w,Uv(Z,i,S,a,s,f,p,v,O.iM.isAboveMobile,h,b));var t=g.current;t===null||t===void 0?void 0:t.addEventListener("change",k);t===null||t===void 0?void 0:t.addEventListener("input",k);return function(){window.wp.editor.remove(w);t===null||t===void 0?void 0:t.removeEventListener("change",k);t===null||t===void 0?void 0:t.removeEventListener("input",k)}}}),[f]);return(0,n.tZ)("div",{css:Yv.wrapper({isMinimal:a,isFocused:Z,isReadOnly:f})},(0,n.tZ)("textarea",{"data-cy":"tutor-tinymce",ref:g,id:w,defaultValue:o}))};const Qv=Gv;var Yv={wrapper:function t(e){var r=e.isMinimal,o=e.isFocused,i=e.isReadOnly;return(0,n.iv)("flex:1;.wp-editor-tools{z-index:auto;}.wp-editor-container{border-top-left-radius:",h.E0[6],";border-bottom-left-radius:",h.E0[6],";border-bottom-right-radius:",h.E0[6],";",o&&!i&&(0,n.iv)(At.i.inputFocus,";"+(true?"":0),true?"":0)," :focus-within{",!i&&At.i.inputFocus,";}}.wp-switch-editor{height:auto;border:1px solid #dcdcde;border-radius:0px;border-top-left-radius:",h.E0[4],";border-top-right-radius:",h.E0[4],";top:2px;padding:3px 8px 4px;font-size:13px;color:#646970;&:focus,&:active,&:hover{background:#f0f0f1;color:#646970;}}.mce-btn button{&:focus,&:active,&:hover{background:none;color:#50575e;}}.mce-toolbar-grp,.quicktags-toolbar{border-top-left-radius:",h.E0[6],";",r&&(0,n.iv)("border-top-right-radius:",h.E0[6],";"+(true?"":0),true?"":0),";}.mce-top-part::before{display:none;}.mce-statusbar{border-bottom-left-radius:",h.E0[6],";border-bottom-right-radius:",h.E0[6],";}.mce-tinymce{box-shadow:none;background-color:transparent;}.mce-edit-area{background-color:unset;}",r&&(0,n.iv)(".mce-tinymce.mce-container{border:",!i?"1px solid ".concat(h.Jv.stroke["default"]):"none",";border-radius:",h.E0[6],";",o&&!i&&(0,n.iv)(At.i.inputFocus,";"+(true?"":0),true?"":0),";}"+(true?"":0),true?"":0)," textarea{visibility:visible!important;width:100%;resize:none;border:none;outline:none;padding:",h.W0[10],";}"+(true?"":0),true?"":0)}};var qv=r(8789);function Hv(t){"@babel/helpers - typeof";return Hv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Hv(t)}var Vv;function $v(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */$v=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(k([])));y&&y!==e&&r.call(y,i)&&(h=y);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==Hv(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function Kv(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function Xv(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Kv(i,n,o,a,u,"next",t)}function u(t){Kv(i,n,o,a,u,"throw",t)}a(undefined)}))}}function th(t,e){return ih(t)||oh(t,e)||rh(t,e)||eh()}function eh(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function rh(t,e){if(!t)return;if(typeof t==="string")return nh(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nh(t,e)}function nh(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function oh(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function ih(t){if(Array.isArray(t))return t}function ah(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var uh={droip:"droipColorized",elementor:"elementorColorized",gutenberg:"gutenbergColorized",divi:"diviColorized"};var lh=!!k.y.tutor_pro_url;var ch=(Vv=k.y.settings)===null||Vv===void 0?void 0:Vv.chatgpt_key_exist;var sh=function t(e){var r=e.editorUsed,o=e.onBackToWPEditorClick,i=e.onCustomEditorButtonClick;var l=(0,an.d)(),c=l.showModal;var s=(0,u.useState)(""),f=th(s,2),p=f[0],b=f[1];return(0,n.tZ)("div",{css:ph.editorOverlay},(0,n.tZ)(S.Z,{when:r.name!=="gutenberg"},(0,n.tZ)(v.Z,{variant:"tertiary",size:"small",buttonCss:ph.editWithButton,icon:(0,n.tZ)(d.Z,{name:"arrowLeft",height:24,width:24}),loading:p==="back_to",onClick:Xv($v().mark((function t(){var e,i;return $v().wrap((function t(u){while(1)switch(u.prev=u.next){case 0:u.next=2;return c({component:qv.Z,props:{title:(0,a.__)("Back to WordPress Editor","tutor"),description:(0,n.tZ)(vn,{type:"warning",icon:"warning"},(0,a.__)("Warning: Switching to the WordPress default editor may cause issues with your current layout, design, and content.","tutor")),confirmButtonText:(0,a.__)("Confirm","tutor"),confirmButtonVariant:"primary"},depthIndex:h.W5.highest});case 2:e=u.sent;i=e.action;if(!(i==="CONFIRM")){u.next=17;break}u.prev=5;b("back_to");u.next=9;return o===null||o===void 0?void 0:o(r.name);case 9:u.next=14;break;case 11:u.prev=11;u.t0=u["catch"](5);console.error(u.t0);case 14:u.prev=14;b("");return u.finish(14);case 17:case"end":return u.stop()}}),t,null,[[5,11,14,17]])})))},(0,a.__)("Back to WordPress Editor","tutor"))),(0,n.tZ)(v.Z,{variant:"tertiary",size:"small",buttonCss:ph.editWithButton,loading:p==="edit_with",icon:uh[r.name]&&(0,n.tZ)(d.Z,{name:uh[r.name],height:24,width:24}),onClick:Xv($v().mark((function t(){return $v().wrap((function t(e){while(1)switch(e.prev=e.next){case 0:e.prev=0;b("edit_with");e.next=4;return i===null||i===void 0?void 0:i(r);case 4:window.location.href=r.link;e.next=10;break;case 7:e.prev=7;e.t0=e["catch"](0);console.error(e.t0);case 10:e.prev=10;b("");return e.finish(10);case 13:case"end":return e.stop()}}),t,null,[[0,7,10,13]])})))},(0,a.sprintf)((0,a.__)("Edit with %s","tutor"),r===null||r===void 0?void 0:r.label)))};var dh=function t(e){var r,o,i,l,c;var s=e.label,f=e.field,p=e.fieldState,v=e.disabled,b=e.readOnly,y=e.loading,g=e.placeholder,m=e.helpText,w=e.onChange,x=e.generateWithAi,_=x===void 0?false:x,j=e.onClickAiButton,E=e.hasCustomEditorSupport,C=E===void 0?false:E,A=e.isMinimal,P=A===void 0?false:A,W=e.editors,I=W===void 0?[]:W,L=e.editorUsed,D=L===void 0?{name:"classic",label:"Classic Editor",link:""}:L,T=e.isMagicAi,J=T===void 0?false:T,M=e.autoFocus,N=M===void 0?false:M,F=e.onCustomEditorButtonClick,B=e.onBackToWPEditorClick,z=e.onFullScreenChange,R=e.min_height,U=e.max_height,G=e.toolbar1,Q=e.toolbar2;var Y=(0,an.d)(),q=Y.showModal;var H=((r=k.y.settings)===null||r===void 0?void 0:r.hide_admin_bar_for_users)==="off";var V=(o=k.y.current_user)===null||o===void 0?void 0:(i=o.roles)===null||i===void 0?void 0:i.includes(O.er.ADMINISTRATOR);var $=(l=k.y.current_user)===null||l===void 0?void 0:(c=l.roles)===null||c===void 0?void 0:c.includes(O.er.TUTOR_INSTRUCTOR);var K=(0,u.useState)(null),X=th(K,2),tt=X[0],et=X[1];var rt=I.filter((function(t){return V||$&&H||t.name==="droip"}));var nt=C&&rt.length>0;var ot=nt&&D.name!=="classic";var it=function t(){if(!lh){q({component:sn,props:{image:ro,image2x:eo}})}else if(!ch){q({component:Xn,props:{image:ro,image2x:eo}})}else{q({component:nn,isMagicAi:true,props:{title:(0,a.__)("AI Studio","tutor"),icon:(0,n.tZ)(d.Z,{name:"magicAiColorize",width:24,height:24}),characters:1e3,field:f,fieldState:p,is_html:true}});j===null||j===void 0?void 0:j()}};var at=(0,n.tZ)("div",{css:ph.editorLabel},(0,n.tZ)("span",{css:ph.labelWithAi},s,(0,n.tZ)(S.Z,{when:_},(0,n.tZ)("button",{type:"button",css:ph.aiButton,onClick:it},(0,n.tZ)(d.Z,{name:"magicAiColorize",width:32,height:32})))),(0,n.tZ)("div",{css:ph.editorsButtonWrapper},(0,n.tZ)("span",null,(0,a.__)("Edit with","tutor")),(0,n.tZ)("div",{css:ph.customEditorButtons},(0,n.tZ)(ue,{each:rt},(function(t){return(0,n.tZ)(Pt.Z,{key:t.name,content:t.label,delay:200},(0,n.tZ)("button",{type:"button",css:ph.customEditorButton,disabled:tt===t.name,onClick:Xv($v().mark((function e(){return $v().wrap((function e(r){while(1)switch(r.prev=r.next){case 0:r.prev=0;et(t.name);r.next=4;return F===null||F===void 0?void 0:F(t);case 4:window.location.href=t.link;r.next=10;break;case 7:r.prev=7;r.t0=r["catch"](0);console.error(r.t0);case 10:r.prev=10;et(null);return r.finish(10);case 13:case"end":return r.stop()}}),e,null,[[0,7,10,13]])})))},(0,n.tZ)(S.Z,{when:tt===t.name},(0,n.tZ)(Z.fz,null)),(0,n.tZ)(d.Z,{name:uh[t.name],height:24,width:24})))})))));return(0,n.tZ)(Lt,{label:nt?at:s,field:f,fieldState:p,disabled:v,readOnly:b,placeholder:g,helpText:m,isMagicAi:J,generateWithAi:!nt&&_,onClickAiButton:it,replaceEntireLabel:nt},(function(){var t;if(y){return(0,n.tZ)("div",{css:At.i.flexCenter()},(0,n.tZ)(Z.ZP,{size:20,color:h.Jv.icon["default"]}))}return(0,n.tZ)("div",{css:ph.wrapper({isOverlayVisible:ot})},(0,n.tZ)(S.Z,{when:ot},(0,n.tZ)(sh,{editorUsed:D,onBackToWPEditorClick:B,onCustomEditorButtonClick:F})),(0,n.tZ)(Qv,{value:(t=f.value)!==null&&t!==void 0?t:"",onChange:function t(e){f.onChange(e);if(w){w(e)}},isMinimal:P,autoFocus:N,onFullScreenChange:z,readonly:b,min_height:R,max_height:U,toolbar1:G,toolbar2:Q}))}))};const fh=dh;var ph={wrapper:function t(e){var r=e.isOverlayVisible,o=r===void 0?false:r;return(0,n.iv)("position:relative;",o&&(0,n.iv)("overflow:hidden;border-radius:",h.E0[6],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},editorLabel:true?{name:"1u6jws0",styles:"display:flex;width:100%;align-items:center;justify-content:space-between"}:0,aiButton:(0,n.iv)(At.i.resetButton,";",At.i.flexCenter(),";width:32px;height:32px;border-radius:",h.E0[4],";:disabled{cursor:not-allowed;}&:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";}"+(true?"":0),true?"":0),labelWithAi:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[4],";"+(true?"":0),true?"":0),editorsButtonWrapper:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[8],";color:",h.Jv.text.hints,";"+(true?"":0),true?"":0),customEditorButtons:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[4],";"+(true?"":0),true?"":0),customEditorButton:(0,n.iv)(At.i.resetButton," display:flex;align-items:center;justify-content:center;position:relative;border-radius:",h.E0.circle,";&:focus-visible{outline:2px solid ",h.Jv.stroke.brand,";outline-offset:1px;}"+(true?"":0),true?"":0),editorOverlay:(0,n.iv)("position:absolute;height:100%;width:100%;",At.i.flexCenter(),";gap:",h.W0[8],";background-color:",fi()(h.Jv.background.modal,.6),";border-radius:",h.E0[6],";z-index:",h.W5.positive,";backdrop-filter:blur(8px);"+(true?"":0),true?"":0),editWithButton:(0,n.iv)("background:",h.Jv.action.secondary["default"],";color:",h.Jv.text.primary,";box-shadow:inset 0 -1px 0 0 ",fi()("#1112133D",.24),",0 1px 0 0 ",fi()("#1112133D",.8),";"+(true?"":0),true?"":0)};function vh(t){"@babel/helpers - typeof";return vh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vh(t)}function hh(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */hh=function e(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n=Object.defineProperty||function(t,e,r){t[e]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{l({},"")}catch(t){l=function t(e,r,n){return e[r]=n}}function c(t,e,r,o){var i=e&&e.prototype instanceof f?e:f,a=Object.create(i.prototype),u=new S(o||[]);return n(a,"_invoke",{value:x(t,r,u)}),a}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=c;var d={};function f(){}function p(){}function v(){}var h={};l(h,i,(function(){return this}));var b=Object.getPrototypeOf,y=b&&b(b(k([])));y&&y!==e&&r.call(y,i)&&(h=y);var g=v.prototype=f.prototype=Object.create(h);function m(t){["next","throw","return"].forEach((function(e){l(t,e,(function(t){return this._invoke(e,t)}))}))}function w(t,e){function o(n,i,a,u){var l=s(t[n],t,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==vh(d)&&r.call(d,"__await")?e.resolve(d.__await).then((function(t){o("next",t,a,u)}),(function(t){o("throw",t,a,u)})):e.resolve(d).then((function(t){c.value=t,a(c)}),(function(t){return o("throw",t,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function t(r,n){function a(){return new e((function(t,e){o(r,n,t,e)}))}return i=i?i.then(a,a):a()}})}function x(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=_(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(t,e,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function _(t,e){var r=e.method,n=t.iterator[r];if(undefined===n)return e.delegate=null,"throw"===r&&t.iterator["return"]&&(e.method="return",e.arg=undefined,_(t,e),"throw"===e.method)||"return"!==r&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,t.iterator,e.arg);if("throw"===o.type)return e.method="throw",e.arg=o.arg,e.delegate=null,d;var i=o.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=undefined),e.delegate=null,d):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,d)}function Z(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function O(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(Z,this),this.reset(!0)}function k(t){if(t){var e=t[i];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=undefined,e.done=!0,e};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(g,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===p||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,l(t,u,"GeneratorFunction")),t.prototype=Object.create(g),t},t.awrap=function(t){return{__await:t}},m(w.prototype),l(w.prototype,a,(function(){return this})),t.AsyncIterator=w,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},m(g),l(g,u,"Generator"),l(g,i,(function(){return this})),l(g,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},t.values=k,S.prototype={constructor:S,reset:function t(e){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!e)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function t(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function t(e){if(this.done)throw e;var n=this;function o(t,r){return u.type="throw",u.arg=e,n.next=t,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function t(e,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=e,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function t(e,r){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&r&&(this.next=r),d},finish:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function t(e){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function t(e,r,n){return this.delegate={iterator:k(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},t}function bh(t,e,r,n,o,i,a){try{var u=t[i](a);var l=u.value}catch(t){r(t);return}if(u.done){e(l)}else{Promise.resolve(l).then(n,o)}}function yh(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){bh(i,n,o,a,u,"next",t)}function u(t){bh(i,n,o,a,u,"throw",t)}a(undefined)}))}}function gh(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function mh(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?gh(Object(r),!0).forEach((function(e){wh(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):gh(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function wh(t,e,r){e=xh(e);if(e in t){Object.defineProperty(t,e,{value:r,enumerable:true,configurable:true,writable:true})}else{t[e]=r}return t}function xh(t){var e=_h(t,"string");return vh(e)==="symbol"?e:String(e)}function _h(t,e){if(vh(t)!=="object"||t===null)return t;var r=t[Symbol.toPrimitive];if(r!==undefined){var n=r.call(t,e||"default");if(vh(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(t)}function Zh(){Zh=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){t[n]=r[n]}}}return t};return Zh.apply(this,arguments)}function Oh(t,e){return Ch(t)||Eh(t,e)||kh(t,e)||Sh()}function Sh(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function kh(t,e){if(!t)return;if(typeof t==="string")return jh(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if(r==="Object"&&t.constructor)r=t.constructor.name;if(r==="Map"||r==="Set")return Array.from(t);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return jh(t,e)}function jh(t,e){if(e==null||e>t.length)e=t.length;for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Eh(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Ch(t){if(Array.isArray(t))return t}var Ah=(0,Vd.X)();var Ph=false;var Wh=function t(){var e;var r=(0,c.Gc)();var l=(0,o.NL)();var s=(0,dv.f)();var d=l.getQueryData(["CourseBundle",Ah]);var f=Wp();var p=(0,u.useState)(false),v=Oh(p,2),h=v[0],b=v[1];var g=(0,i.y)({queryKey:["CourseBundle",Ah]});var m=!!k.y.tutor_pro_url;var w=((e=k.y.settings)===null||e===void 0?void 0:e.chatgpt_enable)==="on";var x=r.watch("post_status");var _=r.watch("editor_used");return(0,n.tZ)("div",{css:Lh.wrapper},(0,n.tZ)("div",{css:Lh.mainForm({isWpEditorFullScreen:h})},(0,n.tZ)("div",{css:Lh.fieldsWrapper},(0,n.tZ)("div",{css:Lh.titleAndSlug},(0,n.tZ)(c.Qr,{name:"post_title",control:r.control,render:function t(e){return(0,n.tZ)(_o,Zh({},e,{label:(0,a.__)("Title","tutor-pro"),placeholder:(0,a.__)("ex. Learn Photoshop CS6 from scratch","tutor-pro"),isClearable:true,generateWithAi:!m||w,loading:!!g&&!e.field.value,onChange:function t(e){if(x==="draft"&&!Ph){r.setValue("post_name",(0,y.k6)(String(e)),{shouldValidate:true,shouldDirty:true})}}}))}}),(0,n.tZ)(c.Qr,{name:"post_name",control:r.control,render:function t(e){return(0,n.tZ)(Tv,Zh({},e,{label:(0,a.__)("Bundle URL","tutor-pro"),baseURL:"".concat(k.y.home_url,"/course-bundle"),onChange:function t(){return Ph=true}}))}})),(0,n.tZ)(c.Qr,{name:"post_content",control:r.control,render:function t(e){return(0,n.tZ)(fh,Zh({},e,{label:(0,a.__)("Description","tutor-pro"),loading:!!g&&!e.field.value,max_height:280,hasCustomEditorSupport:true,editorUsed:_,editors:d===null||d===void 0?void 0:d.editors,generateWithAi:!m||w,onFullScreenChange:function t(e){b(e)},onCustomEditorButtonClick:function t(){return r.handleSubmit((function(t){var e=(0,dv.ZO)(t);return s.mutateAsync(mh(mh({ID:Ah},e),{},{post_status:(0,y.Xl)(r.getValues("post_status"),r.getValues("visibility"))}))}))()},onBackToWPEditorClick:function(){var t=yh(hh().mark((function t(e){return hh().wrap((function t(n){while(1)switch(n.prev=n.next){case 0:return n.abrupt("return",f.mutateAsync({courseId:Ah,builder:e}).then((function(t){r.setValue("editor_used",{name:"classic",label:(0,a.__)("Classic Editor","tutor-pro"),link:""});return t})));case 1:case"end":return n.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()}))}}),(0,n.tZ)(Ov,{loading:!!g&&!r.getValues("details.courses").length}),(0,n.tZ)("div",{css:Lh.additionalFields},(0,n.tZ)(c.Qr,{name:"course_benefits",control:r.control,render:function t(e){return(0,n.tZ)(ne,Zh({},e,{label:(0,a.__)("What Will I Learn?","tutor-pro"),placeholder:(0,a.__)("Define the key takeaways from this course (list one benefit per line)","tutor-pro"),rows:3,enableResize:true}))}})))),(0,n.tZ)(Jf,null))};const Ih=Wh;var Lh={wrapper:(0,n.iv)("display:grid;grid-template-columns:1fr 338px;gap:",h.W0[32],";width:100%;",h.Uo.smallTablet,"{grid-template-columns:1fr;gap:0;}"+(true?"":0),true?"":0),mainForm:function t(e){var r=e.isWpEditorFullScreen;return(0,n.iv)("padding-block:",h.W0[32]," ",h.W0[24],";align-self:start;top:",h.J9,"px;position:sticky;",r&&(0,n.iv)("z-index:",h.W5.header+1,";"+(true?"":0),true?"":0)," ",h.Uo.smallTablet,"{padding-top:",h.W0[16],";position:unset;}"+(true?"":0),true?"":0)},fieldsWrapper:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[24],";"+(true?"":0),true?"":0),titleAndSlug:(0,n.iv)("display:flex;flex-direction:column;gap:",h.W0[8],";"+(true?"":0),true?"":0),sidebar:(0,n.iv)("border-left:1px solid ",h.Jv.stroke.divider,";min-height:calc(100vh - ",h.J9,"px);padding-left:",h.W0[32],";padding-block:",h.W0[24],";display:flex;flex-direction:column;gap:",h.W0[16],";"+(true?"":0),true?"":0),priceRadioGroup:(0,n.iv)("display:flex;align-items:center;gap:",h.W0[36],";"+(true?"":0),true?"":0),coursePriceWrapper:(0,n.iv)("display:flex;align-items:flex-start;gap:",h.W0[16],";"+(true?"":0),true?"":0),statusAndDate:(0,n.iv)(At.i.display.flex("column"),";gap:",h.W0[4],";"+(true?"":0),true?"":0),updatedOn:(0,n.iv)(b.c.caption(),";color:",h.Jv.text.hints,";"+(true?"":0),true?"":0),additionalFields:(0,n.iv)("padding:",h.W0[12]," ",h.W0[20],";background-color:",h.Jv.background.white,";border:1px solid ",h.Jv.stroke.divider,";border-radius:",h.E0.card,";"+(true?"":0),true?"":0)}}}]);