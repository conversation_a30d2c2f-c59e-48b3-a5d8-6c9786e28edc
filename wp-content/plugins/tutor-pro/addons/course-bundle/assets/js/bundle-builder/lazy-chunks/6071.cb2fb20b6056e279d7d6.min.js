"use strict";(self["webpackChunktutor_pro"]=self["webpackChunktutor_pro"]||[]).push([[6071],{9752:(e,t,n)=>{n.d(t,{LB:()=>Dt,y9:()=>Ut,g4:()=>ve,Lg:()=>we,we:()=>Se,pE:()=>N,ey:()=>O,VK:()=>Y,_8:()=>_,hI:()=>q,Cj:()=>_t,O1:()=>Mt,Zj:()=>Ot,VT:()=>y,Dy:()=>w});var r=n(7363);var o=n.n(r);var s=n(1533);var i=n(4285);const a={display:"none"};function c(e){let{id:t,value:n}=e;return o().createElement("div",{id:t,style:a},n)}function l(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;const s={position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"};return o().createElement("div",{id:t,style:s,role:"status","aria-live":r,"aria-atomic":true},n)}function u(){const[e,t]=(0,r.useState)("");const n=(0,r.useCallback)((e=>{if(e!=null){t(e)}}),[]);return{announce:n,announcement:e}}const d=(0,r.createContext)(null);function f(e){const t=(0,r.useContext)(d);(0,r.useEffect)((()=>{if(!t){throw new Error("useDndMonitor must be used within a children of <DndContext>")}const n=t(e);return n}),[e,t])}function h(){const[e]=(0,r.useState)((()=>new Set));const t=(0,r.useCallback)((t=>{e.add(t);return()=>e.delete(t)}),[e]);const n=(0,r.useCallback)((t=>{let{type:n,event:r}=t;e.forEach((e=>{var t;return(t=e[n])==null?void 0:t.call(e,r)}))}),[e]);return[n,t]}const p={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "};const g={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;if(n){return"Draggable item "+t.id+" was moved over droppable area "+n.id+"."}return"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;if(n){return"Draggable item "+t.id+" was dropped over droppable area "+n.id}return"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function m(e){let{announcements:t=g,container:n,hiddenTextDescribedById:a,screenReaderInstructions:d=p}=e;const{announce:h,announcement:m}=u();const v=(0,i.Ld)("DndLiveRegion");const[b,y]=(0,r.useState)(false);(0,r.useEffect)((()=>{y(true)}),[]);f((0,r.useMemo)((()=>({onDragStart(e){let{active:n}=e;h(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;if(t.onDragMove){h(t.onDragMove({active:n,over:r}))}},onDragOver(e){let{active:n,over:r}=e;h(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;h(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;h(t.onDragCancel({active:n,over:r}))}})),[h,t]));if(!b){return null}const w=o().createElement(o().Fragment,null,o().createElement(c,{id:a,value:d.draggable}),o().createElement(l,{id:v,announcement:m}));return n?(0,s.createPortal)(w,n):w}var v;(function(e){e["DragStart"]="dragStart";e["DragMove"]="dragMove";e["DragEnd"]="dragEnd";e["DragCancel"]="dragCancel";e["DragOver"]="dragOver";e["RegisterDroppable"]="registerDroppable";e["SetDroppableDisabled"]="setDroppableDisabled";e["UnregisterDroppable"]="unregisterDroppable"})(v||(v={}));function b(){}function y(e,t){return(0,r.useMemo)((()=>({sensor:e,options:t!=null?t:{}})),[e,t])}function w(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++){t[n]=arguments[n]}return(0,r.useMemo)((()=>[...t].filter((e=>e!=null))),[...t])}const x=Object.freeze({x:0,y:0});function D(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function k(e,t){const n=(0,i.DC)(e);if(!n){return"0 0"}const r={x:(n.x-t.left)/t.width*100,y:(n.y-t.top)/t.height*100};return r.x+"% "+r.y+"%"}function C(e,t){let{data:{value:n}}=e;let{data:{value:r}}=t;return n-r}function S(e,t){let{data:{value:n}}=e;let{data:{value:r}}=t;return r-n}function M(e){let{left:t,top:n,height:r,width:o}=e;return[{x:t,y:n},{x:t+o,y:n},{x:t,y:n+r},{x:t+o,y:n+r}]}function _(e,t){if(!e||e.length===0){return null}const[n]=e;return t?n[t]:n}function E(e,t,n){if(t===void 0){t=e.left}if(n===void 0){n=e.top}return{x:t+e.width*.5,y:n+e.height*.5}}const N=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=E(t,t.left,t.top);const s=[];for(const e of r){const{id:t}=e;const r=n.get(t);if(r){const n=D(E(r),o);s.push({id:t,data:{droppableContainer:e,value:n}})}}return s.sort(C)};const O=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=M(t);const s=[];for(const e of r){const{id:t}=e;const r=n.get(t);if(r){const n=M(r);const i=o.reduce(((e,t,r)=>e+D(n[r],t)),0);const a=Number((i/4).toFixed(4));s.push({id:t,data:{droppableContainer:e,value:a}})}}return s.sort(C)};function R(e,t){const n=Math.max(t.top,e.top);const r=Math.max(t.left,e.left);const o=Math.min(t.left+t.width,e.left+e.width);const s=Math.min(t.top+t.height,e.top+e.height);const i=o-r;const a=s-n;if(r<o&&n<s){const n=t.width*t.height;const r=e.width*e.height;const o=i*a;const s=o/(n+r-o);return Number(s.toFixed(4))}return 0}const T=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=[];for(const e of r){const{id:r}=e;const s=n.get(r);if(s){const n=R(s,t);if(n>0){o.push({id:r,data:{droppableContainer:e,value:n}})}}}return o.sort(S)};function A(e,t){const{top:n,left:r,bottom:o,right:s}=t;return n<=e.y&&e.y<=o&&r<=e.x&&e.x<=s}const I=e=>{let{droppableContainers:t,droppableRects:n,pointerCoordinates:r}=e;if(!r){return[]}const o=[];for(const e of t){const{id:t}=e;const s=n.get(t);if(s&&A(r,s)){const n=M(s);const i=n.reduce(((e,t)=>e+D(r,t)),0);const a=Number((i/4).toFixed(4));o.push({id:t,data:{droppableContainer:e,value:a}})}}return o.sort(C)};function F(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}function L(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:x}function P(e){return function t(n){for(var r=arguments.length,o=new Array(r>1?r-1:0),s=1;s<r;s++){o[s-1]=arguments[s]}return o.reduce(((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x})),{...n})}}const W=P(1);function j(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}else if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}function B(e,t,n){const r=j(t);if(!r){return e}const{scaleX:o,scaleY:s,x:i,y:a}=r;const c=e.left-i-(1-o)*parseFloat(n);const l=e.top-a-(1-s)*parseFloat(n.slice(n.indexOf(" ")+1));const u=o?e.width/o:e.width;const d=s?e.height/s:e.height;return{width:u,height:d,top:l,right:c+u,bottom:l+d,left:c}}const z={ignoreTransform:false};function Y(e,t){if(t===void 0){t=z}let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:t,transformOrigin:r}=(0,i.Jj)(e).getComputedStyle(e);if(t){n=B(n,t,r)}}const{top:r,left:o,width:s,height:a,bottom:c,right:l}=n;return{top:r,left:o,width:s,height:a,bottom:c,right:l}}function V(e){return Y(e,{ignoreTransform:true})}function U(e){const t=e.innerWidth;const n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}function H(e,t){if(t===void 0){t=(0,i.Jj)(e).getComputedStyle(e)}return t.position==="fixed"}function X(e,t){if(t===void 0){t=(0,i.Jj)(e).getComputedStyle(e)}const n=/(auto|scroll|overlay)/;const r=["overflow","overflowX","overflowY"];return r.some((e=>{const r=t[e];return typeof r==="string"?n.test(r):false}))}function q(e,t){const n=[];function r(o){if(t!=null&&n.length>=t){return n}if(!o){return n}if((0,i.qk)(o)&&o.scrollingElement!=null&&!n.includes(o.scrollingElement)){n.push(o.scrollingElement);return n}if(!(0,i.Re)(o)||(0,i.vZ)(o)){return n}if(n.includes(o)){return n}const s=(0,i.Jj)(e).getComputedStyle(o);if(o!==e){if(X(o,s)){n.push(o)}}if(H(o,s)){return n}return r(o.parentNode)}if(!e){return n}return r(e)}function Z(e){const[t]=q(e,1);return t!=null?t:null}function J(e){if(!i.Nq||!e){return null}if((0,i.FJ)(e)){return e}if(!(0,i.UG)(e)){return null}if((0,i.qk)(e)||e===(0,i.r3)(e).scrollingElement){return window}if((0,i.Re)(e)){return e}return null}function G(e){if((0,i.FJ)(e)){return e.scrollX}return e.scrollLeft}function $(e){if((0,i.FJ)(e)){return e.scrollY}return e.scrollTop}function K(e){return{x:G(e),y:$(e)}}var Q;(function(e){e[e["Forward"]=1]="Forward";e[e["Backward"]=-1]="Backward"})(Q||(Q={}));function ee(e){if(!i.Nq||!e){return false}return e===document.scrollingElement}function te(e){const t={x:0,y:0};const n=ee(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth};const r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height};const o=e.scrollTop<=t.y;const s=e.scrollLeft<=t.x;const i=e.scrollTop>=r.y;const a=e.scrollLeft>=r.x;return{isTop:o,isLeft:s,isBottom:i,isRight:a,maxScroll:r,minScroll:t}}const ne={x:.2,y:.2};function re(e,t,n,r,o){let{top:s,left:i,right:a,bottom:c}=n;if(r===void 0){r=10}if(o===void 0){o=ne}const{isTop:l,isBottom:u,isLeft:d,isRight:f}=te(e);const h={x:0,y:0};const p={x:0,y:0};const g={height:t.height*o.y,width:t.width*o.x};if(!l&&s<=t.top+g.height){h.y=Q.Backward;p.y=r*Math.abs((t.top+g.height-s)/g.height)}else if(!u&&c>=t.bottom-g.height){h.y=Q.Forward;p.y=r*Math.abs((t.bottom-g.height-c)/g.height)}if(!f&&a>=t.right-g.width){h.x=Q.Forward;p.x=r*Math.abs((t.right-g.width-a)/g.width)}else if(!d&&i<=t.left+g.width){h.x=Q.Backward;p.x=r*Math.abs((t.left+g.width-i)/g.width)}return{direction:h,speed:p}}function oe(e){if(e===document.scrollingElement){const{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}const{top:t,left:n,right:r,bottom:o}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:o,width:e.clientWidth,height:e.clientHeight}}function se(e){return e.reduce(((e,t)=>(0,i.IH)(e,K(t))),x)}function ie(e){return e.reduce(((e,t)=>e+G(t)),0)}function ae(e){return e.reduce(((e,t)=>e+$(t)),0)}function ce(e,t){if(t===void 0){t=Y}if(!e){return}const{top:n,left:r,bottom:o,right:s}=t(e);const i=Z(e);if(!i){return}if(o<=0||s<=0||n>=window.innerHeight||r>=window.innerWidth){e.scrollIntoView({block:"center",inline:"center"})}}const le=[["x",["left","right"],ie],["y",["top","bottom"],ae]];class ue{constructor(e,t){this.rect=void 0;this.width=void 0;this.height=void 0;this.top=void 0;this.bottom=void 0;this.right=void 0;this.left=void 0;const n=q(t);const r=se(n);this.rect={...e};this.width=e.width;this.height=e.height;for(const[e,t,o]of le){for(const s of t){Object.defineProperty(this,s,{get:()=>{const t=o(n);const i=r[e]-t;return this.rect[s]+i},enumerable:true})}}Object.defineProperty(this,"rect",{enumerable:false})}}class de{constructor(e){this.target=void 0;this.listeners=[];this.removeAll=()=>{this.listeners.forEach((e=>{var t;return(t=this.target)==null?void 0:t.removeEventListener(...e)}))};this.target=e}add(e,t,n){var r;(r=this.target)==null?void 0:r.addEventListener(e,t,n);this.listeners.push([e,t,n])}}function fe(e){const{EventTarget:t}=(0,i.Jj)(e);return e instanceof t?e:(0,i.r3)(e)}function he(e,t){const n=Math.abs(e.x);const r=Math.abs(e.y);if(typeof t==="number"){return Math.sqrt(n**2+r**2)>t}if("x"in t&&"y"in t){return n>t.x&&r>t.y}if("x"in t){return n>t.x}if("y"in t){return r>t.y}return false}var pe;(function(e){e["Click"]="click";e["DragStart"]="dragstart";e["Keydown"]="keydown";e["ContextMenu"]="contextmenu";e["Resize"]="resize";e["SelectionChange"]="selectionchange";e["VisibilityChange"]="visibilitychange"})(pe||(pe={}));function ge(e){e.preventDefault()}function me(e){e.stopPropagation()}var ve;(function(e){e["Space"]="Space";e["Down"]="ArrowDown";e["Right"]="ArrowRight";e["Left"]="ArrowLeft";e["Up"]="ArrowUp";e["Esc"]="Escape";e["Enter"]="Enter";e["Tab"]="Tab"})(ve||(ve={}));const be={start:[ve.Space,ve.Enter],cancel:[ve.Esc],end:[ve.Space,ve.Enter,ve.Tab]};const ye=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case ve.Right:return{...n,x:n.x+25};case ve.Left:return{...n,x:n.x-25};case ve.Down:return{...n,y:n.y+25};case ve.Up:return{...n,y:n.y-25}}return undefined};class we{constructor(e){this.props=void 0;this.autoScrollEnabled=false;this.referenceCoordinates=void 0;this.listeners=void 0;this.windowListeners=void 0;this.props=e;const{event:{target:t}}=e;this.props=e;this.listeners=new de((0,i.r3)(t));this.windowListeners=new de((0,i.Jj)(t));this.handleKeyDown=this.handleKeyDown.bind(this);this.handleCancel=this.handleCancel.bind(this);this.attach()}attach(){this.handleStart();this.windowListeners.add(pe.Resize,this.handleCancel);this.windowListeners.add(pe.VisibilityChange,this.handleCancel);setTimeout((()=>this.listeners.add(pe.Keydown,this.handleKeyDown)))}handleStart(){const{activeNode:e,onStart:t}=this.props;const n=e.node.current;if(n){ce(n)}t(x)}handleKeyDown(e){if((0,i.vd)(e)){const{active:t,context:n,options:r}=this.props;const{keyboardCodes:o=be,coordinateGetter:s=ye,scrollBehavior:a="smooth"}=r;const{code:c}=e;if(o.end.includes(c)){this.handleEnd(e);return}if(o.cancel.includes(c)){this.handleCancel(e);return}const{collisionRect:l}=n.current;const u=l?{x:l.left,y:l.top}:x;if(!this.referenceCoordinates){this.referenceCoordinates=u}const d=s(e,{active:t,context:n.current,currentCoordinates:u});if(d){const t=(0,i.$X)(d,u);const r={x:0,y:0};const{scrollableAncestors:o}=n.current;for(const n of o){const o=e.code;const{isTop:s,isRight:i,isLeft:c,isBottom:l,maxScroll:u,minScroll:f}=te(n);const h=oe(n);const p={x:Math.min(o===ve.Right?h.right-h.width/2:h.right,Math.max(o===ve.Right?h.left:h.left+h.width/2,d.x)),y:Math.min(o===ve.Down?h.bottom-h.height/2:h.bottom,Math.max(o===ve.Down?h.top:h.top+h.height/2,d.y))};const g=o===ve.Right&&!i||o===ve.Left&&!c;const m=o===ve.Down&&!l||o===ve.Up&&!s;if(g&&p.x!==d.x){const e=n.scrollLeft+t.x;const s=o===ve.Right&&e<=u.x||o===ve.Left&&e>=f.x;if(s&&!t.y){n.scrollTo({left:e,behavior:a});return}if(s){r.x=n.scrollLeft-e}else{r.x=o===ve.Right?n.scrollLeft-u.x:n.scrollLeft-f.x}if(r.x){n.scrollBy({left:-r.x,behavior:a})}break}else if(m&&p.y!==d.y){const e=n.scrollTop+t.y;const s=o===ve.Down&&e<=u.y||o===ve.Up&&e>=f.y;if(s&&!t.x){n.scrollTo({top:e,behavior:a});return}if(s){r.y=n.scrollTop-e}else{r.y=o===ve.Down?n.scrollTop-u.y:n.scrollTop-f.y}if(r.y){n.scrollBy({top:-r.y,behavior:a})}break}}this.handleMove(e,(0,i.IH)((0,i.$X)(d,this.referenceCoordinates),r))}}}handleMove(e,t){const{onMove:n}=this.props;e.preventDefault();n(t)}handleEnd(e){const{onEnd:t}=this.props;e.preventDefault();this.detach();t()}handleCancel(e){const{onCancel:t}=this.props;e.preventDefault();this.detach();t()}detach(){this.listeners.removeAll();this.windowListeners.removeAll()}}we.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=be,onActivation:o}=t;let{active:s}=n;const{code:i}=e.nativeEvent;if(r.start.includes(i)){const t=s.activatorNode.current;if(t&&e.target!==t){return false}e.preventDefault();o==null?void 0:o({event:e.nativeEvent});return true}return false}}];function xe(e){return Boolean(e&&"distance"in e)}function De(e){return Boolean(e&&"delay"in e)}class ke{constructor(e,t,n){var r;if(n===void 0){n=fe(e.event.target)}this.props=void 0;this.events=void 0;this.autoScrollEnabled=true;this.document=void 0;this.activated=false;this.initialCoordinates=void 0;this.timeoutId=null;this.listeners=void 0;this.documentListeners=void 0;this.windowListeners=void 0;this.props=e;this.events=t;const{event:o}=e;const{target:s}=o;this.props=e;this.events=t;this.document=(0,i.r3)(s);this.documentListeners=new de(this.document);this.listeners=new de(n);this.windowListeners=new de((0,i.Jj)(s));this.initialCoordinates=(r=(0,i.DC)(o))!=null?r:x;this.handleStart=this.handleStart.bind(this);this.handleMove=this.handleMove.bind(this);this.handleEnd=this.handleEnd.bind(this);this.handleCancel=this.handleCancel.bind(this);this.handleKeydown=this.handleKeydown.bind(this);this.removeTextSelection=this.removeTextSelection.bind(this);this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;this.listeners.add(e.move.name,this.handleMove,{passive:false});this.listeners.add(e.end.name,this.handleEnd);if(e.cancel){this.listeners.add(e.cancel.name,this.handleCancel)}this.windowListeners.add(pe.Resize,this.handleCancel);this.windowListeners.add(pe.DragStart,ge);this.windowListeners.add(pe.VisibilityChange,this.handleCancel);this.windowListeners.add(pe.ContextMenu,ge);this.documentListeners.add(pe.Keydown,this.handleKeydown);if(t){if(n!=null&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options})){return this.handleStart()}if(De(t)){this.timeoutId=setTimeout(this.handleStart,t.delay);this.handlePending(t);return}if(xe(t)){this.handlePending(t);return}}this.handleStart()}detach(){this.listeners.removeAll();this.windowListeners.removeAll();setTimeout(this.documentListeners.removeAll,50);if(this.timeoutId!==null){clearTimeout(this.timeoutId);this.timeoutId=null}}handlePending(e,t){const{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){const{initialCoordinates:e}=this;const{onStart:t}=this.props;if(e){this.activated=true;this.documentListeners.add(pe.Click,me,{capture:true});this.removeTextSelection();this.documentListeners.add(pe.SelectionChange,this.removeTextSelection);t(e)}}handleMove(e){var t;const{activated:n,initialCoordinates:r,props:o}=this;const{onMove:s,options:{activationConstraint:a}}=o;if(!r){return}const c=(t=(0,i.DC)(e))!=null?t:x;const l=(0,i.$X)(r,c);if(!n&&a){if(xe(a)){if(a.tolerance!=null&&he(l,a.tolerance)){return this.handleCancel()}if(he(l,a.distance)){return this.handleStart()}}if(De(a)){if(he(l,a.tolerance)){return this.handleCancel()}}this.handlePending(a,l);return}if(e.cancelable){e.preventDefault()}s(c)}handleEnd(){const{onAbort:e,onEnd:t}=this.props;this.detach();if(!this.activated){e(this.props.active)}t()}handleCancel(){const{onAbort:e,onCancel:t}=this.props;this.detach();if(!this.activated){e(this.props.active)}t()}handleKeydown(e){if(e.code===ve.Esc){this.handleCancel()}}removeTextSelection(){var e;(e=this.document.getSelection())==null?void 0:e.removeAllRanges()}}const Ce={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class Se extends ke{constructor(e){const{event:t}=e;const n=(0,i.r3)(t.target);super(e,Ce,n)}}Se.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e;let{onActivation:r}=t;if(!n.isPrimary||n.button!==0){return false}r==null?void 0:r({event:n});return true}}];const Me={move:{name:"mousemove"},end:{name:"mouseup"}};var _e;(function(e){e[e["RightClick"]=2]="RightClick"})(_e||(_e={}));class Ee extends ke{constructor(e){super(e,Me,(0,i.r3)(e.event.target))}}Ee.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e;let{onActivation:r}=t;if(n.button===_e.RightClick){return false}r==null?void 0:r({event:n});return true}}];const Ne={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class Oe extends ke{constructor(e){super(e,Ne)}static setup(){window.addEventListener(Ne.move.name,e,{capture:false,passive:false});return function t(){window.removeEventListener(Ne.move.name,e)};function e(){}}}Oe.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e;let{onActivation:r}=t;const{touches:o}=n;if(o.length>1){return false}r==null?void 0:r({event:n});return true}}];var Re;(function(e){e[e["Pointer"]=0]="Pointer";e[e["DraggableRect"]=1]="DraggableRect"})(Re||(Re={}));var Te;(function(e){e[e["TreeOrder"]=0]="TreeOrder";e[e["ReversedTreeOrder"]=1]="ReversedTreeOrder"})(Te||(Te={}));function Ae(e){let{acceleration:t,activator:n=Re.Pointer,canScroll:o,draggingRect:s,enabled:a,interval:c=5,order:l=Te.TreeOrder,pointerCoordinates:u,scrollableAncestors:d,scrollableAncestorRects:f,delta:h,threshold:p}=e;const g=Fe({delta:h,disabled:!a});const[m,v]=(0,i.Yz)();const b=(0,r.useRef)({x:0,y:0});const y=(0,r.useRef)({x:0,y:0});const w=(0,r.useMemo)((()=>{switch(n){case Re.Pointer:return u?{top:u.y,bottom:u.y,left:u.x,right:u.x}:null;case Re.DraggableRect:return s}}),[n,s,u]);const x=(0,r.useRef)(null);const D=(0,r.useCallback)((()=>{const e=x.current;if(!e){return}const t=b.current.x*y.current.x;const n=b.current.y*y.current.y;e.scrollBy(t,n)}),[]);const k=(0,r.useMemo)((()=>l===Te.TreeOrder?[...d].reverse():d),[l,d]);(0,r.useEffect)((()=>{if(!a||!d.length||!w){v();return}for(const e of k){if((o==null?void 0:o(e))===false){continue}const n=d.indexOf(e);const r=f[n];if(!r){continue}const{direction:s,speed:i}=re(e,r,w,t,p);for(const e of["x","y"]){if(!g[e][s[e]]){i[e]=0;s[e]=0}}if(i.x>0||i.y>0){v();x.current=e;m(D,c);b.current=i;y.current=s;return}}b.current={x:0,y:0};y.current={x:0,y:0};v()}),[t,D,o,v,a,c,JSON.stringify(w),JSON.stringify(g),m,d,k,f,JSON.stringify(p)])}const Ie={x:{[Q.Backward]:false,[Q.Forward]:false},y:{[Q.Backward]:false,[Q.Forward]:false}};function Fe(e){let{delta:t,disabled:n}=e;const r=(0,i.D9)(t);return(0,i.Gj)((e=>{if(n||!r||!e){return Ie}const o={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[Q.Backward]:e.x[Q.Backward]||o.x===-1,[Q.Forward]:e.x[Q.Forward]||o.x===1},y:{[Q.Backward]:e.y[Q.Backward]||o.y===-1,[Q.Forward]:e.y[Q.Forward]||o.y===1}}}),[n,t,r])}function Le(e,t){const n=t!=null?e.get(t):undefined;const r=n?n.node.current:null;return(0,i.Gj)((e=>{var n;if(t==null){return null}return(n=r!=null?r:e)!=null?n:null}),[r,t])}function Pe(e,t){return(0,r.useMemo)((()=>e.reduce(((e,n)=>{const{sensor:r}=n;const o=r.activators.map((e=>({eventName:e.eventName,handler:t(e.handler,n)})));return[...e,...o]}),[])),[e,t])}var We;(function(e){e[e["Always"]=0]="Always";e[e["BeforeDragging"]=1]="BeforeDragging";e[e["WhileDragging"]=2]="WhileDragging"})(We||(We={}));var je;(function(e){e["Optimized"]="optimized"})(je||(je={}));const Be=new Map;function ze(e,t){let{dragging:n,dependencies:o,config:s}=t;const[a,c]=(0,r.useState)(null);const{frequency:l,measure:u,strategy:d}=s;const f=(0,r.useRef)(e);const h=b();const p=(0,i.Ey)(h);const g=(0,r.useCallback)((function(e){if(e===void 0){e=[]}if(p.current){return}c((t=>{if(t===null){return e}return t.concat(e.filter((e=>!t.includes(e))))}))}),[p]);const m=(0,r.useRef)(null);const v=(0,i.Gj)((t=>{if(h&&!n){return Be}if(!t||t===Be||f.current!==e||a!=null){const t=new Map;for(let n of e){if(!n){continue}if(a&&a.length>0&&!a.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}const e=n.node.current;const r=e?new ue(u(e),e):null;n.rect.current=r;if(r){t.set(n.id,r)}}return t}return t}),[e,a,n,h,u]);(0,r.useEffect)((()=>{f.current=e}),[e]);(0,r.useEffect)((()=>{if(h){return}g()}),[n,h]);(0,r.useEffect)((()=>{if(a&&a.length>0){c(null)}}),[JSON.stringify(a)]);(0,r.useEffect)((()=>{if(h||typeof l!=="number"||m.current!==null){return}m.current=setTimeout((()=>{g();m.current=null}),l)}),[l,h,g,...o]);return{droppableRects:v,measureDroppableContainers:g,measuringScheduled:a!=null};function b(){switch(d){case We.Always:return false;case We.BeforeDragging:return n;default:return!n}}}function Ye(e,t){return(0,i.Gj)((n=>{if(!e){return null}if(n){return n}return typeof t==="function"?t(e):e}),[t,e])}function Ve(e,t){return Ye(e,t)}function Ue(e){let{callback:t,disabled:n}=e;const o=(0,i.zX)(t);const s=(0,r.useMemo)((()=>{if(n||typeof window==="undefined"||typeof window.MutationObserver==="undefined"){return undefined}const{MutationObserver:e}=window;return new e(o)}),[o,n]);(0,r.useEffect)((()=>()=>s==null?void 0:s.disconnect()),[s]);return s}function He(e){let{callback:t,disabled:n}=e;const o=(0,i.zX)(t);const s=(0,r.useMemo)((()=>{if(n||typeof window==="undefined"||typeof window.ResizeObserver==="undefined"){return undefined}const{ResizeObserver:e}=window;return new e(o)}),[n]);(0,r.useEffect)((()=>()=>s==null?void 0:s.disconnect()),[s]);return s}function Xe(e){return new ue(Y(e),e)}function qe(e,t,n){if(t===void 0){t=Xe}const[o,s]=(0,r.useState)(null);function a(){s((r=>{if(!e){return null}if(e.isConnected===false){var o;return(o=r!=null?r:n)!=null?o:null}const s=t(e);if(JSON.stringify(r)===JSON.stringify(s)){return r}return s}))}const c=Ue({callback(t){if(!e){return}for(const n of t){const{type:t,target:r}=n;if(t==="childList"&&r instanceof HTMLElement&&r.contains(e)){a();break}}}});const l=He({callback:a});(0,i.LI)((()=>{a();if(e){l==null?void 0:l.observe(e);c==null?void 0:c.observe(document.body,{childList:true,subtree:true})}else{l==null?void 0:l.disconnect();c==null?void 0:c.disconnect()}}),[e]);return o}function Ze(e){const t=Ye(e);return L(e,t)}const Je=[];function Ge(e){const t=(0,r.useRef)(e);const n=(0,i.Gj)((n=>{if(!e){return Je}if(n&&n!==Je&&e&&t.current&&e.parentNode===t.current.parentNode){return n}return q(e)}),[e]);(0,r.useEffect)((()=>{t.current=e}),[e]);return n}function $e(e){const[t,n]=(0,r.useState)(null);const o=(0,r.useRef)(e);const s=(0,r.useCallback)((e=>{const t=J(e.target);if(!t){return}n((e=>{if(!e){return null}e.set(t,K(t));return new Map(e)}))}),[]);(0,r.useEffect)((()=>{const t=o.current;if(e!==t){r(t);const i=e.map((e=>{const t=J(e);if(t){t.addEventListener("scroll",s,{passive:true});return[t,K(t)]}return null})).filter((e=>e!=null));n(i.length?new Map(i):null);o.current=e}return()=>{r(e);r(t)};function r(e){e.forEach((e=>{const t=J(e);t==null?void 0:t.removeEventListener("scroll",s)}))}}),[s,e]);return(0,r.useMemo)((()=>{if(e.length){return t?Array.from(t.values()).reduce(((e,t)=>(0,i.IH)(e,t)),x):se(e)}return x}),[e,t])}function Ke(e,t){if(t===void 0){t=[]}const n=(0,r.useRef)(null);(0,r.useEffect)((()=>{n.current=null}),t);(0,r.useEffect)((()=>{const t=e!==x;if(t&&!n.current){n.current=e}if(!t&&n.current){n.current=null}}),[e]);return n.current?(0,i.$X)(e,n.current):x}function Qe(e){(0,r.useEffect)((()=>{if(!i.Nq){return}const t=e.map((e=>{let{sensor:t}=e;return t.setup==null?void 0:t.setup()}));return()=>{for(const e of t){e==null?void 0:e()}}}),e.map((e=>{let{sensor:t}=e;return t})))}function et(e,t){return(0,r.useMemo)((()=>e.reduce(((e,n)=>{let{eventName:r,handler:o}=n;e[r]=e=>{o(e,t)};return e}),{})),[e,t])}function tt(e){return(0,r.useMemo)((()=>e?U(e):null),[e])}const nt=[];function rt(e,t){if(t===void 0){t=Y}const[n]=e;const o=tt(n?(0,i.Jj)(n):null);const[s,a]=(0,r.useState)(nt);function c(){a((()=>{if(!e.length){return nt}return e.map((e=>ee(e)?o:new ue(t(e),e)))}))}const l=He({callback:c});(0,i.LI)((()=>{l==null?void 0:l.disconnect();c();e.forEach((e=>l==null?void 0:l.observe(e)))}),[e]);return s}function ot(e){if(!e){return null}if(e.children.length>1){return e}const t=e.children[0];return(0,i.Re)(t)?t:e}function st(e){let{measure:t}=e;const[n,o]=(0,r.useState)(null);const s=(0,r.useCallback)((e=>{for(const{target:n}of e){if((0,i.Re)(n)){o((e=>{const r=t(n);return e?{...e,width:r.width,height:r.height}:r}));break}}}),[t]);const a=He({callback:s});const c=(0,r.useCallback)((e=>{const n=ot(e);a==null?void 0:a.disconnect();if(n){a==null?void 0:a.observe(n)}o(n?t(n):null)}),[t,a]);const[l,u]=(0,i.wm)(c);return(0,r.useMemo)((()=>({nodeRef:l,rect:n,setRef:u})),[n,l,u])}const it=[{sensor:Se,options:{}},{sensor:we,options:{}}];const at={current:{}};const ct={draggable:{measure:V},droppable:{measure:V,strategy:We.WhileDragging,frequency:je.Optimized},dragOverlay:{measure:Y}};class lt extends Map{get(e){var t;return e!=null?(t=super.get(e))!=null?t:undefined:undefined}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter((e=>{let{disabled:t}=e;return!t}))}getNodeFor(e){var t,n;return(t=(n=this.get(e))==null?void 0:n.node.current)!=null?t:undefined}}const ut={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new lt,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:b},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:ct,measureDroppableContainers:b,windowRect:null,measuringScheduled:false};const dt={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:b,draggableNodes:new Map,over:null,measureDroppableContainers:b};const ft=(0,r.createContext)(dt);const ht=(0,r.createContext)(ut);function pt(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new lt}}}function gt(e,t){switch(t.type){case v.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case v.DragMove:if(e.draggable.active==null){return e}return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case v.DragEnd:case v.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case v.RegisterDroppable:{const{element:n}=t;const{id:r}=n;const o=new lt(e.droppable.containers);o.set(r,n);return{...e,droppable:{...e.droppable,containers:o}}}case v.SetDroppableDisabled:{const{id:n,key:r,disabled:o}=t;const s=e.droppable.containers.get(n);if(!s||r!==s.key){return e}const i=new lt(e.droppable.containers);i.set(n,{...s,disabled:o});return{...e,droppable:{...e.droppable,containers:i}}}case v.UnregisterDroppable:{const{id:n,key:r}=t;const o=e.droppable.containers.get(n);if(!o||r!==o.key){return e}const s=new lt(e.droppable.containers);s.delete(n);return{...e,droppable:{...e.droppable,containers:s}}}default:{return e}}}function mt(e){let{disabled:t}=e;const{active:n,activatorEvent:o,draggableNodes:s}=(0,r.useContext)(ft);const a=(0,i.D9)(o);const c=(0,i.D9)(n==null?void 0:n.id);(0,r.useEffect)((()=>{if(t){return}if(!o&&a&&c!=null){if(!(0,i.vd)(a)){return}if(document.activeElement===a.target){return}const e=s.get(c);if(!e){return}const{activatorNode:t,node:n}=e;if(!t.current&&!n.current){return}requestAnimationFrame((()=>{for(const e of[t.current,n.current]){if(!e){continue}const t=(0,i.so)(e);if(t){t.focus();break}}}))}}),[o,t,s,c,a]);return null}function vt(e,t){let{transform:n,...r}=t;return e!=null&&e.length?e.reduce(((e,t)=>t({transform:e,...r})),n):n}function bt(e){return(0,r.useMemo)((()=>({draggable:{...ct.draggable,...e==null?void 0:e.draggable},droppable:{...ct.droppable,...e==null?void 0:e.droppable},dragOverlay:{...ct.dragOverlay,...e==null?void 0:e.dragOverlay}})),[e==null?void 0:e.draggable,e==null?void 0:e.droppable,e==null?void 0:e.dragOverlay])}function yt(e){let{activeNode:t,measure:n,initialRect:o,config:s=true}=e;const a=(0,r.useRef)(false);const{x:c,y:l}=typeof s==="boolean"?{x:s,y:s}:s;(0,i.LI)((()=>{const e=!c&&!l;if(e||!t){a.current=false;return}if(a.current||!o){return}const r=t==null?void 0:t.node.current;if(!r||r.isConnected===false){return}const s=n(r);const i=L(s,o);if(!c){i.x=0}if(!l){i.y=0}a.current=true;if(Math.abs(i.x)>0||Math.abs(i.y)>0){const e=Z(r);if(e){e.scrollBy({top:i.y,left:i.x})}}}),[t,c,l,o,n])}const wt=(0,r.createContext)({...x,scaleX:1,scaleY:1});var xt;(function(e){e[e["Uninitialized"]=0]="Uninitialized";e[e["Initializing"]=1]="Initializing";e[e["Initialized"]=2]="Initialized"})(xt||(xt={}));const Dt=(0,r.memo)((function e(t){var n,a,c,l;let{id:u,accessibility:f,autoScroll:p=true,children:g,sensors:b=it,collisionDetection:y=T,measuring:w,modifiers:x,...D}=t;const k=(0,r.useReducer)(gt,undefined,pt);const[C,S]=k;const[M,E]=h();const[N,O]=(0,r.useState)(xt.Uninitialized);const R=N===xt.Initialized;const{draggable:{active:A,nodes:I,translate:L},droppable:{containers:P}}=C;const j=A!=null?I.get(A):null;const B=(0,r.useRef)({initial:null,translated:null});const z=(0,r.useMemo)((()=>{var e;return A!=null?{id:A,data:(e=j==null?void 0:j.data)!=null?e:at,rect:B}:null}),[A,j]);const Y=(0,r.useRef)(null);const[V,U]=(0,r.useState)(null);const[H,X]=(0,r.useState)(null);const q=(0,i.Ey)(D,Object.values(D));const Z=(0,i.Ld)("DndDescribedBy",u);const J=(0,r.useMemo)((()=>P.getEnabled()),[P]);const G=bt(w);const{droppableRects:$,measureDroppableContainers:K,measuringScheduled:Q}=ze(J,{dragging:R,dependencies:[L.x,L.y],config:G.droppable});const ee=Le(I,A);const te=(0,r.useMemo)((()=>H?(0,i.DC)(H):null),[H]);const ne=We();const re=Ve(ee,G.draggable.measure);yt({activeNode:A!=null?I.get(A):null,config:ne.layoutShiftCompensation,initialRect:re,measure:G.draggable.measure});const oe=qe(ee,G.draggable.measure,re);const se=qe(ee?ee.parentElement:null);const ie=(0,r.useRef)({activatorEvent:null,active:null,activeNode:ee,collisionRect:null,collisions:null,droppableRects:$,draggableNodes:I,draggingNode:null,draggingNodeRect:null,droppableContainers:P,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null});const ae=P.getNodeFor((n=ie.current.over)==null?void 0:n.id);const ce=st({measure:G.dragOverlay.measure});const le=(a=ce.nodeRef.current)!=null?a:ee;const ue=R?(c=ce.rect)!=null?c:oe:null;const de=Boolean(ce.nodeRef.current&&ce.rect);const fe=Ze(de?null:oe);const he=tt(le?(0,i.Jj)(le):null);const pe=Ge(R?ae!=null?ae:ee:null);const ge=rt(pe);const me=vt(x,{transform:{x:L.x-fe.x,y:L.y-fe.y,scaleX:1,scaleY:1},activatorEvent:H,active:z,activeNodeRect:oe,containerNodeRect:se,draggingNodeRect:ue,over:ie.current.over,overlayNodeRect:ce.rect,scrollableAncestors:pe,scrollableAncestorRects:ge,windowRect:he});const ve=te?(0,i.IH)(te,L):null;const be=$e(pe);const ye=Ke(be);const we=Ke(be,[oe]);const xe=(0,i.IH)(me,ye);const De=ue?W(ue,me):null;const ke=z&&De?y({active:z,collisionRect:De,droppableRects:$,droppableContainers:J,pointerCoordinates:ve}):null;const Ce=_(ke,"id");const[Se,Me]=(0,r.useState)(null);const _e=de?me:(0,i.IH)(me,we);const Ee=F(_e,(l=Se==null?void 0:Se.rect)!=null?l:null,oe);const Ne=(0,r.useRef)(null);const Oe=(0,r.useCallback)(((e,t)=>{let{sensor:n,options:r}=t;if(Y.current==null){return}const o=I.get(Y.current);if(!o){return}const i=e.nativeEvent;const a=new n({active:Y.current,activeNode:o,event:i,options:r,context:ie,onAbort(e){const t=I.get(e);if(!t){return}const{onDragAbort:n}=q.current;const r={id:e};n==null?void 0:n(r);M({type:"onDragAbort",event:r})},onPending(e,t,n,r){const o=I.get(e);if(!o){return}const{onDragPending:s}=q.current;const i={id:e,constraint:t,initialCoordinates:n,offset:r};s==null?void 0:s(i);M({type:"onDragPending",event:i})},onStart(e){const t=Y.current;if(t==null){return}const n=I.get(t);if(!n){return}const{onDragStart:r}=q.current;const o={activatorEvent:i,active:{id:t,data:n.data,rect:B}};(0,s.unstable_batchedUpdates)((()=>{r==null?void 0:r(o);O(xt.Initializing);S({type:v.DragStart,initialCoordinates:e,active:t});M({type:"onDragStart",event:o});U(Ne.current);X(i)}))},onMove(e){S({type:v.DragMove,coordinates:e})},onEnd:c(v.DragEnd),onCancel:c(v.DragCancel)});Ne.current=a;function c(e){return async function t(){const{active:n,collisions:r,over:o,scrollAdjustedTranslate:a}=ie.current;let c=null;if(n&&a){const{cancelDrop:t}=q.current;c={activatorEvent:i,active:n,collisions:r,delta:a,over:o};if(e===v.DragEnd&&typeof t==="function"){const n=await Promise.resolve(t(c));if(n){e=v.DragCancel}}}Y.current=null;(0,s.unstable_batchedUpdates)((()=>{S({type:e});O(xt.Uninitialized);Me(null);U(null);X(null);Ne.current=null;const t=e===v.DragEnd?"onDragEnd":"onDragCancel";if(c){const e=q.current[t];e==null?void 0:e(c);M({type:t,event:c})}}))}}}),[I]);const Re=(0,r.useCallback)(((e,t)=>(n,r)=>{const o=n.nativeEvent;const s=I.get(r);if(Y.current!==null||!s||o.dndKit||o.defaultPrevented){return}const i={active:s};const a=e(n,t.options,i);if(a===true){o.dndKit={capturedBy:t.sensor};Y.current=r;Oe(n,t)}}),[I,Oe]);const Te=Pe(b,Re);Qe(b);(0,i.LI)((()=>{if(oe&&N===xt.Initializing){O(xt.Initialized)}}),[oe,N]);(0,r.useEffect)((()=>{const{onDragMove:e}=q.current;const{active:t,activatorEvent:n,collisions:r,over:o}=ie.current;if(!t||!n){return}const i={active:t,activatorEvent:n,collisions:r,delta:{x:xe.x,y:xe.y},over:o};(0,s.unstable_batchedUpdates)((()=>{e==null?void 0:e(i);M({type:"onDragMove",event:i})}))}),[xe.x,xe.y]);(0,r.useEffect)((()=>{const{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:o}=ie.current;if(!e||Y.current==null||!t||!o){return}const{onDragOver:i}=q.current;const a=r.get(Ce);const c=a&&a.rect.current?{id:a.id,rect:a.rect.current,data:a.data,disabled:a.disabled}:null;const l={active:e,activatorEvent:t,collisions:n,delta:{x:o.x,y:o.y},over:c};(0,s.unstable_batchedUpdates)((()=>{Me(c);i==null?void 0:i(l);M({type:"onDragOver",event:l})}))}),[Ce]);(0,i.LI)((()=>{ie.current={activatorEvent:H,active:z,activeNode:ee,collisionRect:De,collisions:ke,droppableRects:$,draggableNodes:I,draggingNode:le,draggingNodeRect:ue,droppableContainers:P,over:Se,scrollableAncestors:pe,scrollAdjustedTranslate:xe};B.current={initial:ue,translated:De}}),[z,ee,ke,De,I,le,ue,$,P,Se,pe,xe]);Ae({...ne,delta:L,draggingRect:De,pointerCoordinates:ve,scrollableAncestors:pe,scrollableAncestorRects:ge});const Ie=(0,r.useMemo)((()=>{const e={active:z,activeNode:ee,activeNodeRect:oe,activatorEvent:H,collisions:ke,containerNodeRect:se,dragOverlay:ce,draggableNodes:I,droppableContainers:P,droppableRects:$,over:Se,measureDroppableContainers:K,scrollableAncestors:pe,scrollableAncestorRects:ge,measuringConfiguration:G,measuringScheduled:Q,windowRect:he};return e}),[z,ee,oe,H,ke,se,ce,I,P,$,Se,K,pe,ge,G,Q,he]);const Fe=(0,r.useMemo)((()=>{const e={activatorEvent:H,activators:Te,active:z,activeNodeRect:oe,ariaDescribedById:{draggable:Z},dispatch:S,draggableNodes:I,over:Se,measureDroppableContainers:K};return e}),[H,Te,z,oe,S,Z,I,Se,K]);return o().createElement(d.Provider,{value:E},o().createElement(ft.Provider,{value:Fe},o().createElement(ht.Provider,{value:Ie},o().createElement(wt.Provider,{value:Ee},g)),o().createElement(mt,{disabled:(f==null?void 0:f.restoreFocus)===false})),o().createElement(m,{...f,hiddenTextDescribedById:Z}));function We(){const e=(V==null?void 0:V.autoScrollEnabled)===false;const t=typeof p==="object"?p.enabled===false:p===false;const n=R&&!e&&!t;if(typeof p==="object"){return{...p,enabled:n}}return{enabled:n}}}));const kt=(0,r.createContext)(null);const Ct="button";const St="Draggable";function Mt(e){let{id:t,data:n,disabled:o=false,attributes:s}=e;const a=(0,i.Ld)(St);const{activators:c,activatorEvent:l,active:u,activeNodeRect:d,ariaDescribedById:f,draggableNodes:h,over:p}=(0,r.useContext)(ft);const{role:g=Ct,roleDescription:m="draggable",tabIndex:v=0}=s!=null?s:{};const b=(u==null?void 0:u.id)===t;const y=(0,r.useContext)(b?wt:kt);const[w,x]=(0,i.wm)();const[D,k]=(0,i.wm)();const C=et(c,t);const S=(0,i.Ey)(n);(0,i.LI)((()=>{h.set(t,{id:t,key:a,node:w,activatorNode:D,data:S});return()=>{const e=h.get(t);if(e&&e.key===a){h.delete(t)}}}),[h,t]);const M=(0,r.useMemo)((()=>({role:g,tabIndex:v,"aria-disabled":o,"aria-pressed":b&&g===Ct?true:undefined,"aria-roledescription":m,"aria-describedby":f.draggable})),[o,g,v,b,m,f.draggable]);return{active:u,activatorEvent:l,activeNodeRect:d,attributes:M,isDragging:b,listeners:o?undefined:C,node:w,over:p,setNodeRef:x,setActivatorNodeRef:k,transform:y}}function _t(){return(0,r.useContext)(ht)}const Et="Droppable";const Nt={timeout:25};function Ot(e){let{data:t,disabled:n=false,id:o,resizeObserverConfig:s}=e;const a=(0,i.Ld)(Et);const{active:c,dispatch:l,over:u,measureDroppableContainers:d}=(0,r.useContext)(ft);const f=(0,r.useRef)({disabled:n});const h=(0,r.useRef)(false);const p=(0,r.useRef)(null);const g=(0,r.useRef)(null);const{disabled:m,updateMeasurementsFor:b,timeout:y}={...Nt,...s};const w=(0,i.Ey)(b!=null?b:o);const x=(0,r.useCallback)((()=>{if(!h.current){h.current=true;return}if(g.current!=null){clearTimeout(g.current)}g.current=setTimeout((()=>{d(Array.isArray(w.current)?w.current:[w.current]);g.current=null}),y)}),[y]);const D=He({callback:x,disabled:m||!c});const k=(0,r.useCallback)(((e,t)=>{if(!D){return}if(t){D.unobserve(t);h.current=false}if(e){D.observe(e)}}),[D]);const[C,S]=(0,i.wm)(k);const M=(0,i.Ey)(t);(0,r.useEffect)((()=>{if(!D||!C.current){return}D.disconnect();h.current=false;D.observe(C.current)}),[C,D]);(0,r.useEffect)((()=>{l({type:v.RegisterDroppable,element:{id:o,key:a,disabled:n,node:C,rect:p,data:M}});return()=>l({type:v.UnregisterDroppable,key:a,id:o})}),[o]);(0,r.useEffect)((()=>{if(n!==f.current.disabled){l({type:v.SetDroppableDisabled,id:o,key:a,disabled:n});f.current.disabled=n}}),[o,a,n,l]);return{active:c,rect:p,isOver:(u==null?void 0:u.id)===o,node:C,over:u,setNodeRef:S}}function Rt(e){let{animation:t,children:n}=e;const[s,a]=(0,r.useState)(null);const[c,l]=(0,r.useState)(null);const u=(0,i.D9)(n);if(!n&&!s&&u){a(u)}(0,i.LI)((()=>{if(!c){return}const e=s==null?void 0:s.key;const n=s==null?void 0:s.props.id;if(e==null||n==null){a(null);return}Promise.resolve(t(n,c)).then((()=>{a(null)}))}),[t,s,c]);return o().createElement(o().Fragment,null,n,s?(0,r.cloneElement)(s,{ref:l}):null)}const Tt={x:0,y:0,scaleX:1,scaleY:1};function At(e){let{children:t}=e;return o().createElement(ft.Provider,{value:dt},o().createElement(wt.Provider,{value:Tt},t))}const It={position:"fixed",touchAction:"none"};const Ft=e=>{const t=(0,i.vd)(e);return t?"transform 250ms ease":undefined};const Lt=(0,r.forwardRef)(((e,t)=>{let{as:n,activatorEvent:r,adjustScale:s,children:a,className:c,rect:l,style:u,transform:d,transition:f=Ft}=e;if(!l){return null}const h=s?d:{...d,scaleX:1,scaleY:1};const p={...It,width:l.width,height:l.height,top:l.top,left:l.left,transform:i.ux.Transform.toString(h),transformOrigin:s&&r?k(r,l):undefined,transition:typeof f==="function"?f(r):f,...u};return o().createElement(n,{className:c,style:p,ref:t},a)}));const Pt=e=>t=>{let{active:n,dragOverlay:r}=t;const o={};const{styles:s,className:i}=e;if(s!=null&&s.active){for(const[e,t]of Object.entries(s.active)){if(t===undefined){continue}o[e]=n.node.style.getPropertyValue(e);n.node.style.setProperty(e,t)}}if(s!=null&&s.dragOverlay){for(const[e,t]of Object.entries(s.dragOverlay)){if(t===undefined){continue}r.node.style.setProperty(e,t)}}if(i!=null&&i.active){n.node.classList.add(i.active)}if(i!=null&&i.dragOverlay){r.node.classList.add(i.dragOverlay)}return function e(){for(const[e,t]of Object.entries(o)){n.node.style.setProperty(e,t)}if(i!=null&&i.active){n.node.classList.remove(i.active)}}};const Wt=e=>{let{transform:{initial:t,final:n}}=e;return[{transform:i.ux.Transform.toString(t)},{transform:i.ux.Transform.toString(n)}]};const jt={duration:250,easing:"ease",keyframes:Wt,sideEffects:Pt({styles:{active:{opacity:"0"}}})};function Bt(e){let{config:t,draggableNodes:n,droppableContainers:r,measuringConfiguration:o}=e;return(0,i.zX)(((e,s)=>{if(t===null){return}const a=n.get(e);if(!a){return}const c=a.node.current;if(!c){return}const l=ot(s);if(!l){return}const{transform:u}=(0,i.Jj)(s).getComputedStyle(s);const d=j(u);if(!d){return}const f=typeof t==="function"?t:zt(t);ce(c,o.draggable.measure);return f({active:{id:e,data:a.data,node:c,rect:o.draggable.measure(c)},draggableNodes:n,dragOverlay:{node:s,rect:o.dragOverlay.measure(l)},droppableContainers:r,measuringConfiguration:o,transform:d})}))}function zt(e){const{duration:t,easing:n,sideEffects:r,keyframes:o}={...jt,...e};return e=>{let{active:s,dragOverlay:i,transform:a,...c}=e;if(!t){return}const l={x:i.rect.left-s.rect.left,y:i.rect.top-s.rect.top};const u={scaleX:a.scaleX!==1?s.rect.width*a.scaleX/i.rect.width:1,scaleY:a.scaleY!==1?s.rect.height*a.scaleY/i.rect.height:1};const d={x:a.x-l.x,y:a.y-l.y,...u};const f=o({...c,active:s,dragOverlay:i,transform:{initial:a,final:d}});const[h]=f;const p=f[f.length-1];if(JSON.stringify(h)===JSON.stringify(p)){return}const g=r==null?void 0:r({active:s,dragOverlay:i,...c});const m=i.node.animate(f,{duration:t,easing:n,fill:"forwards"});return new Promise((e=>{m.onfinish=()=>{g==null?void 0:g();e()}}))}}let Yt=0;function Vt(e){return(0,r.useMemo)((()=>{if(e==null){return}Yt++;return Yt}),[e])}const Ut=o().memo((e=>{let{adjustScale:t=false,children:n,dropAnimation:s,style:i,transition:a,modifiers:c,wrapperElement:l="div",className:u,zIndex:d=999}=e;const{activatorEvent:f,active:h,activeNodeRect:p,containerNodeRect:g,draggableNodes:m,droppableContainers:v,dragOverlay:b,over:y,measuringConfiguration:w,scrollableAncestors:x,scrollableAncestorRects:D,windowRect:k}=_t();const C=(0,r.useContext)(wt);const S=Vt(h==null?void 0:h.id);const M=vt(c,{activatorEvent:f,active:h,activeNodeRect:p,containerNodeRect:g,draggingNodeRect:b.rect,over:y,overlayNodeRect:b.rect,scrollableAncestors:x,scrollableAncestorRects:D,transform:C,windowRect:k});const _=Ye(p);const E=Bt({config:s,draggableNodes:m,droppableContainers:v,measuringConfiguration:w});const N=_?b.setRef:undefined;return o().createElement(At,null,o().createElement(Rt,{animation:E},h&&S?o().createElement(Lt,{key:S,id:h.id,ref:N,as:l,activatorEvent:f,adjustScale:t,className:u,transition:a,rect:_,style:{zIndex:d,...i},transform:M},n):null))}))},2339:(e,t,n)=>{n.d(t,{hg:()=>u});var r=n(4285);function o(e){return t=>{let{transform:n}=t;return{...n,x:Math.ceil(n.x/e)*e,y:Math.ceil(n.y/e)*e}}}const s=e=>{let{transform:t}=e;return{...t,y:0}};function i(e,t,n){const r={...e};if(t.top+e.y<=n.top){r.y=n.top-t.top}else if(t.bottom+e.y>=n.top+n.height){r.y=n.top+n.height-t.bottom}if(t.left+e.x<=n.left){r.x=n.left-t.left}else if(t.right+e.x>=n.left+n.width){r.x=n.left+n.width-t.right}return r}const a=e=>{let{containerNodeRect:t,draggingNodeRect:n,transform:r}=e;if(!n||!t){return r}return i(r,n,t)};const c=e=>{let{draggingNodeRect:t,transform:n,scrollableAncestorRects:r}=e;const o=r[0];if(!t||!o){return n}return i(n,t,o)};const l=e=>{let{transform:t}=e;return{...t,x:0}};const u=e=>{let{transform:t,draggingNodeRect:n,windowRect:r}=e;if(!n||!r){return t}return i(t,n,r)};const d=e=>{let{activatorEvent:t,draggingNodeRect:n,transform:r}=e;if(n&&t){const e=getEventCoordinates(t);if(!e){return r}const o=e.x-n.left;const s=e.y-n.top;return{...r,x:r.x+o-n.width/2,y:r.y+s-n.height/2}}return r}},5587:(e,t,n)=>{n.d(t,{Fo:()=>k,is:()=>F,nB:()=>R,qw:()=>y});var r=n(7363);var o=n.n(r);var s=n(9752);var i=n(4285);function a(e,t,n){const r=e.slice();r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]);return r}function c(e,t,n){const r=e.slice();r[t]=e[n];r[n]=e[t];return r}function l(e,t){return e.reduce(((e,n,r)=>{const o=t.get(n);if(o){e[r]=o}return e}),Array(e.length))}function u(e){return e!==null&&e>=0}function d(e,t){if(e===t){return true}if(e.length!==t.length){return false}for(let n=0;n<e.length;n++){if(e[n]!==t[n]){return false}}return true}function f(e){if(typeof e==="boolean"){return{draggable:e,droppable:e}}return e}const h={scaleX:1,scaleY:1};const p=e=>{var t;let{rects:n,activeNodeRect:r,activeIndex:o,overIndex:s,index:i}=e;const a=(t=n[o])!=null?t:r;if(!a){return null}const c=g(n,i,o);if(i===o){const e=n[s];if(!e){return null}return{x:o<s?e.left+e.width-(a.left+a.width):e.left-a.left,y:0,...h}}if(i>o&&i<=s){return{x:-a.width-c,y:0,...h}}if(i<o&&i>=s){return{x:a.width+c,y:0,...h}}return{x:0,y:0,...h}};function g(e,t,n){const r=e[t];const o=e[t-1];const s=e[t+1];if(!r||!o&&!s){return 0}if(n<t){return o?r.left-(o.left+o.width):s.left-(r.left+r.width)}return s?s.left-(r.left+r.width):r.left-(o.left+o.width)}const m=e=>{let{rects:t,activeIndex:n,overIndex:r,index:o}=e;const s=a(t,r,n);const i=t[o];const c=s[o];if(!c||!i){return null}return{x:c.left-i.left,y:c.top-i.top,scaleX:c.width/i.width,scaleY:c.height/i.height}};const v=e=>{let{activeIndex:t,index:n,rects:r,overIndex:o}=e;let s;let i;if(n===t){s=r[n];i=r[o]}if(n===o){s=r[n];i=r[t]}if(!i||!s){return null}return{x:i.left-s.left,y:i.top-s.top,scaleX:i.width/s.width,scaleY:i.height/s.height}};const b={scaleX:1,scaleY:1};const y=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:o,rects:s,overIndex:i}=e;const a=(t=s[n])!=null?t:r;if(!a){return null}if(o===n){const e=s[i];if(!e){return null}return{x:0,y:n<i?e.top+e.height-(a.top+a.height):e.top-a.top,...b}}const c=w(s,o,n);if(o>n&&o<=i){return{x:0,y:-a.height-c,...b}}if(o<n&&o>=i){return{x:0,y:a.height+c,...b}}return{x:0,y:0,...b}};function w(e,t,n){const r=e[t];const o=e[t-1];const s=e[t+1];if(!r){return 0}if(n<t){return o?r.top-(o.top+o.height):s?s.top-(r.top+r.height):0}return s?s.top-(r.top+r.height):o?r.top-(o.top+o.height):0}const x="Sortable";const D=o().createContext({activeIndex:-1,containerId:x,disableTransforms:false,items:[],overIndex:-1,useDragOverlay:false,sortedRects:[],strategy:m,disabled:{draggable:false,droppable:false}});function k(e){let{children:t,id:n,items:a,strategy:c=m,disabled:u=false}=e;const{active:h,dragOverlay:p,droppableRects:g,over:v,measureDroppableContainers:b}=(0,s.Cj)();const y=(0,i.Ld)(x,n);const w=Boolean(p.rect!==null);const k=(0,r.useMemo)((()=>a.map((e=>typeof e==="object"&&"id"in e?e.id:e))),[a]);const C=h!=null;const S=h?k.indexOf(h.id):-1;const M=v?k.indexOf(v.id):-1;const _=(0,r.useRef)(k);const E=!d(k,_.current);const N=M!==-1&&S===-1||E;const O=f(u);(0,i.LI)((()=>{if(E&&C){b(k)}}),[E,k,C,b]);(0,r.useEffect)((()=>{_.current=k}),[k]);const R=(0,r.useMemo)((()=>({activeIndex:S,containerId:y,disabled:O,disableTransforms:N,items:k,overIndex:M,useDragOverlay:w,sortedRects:l(k,g),strategy:c})),[S,y,O.draggable,O.droppable,N,k,M,g,w,c]);return o().createElement(D.Provider,{value:R},t)}const C=e=>{let{id:t,items:n,activeIndex:r,overIndex:o}=e;return a(n,r,o).indexOf(t)};const S=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:o,items:s,newIndex:i,previousItems:a,previousContainerId:c,transition:l}=e;if(!l||!r){return false}if(a!==s&&o===i){return false}if(n){return true}return i!==o&&t===c};const M={duration:200,easing:"ease"};const _="transform";const E=i.ux.Transition.toString({property:_,duration:0,easing:"linear"});const N={roleDescription:"sortable"};function O(e){let{disabled:t,index:n,node:o,rect:a}=e;const[c,l]=(0,r.useState)(null);const u=(0,r.useRef)(n);(0,i.LI)((()=>{if(!t&&n!==u.current&&o.current){const e=a.current;if(e){const t=(0,s.VK)(o.current,{ignoreTransform:true});const n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};if(n.x||n.y){l(n)}}}if(n!==u.current){u.current=n}}),[t,n,o,a]);(0,r.useEffect)((()=>{if(c){l(null)}}),[c]);return c}function R(e){let{animateLayoutChanges:t=S,attributes:n,disabled:o,data:a,getNewIndex:c=C,id:l,strategy:d,resizeObserverConfig:f,transition:h=M}=e;const{items:p,containerId:g,activeIndex:m,disabled:v,disableTransforms:b,sortedRects:y,overIndex:w,useDragOverlay:x,strategy:k}=(0,r.useContext)(D);const R=T(o,v);const A=p.indexOf(l);const I=(0,r.useMemo)((()=>({sortable:{containerId:g,index:A,items:p},...a})),[g,a,A,p]);const F=(0,r.useMemo)((()=>p.slice(p.indexOf(l))),[p,l]);const{rect:L,node:P,isOver:W,setNodeRef:j}=(0,s.Zj)({id:l,data:I,disabled:R.droppable,resizeObserverConfig:{updateMeasurementsFor:F,...f}});const{active:B,activatorEvent:z,activeNodeRect:Y,attributes:V,setNodeRef:U,listeners:H,isDragging:X,over:q,setActivatorNodeRef:Z,transform:J}=(0,s.O1)({id:l,data:I,attributes:{...N,...n},disabled:R.draggable});const G=(0,i.HB)(j,U);const $=Boolean(B);const K=$&&!b&&u(m)&&u(w);const Q=!x&&X;const ee=Q&&K?J:null;const te=d!=null?d:k;const ne=K?ee!=null?ee:te({rects:y,activeNodeRect:Y,activeIndex:m,overIndex:w,index:A}):null;const re=u(m)&&u(w)?c({id:l,items:p,activeIndex:m,overIndex:w}):A;const oe=B==null?void 0:B.id;const se=(0,r.useRef)({activeId:oe,items:p,newIndex:re,containerId:g});const ie=p!==se.current.items;const ae=t({active:B,containerId:g,isDragging:X,isSorting:$,id:l,index:A,items:p,newIndex:se.current.newIndex,previousItems:se.current.items,previousContainerId:se.current.containerId,transition:h,wasDragging:se.current.activeId!=null});const ce=O({disabled:!ae,index:A,node:P,rect:L});(0,r.useEffect)((()=>{if($&&se.current.newIndex!==re){se.current.newIndex=re}if(g!==se.current.containerId){se.current.containerId=g}if(p!==se.current.items){se.current.items=p}}),[$,re,g,p]);(0,r.useEffect)((()=>{if(oe===se.current.activeId){return}if(oe!=null&&se.current.activeId==null){se.current.activeId=oe;return}const e=setTimeout((()=>{se.current.activeId=oe}),50);return()=>clearTimeout(e)}),[oe]);return{active:B,activeIndex:m,attributes:V,data:I,rect:L,index:A,newIndex:re,items:p,isOver:W,isSorting:$,isDragging:X,listeners:H,node:P,overIndex:w,over:q,setNodeRef:G,setActivatorNodeRef:Z,setDroppableNodeRef:j,setDraggableNodeRef:U,transform:ce!=null?ce:ne,transition:le()};function le(){if(ce||ie&&se.current.newIndex===A){return E}if(Q&&!(0,i.vd)(z)||!h){return undefined}if($||ae){return i.ux.Transition.toString({...h,property:_})}return undefined}}function T(e,t){var n,r;if(typeof e==="boolean"){return{draggable:e,droppable:false}}return{draggable:(n=e==null?void 0:e.draggable)!=null?n:t.draggable,droppable:(r=e==null?void 0:e.droppable)!=null?r:t.droppable}}function A(e){if(!e){return false}const t=e.data.current;if(t&&"sortable"in t&&typeof t.sortable==="object"&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable){return true}return false}const I=[s.g4.Down,s.g4.Right,s.g4.Up,s.g4.Left];const F=(e,t)=>{let{context:{active:n,collisionRect:r,droppableRects:o,droppableContainers:a,over:c,scrollableAncestors:l}}=t;if(I.includes(e.code)){e.preventDefault();if(!n||!r){return}const t=[];a.getEnabled().forEach((n=>{if(!n||n!=null&&n.disabled){return}const i=o.get(n.id);if(!i){return}switch(e.code){case s.g4.Down:if(r.top<i.top){t.push(n)}break;case s.g4.Up:if(r.top>i.top){t.push(n)}break;case s.g4.Left:if(r.left>i.left){t.push(n)}break;case s.g4.Right:if(r.left<i.left){t.push(n)}break}}));const u=(0,s.ey)({active:n,collisionRect:r,droppableRects:o,droppableContainers:t,pointerCoordinates:null});let d=(0,s._8)(u,"id");if(d===(c==null?void 0:c.id)&&u.length>1){d=u[1].id}if(d!=null){const e=a.get(n.id);const t=a.get(d);const c=t?o.get(t.id):null;const u=t==null?void 0:t.node.current;if(u&&c&&e&&t){const n=(0,s.hI)(u);const o=n.some(((e,t)=>l[t]!==e));const a=L(e,t);const d=P(e,t);const f=o||!a?{x:0,y:0}:{x:d?r.width-c.width:0,y:d?r.height-c.height:0};const h={x:c.left,y:c.top};const p=f.x&&f.y?h:(0,i.$X)(h,f);return p}}}return undefined};function L(e,t){if(!A(e)||!A(t)){return false}return e.data.current.sortable.containerId===t.data.current.sortable.containerId}function P(e,t){if(!A(e)||!A(t)){return false}if(!L(e,t)){return false}return e.data.current.sortable.index<t.data.current.sortable.index}},4285:(e,t,n)=>{n.d(t,{$X:()=>S,D9:()=>w,DC:()=>N,Ey:()=>v,FJ:()=>a,Gj:()=>b,HB:()=>s,IH:()=>C,Jj:()=>l,LI:()=>p,Ld:()=>D,Nq:()=>i,Re:()=>d,UG:()=>c,Yz:()=>m,qk:()=>u,r3:()=>h,so:()=>T,ux:()=>O,vZ:()=>f,vd:()=>_,wm:()=>y,zX:()=>g});var r=n(7363);var o=n.n(r);function s(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++){t[n]=arguments[n]}return(0,r.useMemo)((()=>e=>{t.forEach((t=>t(e)))}),t)}const i=typeof window!=="undefined"&&typeof window.document!=="undefined"&&typeof window.document.createElement!=="undefined";function a(e){const t=Object.prototype.toString.call(e);return t==="[object Window]"||t==="[object global]"}function c(e){return"nodeType"in e}function l(e){var t,n;if(!e){return window}if(a(e)){return e}if(!c(e)){return window}return(t=(n=e.ownerDocument)==null?void 0:n.defaultView)!=null?t:window}function u(e){const{Document:t}=l(e);return e instanceof t}function d(e){if(a(e)){return false}return e instanceof l(e).HTMLElement}function f(e){return e instanceof l(e).SVGElement}function h(e){if(!e){return document}if(a(e)){return e.document}if(!c(e)){return document}if(u(e)){return e}if(d(e)||f(e)){return e.ownerDocument}return document}const p=i?r.useLayoutEffect:r.useEffect;function g(e){const t=(0,r.useRef)(e);p((()=>{t.current=e}));return(0,r.useCallback)((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++){n[r]=arguments[r]}return t.current==null?void 0:t.current(...n)}),[])}function m(){const e=(0,r.useRef)(null);const t=(0,r.useCallback)(((t,n)=>{e.current=setInterval(t,n)}),[]);const n=(0,r.useCallback)((()=>{if(e.current!==null){clearInterval(e.current);e.current=null}}),[]);return[t,n]}function v(e,t){if(t===void 0){t=[e]}const n=(0,r.useRef)(e);p((()=>{if(n.current!==e){n.current=e}}),t);return n}function b(e,t){const n=(0,r.useRef)();return(0,r.useMemo)((()=>{const t=e(n.current);n.current=t;return t}),[...t])}function y(e){const t=g(e);const n=(0,r.useRef)(null);const o=(0,r.useCallback)((e=>{if(e!==n.current){t==null?void 0:t(e,n.current)}n.current=e}),[]);return[n,o]}function w(e){const t=(0,r.useRef)();(0,r.useEffect)((()=>{t.current=e}),[e]);return t.current}let x={};function D(e,t){return(0,r.useMemo)((()=>{if(t){return t}const n=x[e]==null?0:x[e]+1;x[e]=n;return e+"-"+n}),[e,t])}function k(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++){r[o-1]=arguments[o]}return r.reduce(((t,n)=>{const r=Object.entries(n);for(const[n,o]of r){const r=t[n];if(r!=null){t[n]=r+e*o}}return t}),{...t})}}const C=k(1);const S=k(-1);function M(e){return"clientX"in e&&"clientY"in e}function _(e){if(!e){return false}const{KeyboardEvent:t}=l(e.target);return t&&e instanceof t}function E(e){if(!e){return false}const{TouchEvent:t}=l(e.target);return t&&e instanceof t}function N(e){if(E(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}if(M(e)){return{x:e.clientX,y:e.clientY}}return null}const O=Object.freeze({Translate:{toString(e){if(!e){return}const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e){return}const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(!e){return}return[O.Translate.toString(e),O.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}});const R="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function T(e){if(e.matches(R)){return e}return e.querySelector(R)}},3954:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(3645);var o=n.n(r);var s=o()((function(e){return e[1]}));s.push([e.id,'/* Variables declaration */\n/* prettier-ignore */\n.rdp-root {\n  --rdp-accent-color: blue; /* The accent color used for selected days and UI elements. */\n  --rdp-accent-background-color: #f0f0ff; /* The accent background color used for selected days and UI elements. */\n\n  --rdp-day-height: 44px; /* The height of the day cells. */\n  --rdp-day-width: 44px; /* The width of the day cells. */\n  \n  --rdp-day_button-border-radius: 100%; /* The border radius of the day cells. */\n  --rdp-day_button-border: 2px solid transparent; /* The border of the day cells. */\n  --rdp-day_button-height: 42px; /* The height of the day cells. */\n  --rdp-day_button-width: 42px; /* The width of the day cells. */\n  \n  --rdp-selected-border: 2px solid var(--rdp-accent-color); /* The border of the selected days. */\n  --rdp-disabled-opacity: 0.5; /* The opacity of the disabled days. */\n  --rdp-outside-opacity: 0.75; /* The opacity of the days outside the current month. */\n  --rdp-today-color: var(--rdp-accent-color); /* The color of the today\'s date. */\n  \n  --rdp-dropdown-gap: 0.5rem;/* The gap between the dropdowns used in the month captons. */\n  \n  --rdp-months-gap: 2rem; /* The gap between the months in the multi-month view. */\n  \n  --rdp-nav_button-disabled-opacity: 0.5; /* The opacity of the disabled navigation buttons. */\n  --rdp-nav_button-height: 2.25rem; /* The height of the navigation buttons. */\n  --rdp-nav_button-width: 2.25rem; /* The width of the navigation buttons. */\n  --rdp-nav-height: 2.75rem; /* The height of the navigation bar. */\n  \n  --rdp-range_middle-background-color: var(--rdp-accent-background-color); /* The color of the background for days in the middle of a range. */\n  --rdp-range_middle-color: inherit;/* The color of the range text. */\n  \n  --rdp-range_start-color: white; /* The color of the range text. */\n  --rdp-range_start-background: linear-gradient(var(--rdp-gradient-direction), transparent 50%, var(--rdp-range_middle-background-color) 50%); /* Used for the background of the start of the selected range. */\n  --rdp-range_start-date-background-color: var(--rdp-accent-color); /* The background color of the date when at the start of the selected range. */\n  \n  --rdp-range_end-background: linear-gradient(var(--rdp-gradient-direction), var(--rdp-range_middle-background-color) 50%, transparent 50%); /* Used for the background of the end of the selected range. */\n  --rdp-range_end-color: white;/* The color of the range text. */\n  --rdp-range_end-date-background-color: var(--rdp-accent-color); /* The background color of the date when at the end of the selected range. */\n  \n  --rdp-week_number-border-radius: 100%; /* The border radius of the week number. */\n  --rdp-week_number-border: 2px solid transparent; /* The border of the week number. */\n  \n  --rdp-week_number-height: var(--rdp-day-height); /* The height of the week number cells. */\n  --rdp-week_number-opacity: 0.75; /* The opacity of the week number. */\n  --rdp-week_number-width: var(--rdp-day-width); /* The width of the week number cells. */\n  --rdp-weeknumber-text-align: center; /* The text alignment of the weekday cells. */\n\n  --rdp-weekday-opacity: 0.75; /* The opacity of the weekday. */\n  --rdp-weekday-padding: 0.5rem 0rem; /* The padding of the weekday. */\n  --rdp-weekday-text-align: center; /* The text alignment of the weekday cells. */\n\n  --rdp-gradient-direction: 90deg;\n}\n\n.rdp-root[dir="rtl"] {\n  --rdp-gradient-direction: -90deg;\n}\n\n.rdp-root[data-broadcast-calendar="true"] {\n  --rdp-outside-opacity: unset;\n}\n\n/* Root of the component. */\n.rdp-root {\n  position: relative; /* Required to position the navigation toolbar. */\n  box-sizing: border-box;\n}\n\n.rdp-root * {\n  box-sizing: border-box;\n}\n\n.rdp-day {\n  width: var(--rdp-day-width);\n  height: var(--rdp-day-height);\n  text-align: center;\n}\n\n.rdp-day_button {\n  background: none;\n  padding: 0;\n  margin: 0;\n  cursor: pointer;\n  font: inherit;\n  color: inherit;\n  justify-content: center;\n  align-items: center;\n  display: flex;\n\n  width: var(--rdp-day_button-width);\n  height: var(--rdp-day_button-height);\n  border: var(--rdp-day_button-border);\n  border-radius: var(--rdp-day_button-border-radius);\n}\n\n.rdp-day_button:disabled {\n  cursor: revert;\n}\n\n.rdp-caption_label {\n  z-index: 1;\n\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n\n  white-space: nowrap;\n  border: 0;\n}\n\n.rdp-dropdown:focus-visible ~ .rdp-caption_label {\n  outline: 5px auto Highlight;\n  outline: 5px auto -webkit-focus-ring-color;\n}\n\n.rdp-button_next,\n.rdp-button_previous {\n  border: none;\n  background: none;\n  padding: 0;\n  margin: 0;\n  cursor: pointer;\n  font: inherit;\n  color: inherit;\n  -moz-appearance: none;\n  -webkit-appearance: none;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  position: relative;\n  appearance: none;\n\n  width: var(--rdp-nav_button-width);\n  height: var(--rdp-nav_button-height);\n}\n\n.rdp-button_next:disabled,\n.rdp-button_previous:disabled {\n  cursor: revert;\n\n  opacity: var(--rdp-nav_button-disabled-opacity);\n}\n\n.rdp-chevron {\n  display: inline-block;\n  fill: var(--rdp-accent-color);\n}\n\n.rdp-root[dir="rtl"] .rdp-nav .rdp-chevron {\n  transform: rotate(180deg);\n}\n\n.rdp-root[dir="rtl"] .rdp-nav .rdp-chevron {\n  transform: rotate(180deg);\n  transform-origin: 50%;\n}\n\n.rdp-dropdowns {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n  gap: var(--rdp-dropdown-gap);\n}\n.rdp-dropdown {\n  z-index: 2;\n\n  /* Reset */\n  opacity: 0;\n  appearance: none;\n  position: absolute;\n  inset-block-start: 0;\n  inset-block-end: 0;\n  inset-inline-start: 0;\n  width: 100%;\n  margin: 0;\n  padding: 0;\n  cursor: inherit;\n  border: none;\n  line-height: inherit;\n}\n\n.rdp-dropdown_root {\n  position: relative;\n  display: inline-flex;\n  align-items: center;\n}\n\n.rdp-dropdown_root[data-disabled="true"] .rdp-chevron {\n  opacity: var(--rdp-disabled-opacity);\n}\n\n.rdp-month_caption {\n  display: flex;\n  align-content: center;\n  height: var(--rdp-nav-height);\n  font-weight: bold;\n  font-size: large;\n}\n\n.rdp-months {\n  position: relative;\n  display: flex;\n  flex-wrap: wrap;\n  gap: var(--rdp-months-gap);\n  max-width: fit-content;\n}\n\n.rdp-month_grid {\n  border-collapse: collapse;\n}\n\n.rdp-nav {\n  position: absolute;\n  inset-block-start: 0;\n  inset-inline-end: 0;\n\n  display: flex;\n  align-items: center;\n\n  height: var(--rdp-nav-height);\n}\n\n.rdp-weekday {\n  opacity: var(--rdp-weekday-opacity);\n  padding: var(--rdp-weekday-padding);\n  font-weight: 500;\n  font-size: smaller;\n  text-align: var(--rdp-weekday-text-align);\n  text-transform: var(--rdp-weekday-text-transform);\n}\n\n.rdp-week_number {\n  opacity: var(--rdp-week_number-opacity);\n  font-weight: 400;\n  font-size: small;\n  height: var(--rdp-week_number-height);\n  width: var(--rdp-week_number-width);\n  border: var(--rdp-week_number-border);\n  border-radius: var(--rdp-week_number-border-radius);\n  text-align: var(--rdp-weeknumber-text-align);\n}\n\n/* DAY MODIFIERS */\n.rdp-today:not(.rdp-outside) {\n  color: var(--rdp-today-color);\n}\n\n.rdp-selected {\n  font-weight: bold;\n  font-size: large;\n}\n\n.rdp-selected .rdp-day_button {\n  border: var(--rdp-selected-border);\n}\n\n.rdp-outside {\n  opacity: var(--rdp-outside-opacity);\n}\n\n.rdp-disabled {\n  opacity: var(--rdp-disabled-opacity);\n}\n\n.rdp-hidden {\n  visibility: hidden;\n  color: var(--rdp-range_start-color);\n}\n\n.rdp-range_start {\n  background: var(--rdp-range_start-background);\n}\n\n.rdp-range_start .rdp-day_button {\n  background-color: var(--rdp-range_start-date-background-color);\n  color: var(--rdp-range_start-color);\n}\n\n.rdp-range_middle {\n  background-color: var(--rdp-range_middle-background-color);\n}\n\n.rdp-range_middle .rdp-day_button {\n  border-color: transparent;\n  border: unset;\n  border-radius: unset;\n  color: var(--rdp-range_middle-color);\n}\n\n.rdp-range_end {\n  background: var(--rdp-range_end-background);\n  color: var(--rdp-range_end-color);\n}\n\n.rdp-range_end .rdp-day_button {\n  color: var(--rdp-range_start-color);\n  background-color: var(--rdp-range_end-date-background-color);\n}\n\n.rdp-range_start.rdp-range_end {\n  background: revert;\n}\n\n.rdp-focusable {\n  cursor: pointer;\n}\n',""]);const i=s},3645:e=>{e.exports=function(e){var t=[];t.toString=function t(){return this.map((function(t){var n=e(t);if(t[2]){return"@media ".concat(t[2]," {").concat(n,"}")}return n})).join("")};t.i=function(e,n,r){if(typeof e==="string"){e=[[null,e,""]]}var o={};if(r){for(var s=0;s<this.length;s++){var i=this[s][0];if(i!=null){o[i]=true}}}for(var a=0;a<e.length;a++){var c=[].concat(e[a]);if(r&&o[c[0]]){continue}if(n){if(!c[2]){c[2]=n}else{c[2]="".concat(n," and ").concat(c[2])}}t.push(c)}};return t}},6877:(e,t,n)=>{var r=n(3379);var o=n.n(r);var s=n(7795);var i=n.n(s);var a=n(569);var c=n.n(a);var l=n(3565);var u=n.n(l);var d=n(9216);var f=n.n(d);var h=n(4589);var p=n.n(h);var g=n(3954);var m={};m.styleTagTransform=p();m.setAttributes=u();m.insert=c().bind(null,"head");m.domAPI=i();m.insertStyleElement=f();var v=o()(g.Z,m);var b=g.Z&&g.Z.locals?g.Z.locals:undefined},3379:e=>{var t=[];function n(e){var n=-1;for(var r=0;r<t.length;r++){if(t[r].identifier===e){n=r;break}}return n}function r(e,r){var s={};var i=[];for(var a=0;a<e.length;a++){var c=e[a];var l=r.base?c[0]+r.base:c[0];var u=s[l]||0;var d="".concat(l," ").concat(u);s[l]=u+1;var f=n(d);var h={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(f!==-1){t[f].references++;t[f].updater(h)}else{var p=o(h,r);r.byIndex=a;t.splice(a,0,{identifier:d,updater:p,references:1})}i.push(d)}return i}function o(e,t){var n=t.domAPI(t);n.update(e);var r=function t(r){if(r){if(r.css===e.css&&r.media===e.media&&r.sourceMap===e.sourceMap&&r.supports===e.supports&&r.layer===e.layer){return}n.update(e=r)}else{n.remove()}};return r}e.exports=function(e,o){o=o||{};e=e||[];var s=r(e,o);return function e(i){i=i||[];for(var a=0;a<s.length;a++){var c=s[a];var l=n(c);t[l].references--}var u=r(i,o);for(var d=0;d<s.length;d++){var f=s[d];var h=n(f);if(t[h].references===0){t[h].updater();t.splice(h,1)}}s=u}}},569:e=>{var t={};function n(e){if(typeof t[e]==="undefined"){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement){try{n=n.contentDocument.head}catch(e){n=null}}t[e]=n}return t[e]}function r(e,t){var r=n(e);if(!r){throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.")}r.appendChild(t)}e.exports=r},9216:e=>{function t(e){var t=document.createElement("style");e.setAttributes(t,e.attributes);e.insert(t,e.options);return t}e.exports=t},3565:(e,t,n)=>{function r(e){var t=true?n.nc:0;if(t){e.setAttribute("nonce",t)}}e.exports=r},7795:e=>{function t(e,t,n){var r="";if(n.supports){r+="@supports (".concat(n.supports,") {")}if(n.media){r+="@media ".concat(n.media," {")}var o=typeof n.layer!=="undefined";if(o){r+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")}r+=n.css;if(o){r+="}"}if(n.media){r+="}"}if(n.supports){r+="}"}var s=n.sourceMap;if(s&&typeof btoa!=="undefined"){r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(s))))," */")}t.styleTagTransform(r,e,t.options)}function n(e){if(e.parentNode===null){return false}e.parentNode.removeChild(e)}function r(e){if(typeof document==="undefined"){return{update:function e(){},remove:function e(){}}}var r=e.insertStyleElement(e);return{update:function n(o){t(r,e,o)},remove:function e(){n(r)}}}e.exports=r},4589:e=>{function t(e,t){if(t.styleSheet){t.styleSheet.cssText=e}else{while(t.firstChild){t.removeChild(t.firstChild)}t.appendChild(document.createTextNode(e))}}e.exports=t},5298:(e,t,n)=>{n.d(t,{LB:()=>Dt,y9:()=>Ut,g4:()=>ve,Lg:()=>we,uN:()=>We,we:()=>Se,pE:()=>N,ey:()=>O,VK:()=>Y,_8:()=>_,hI:()=>q,Cj:()=>_t,O1:()=>Mt,Zj:()=>Ot,VT:()=>y,Dy:()=>w});var r=n(7363);var o=n.n(r);var s=n(1533);var i=n(3618);const a={display:"none"};function c(e){let{id:t,value:n}=e;return o().createElement("div",{id:t,style:a},n)}function l(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;const s={position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"};return o().createElement("div",{id:t,style:s,role:"status","aria-live":r,"aria-atomic":true},n)}function u(){const[e,t]=(0,r.useState)("");const n=(0,r.useCallback)((e=>{if(e!=null){t(e)}}),[]);return{announce:n,announcement:e}}const d=(0,r.createContext)(null);function f(e){const t=(0,r.useContext)(d);(0,r.useEffect)((()=>{if(!t){throw new Error("useDndMonitor must be used within a children of <DndContext>")}const n=t(e);return n}),[e,t])}function h(){const[e]=(0,r.useState)((()=>new Set));const t=(0,r.useCallback)((t=>{e.add(t);return()=>e.delete(t)}),[e]);const n=(0,r.useCallback)((t=>{let{type:n,event:r}=t;e.forEach((e=>{var t;return(t=e[n])==null?void 0:t.call(e,r)}))}),[e]);return[n,t]}const p={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "};const g={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;if(n){return"Draggable item "+t.id+" was moved over droppable area "+n.id+"."}return"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;if(n){return"Draggable item "+t.id+" was dropped over droppable area "+n.id}return"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function m(e){let{announcements:t=g,container:n,hiddenTextDescribedById:a,screenReaderInstructions:d=p}=e;const{announce:h,announcement:m}=u();const v=(0,i.Ld)("DndLiveRegion");const[b,y]=(0,r.useState)(false);(0,r.useEffect)((()=>{y(true)}),[]);f((0,r.useMemo)((()=>({onDragStart(e){let{active:n}=e;h(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;if(t.onDragMove){h(t.onDragMove({active:n,over:r}))}},onDragOver(e){let{active:n,over:r}=e;h(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;h(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;h(t.onDragCancel({active:n,over:r}))}})),[h,t]));if(!b){return null}const w=o().createElement(o().Fragment,null,o().createElement(c,{id:a,value:d.draggable}),o().createElement(l,{id:v,announcement:m}));return n?(0,s.createPortal)(w,n):w}var v;(function(e){e["DragStart"]="dragStart";e["DragMove"]="dragMove";e["DragEnd"]="dragEnd";e["DragCancel"]="dragCancel";e["DragOver"]="dragOver";e["RegisterDroppable"]="registerDroppable";e["SetDroppableDisabled"]="setDroppableDisabled";e["UnregisterDroppable"]="unregisterDroppable"})(v||(v={}));function b(){}function y(e,t){return(0,r.useMemo)((()=>({sensor:e,options:t!=null?t:{}})),[e,t])}function w(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++){t[n]=arguments[n]}return(0,r.useMemo)((()=>[...t].filter((e=>e!=null))),[...t])}const x=Object.freeze({x:0,y:0});function D(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function k(e,t){const n=(0,i.DC)(e);if(!n){return"0 0"}const r={x:(n.x-t.left)/t.width*100,y:(n.y-t.top)/t.height*100};return r.x+"% "+r.y+"%"}function C(e,t){let{data:{value:n}}=e;let{data:{value:r}}=t;return n-r}function S(e,t){let{data:{value:n}}=e;let{data:{value:r}}=t;return r-n}function M(e){let{left:t,top:n,height:r,width:o}=e;return[{x:t,y:n},{x:t+o,y:n},{x:t,y:n+r},{x:t+o,y:n+r}]}function _(e,t){if(!e||e.length===0){return null}const[n]=e;return t?n[t]:n}function E(e,t,n){if(t===void 0){t=e.left}if(n===void 0){n=e.top}return{x:t+e.width*.5,y:n+e.height*.5}}const N=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=E(t,t.left,t.top);const s=[];for(const e of r){const{id:t}=e;const r=n.get(t);if(r){const n=D(E(r),o);s.push({id:t,data:{droppableContainer:e,value:n}})}}return s.sort(C)};const O=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=M(t);const s=[];for(const e of r){const{id:t}=e;const r=n.get(t);if(r){const n=M(r);const i=o.reduce(((e,t,r)=>e+D(n[r],t)),0);const a=Number((i/4).toFixed(4));s.push({id:t,data:{droppableContainer:e,value:a}})}}return s.sort(C)};function R(e,t){const n=Math.max(t.top,e.top);const r=Math.max(t.left,e.left);const o=Math.min(t.left+t.width,e.left+e.width);const s=Math.min(t.top+t.height,e.top+e.height);const i=o-r;const a=s-n;if(r<o&&n<s){const n=t.width*t.height;const r=e.width*e.height;const o=i*a;const s=o/(n+r-o);return Number(s.toFixed(4))}return 0}const T=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=[];for(const e of r){const{id:r}=e;const s=n.get(r);if(s){const n=R(s,t);if(n>0){o.push({id:r,data:{droppableContainer:e,value:n}})}}}return o.sort(S)};function A(e,t){const{top:n,left:r,bottom:o,right:s}=t;return n<=e.y&&e.y<=o&&r<=e.x&&e.x<=s}const I=e=>{let{droppableContainers:t,droppableRects:n,pointerCoordinates:r}=e;if(!r){return[]}const o=[];for(const e of t){const{id:t}=e;const s=n.get(t);if(s&&A(r,s)){const n=M(s);const i=n.reduce(((e,t)=>e+D(r,t)),0);const a=Number((i/4).toFixed(4));o.push({id:t,data:{droppableContainer:e,value:a}})}}return o.sort(C)};function F(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}function L(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:x}function P(e){return function t(n){for(var r=arguments.length,o=new Array(r>1?r-1:0),s=1;s<r;s++){o[s-1]=arguments[s]}return o.reduce(((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x})),{...n})}}const W=P(1);function j(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}else if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}function B(e,t,n){const r=j(t);if(!r){return e}const{scaleX:o,scaleY:s,x:i,y:a}=r;const c=e.left-i-(1-o)*parseFloat(n);const l=e.top-a-(1-s)*parseFloat(n.slice(n.indexOf(" ")+1));const u=o?e.width/o:e.width;const d=s?e.height/s:e.height;return{width:u,height:d,top:l,right:c+u,bottom:l+d,left:c}}const z={ignoreTransform:false};function Y(e,t){if(t===void 0){t=z}let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:t,transformOrigin:r}=(0,i.Jj)(e).getComputedStyle(e);if(t){n=B(n,t,r)}}const{top:r,left:o,width:s,height:a,bottom:c,right:l}=n;return{top:r,left:o,width:s,height:a,bottom:c,right:l}}function V(e){return Y(e,{ignoreTransform:true})}function U(e){const t=e.innerWidth;const n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}function H(e,t){if(t===void 0){t=(0,i.Jj)(e).getComputedStyle(e)}return t.position==="fixed"}function X(e,t){if(t===void 0){t=(0,i.Jj)(e).getComputedStyle(e)}const n=/(auto|scroll|overlay)/;const r=["overflow","overflowX","overflowY"];return r.some((e=>{const r=t[e];return typeof r==="string"?n.test(r):false}))}function q(e,t){const n=[];function r(o){if(t!=null&&n.length>=t){return n}if(!o){return n}if((0,i.qk)(o)&&o.scrollingElement!=null&&!n.includes(o.scrollingElement)){n.push(o.scrollingElement);return n}if(!(0,i.Re)(o)||(0,i.vZ)(o)){return n}if(n.includes(o)){return n}const s=(0,i.Jj)(e).getComputedStyle(o);if(o!==e){if(X(o,s)){n.push(o)}}if(H(o,s)){return n}return r(o.parentNode)}if(!e){return n}return r(e)}function Z(e){const[t]=q(e,1);return t!=null?t:null}function J(e){if(!i.Nq||!e){return null}if((0,i.FJ)(e)){return e}if(!(0,i.UG)(e)){return null}if((0,i.qk)(e)||e===(0,i.r3)(e).scrollingElement){return window}if((0,i.Re)(e)){return e}return null}function G(e){if((0,i.FJ)(e)){return e.scrollX}return e.scrollLeft}function $(e){if((0,i.FJ)(e)){return e.scrollY}return e.scrollTop}function K(e){return{x:G(e),y:$(e)}}var Q;(function(e){e[e["Forward"]=1]="Forward";e[e["Backward"]=-1]="Backward"})(Q||(Q={}));function ee(e){if(!i.Nq||!e){return false}return e===document.scrollingElement}function te(e){const t={x:0,y:0};const n=ee(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth};const r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height};const o=e.scrollTop<=t.y;const s=e.scrollLeft<=t.x;const i=e.scrollTop>=r.y;const a=e.scrollLeft>=r.x;return{isTop:o,isLeft:s,isBottom:i,isRight:a,maxScroll:r,minScroll:t}}const ne={x:.2,y:.2};function re(e,t,n,r,o){let{top:s,left:i,right:a,bottom:c}=n;if(r===void 0){r=10}if(o===void 0){o=ne}const{isTop:l,isBottom:u,isLeft:d,isRight:f}=te(e);const h={x:0,y:0};const p={x:0,y:0};const g={height:t.height*o.y,width:t.width*o.x};if(!l&&s<=t.top+g.height){h.y=Q.Backward;p.y=r*Math.abs((t.top+g.height-s)/g.height)}else if(!u&&c>=t.bottom-g.height){h.y=Q.Forward;p.y=r*Math.abs((t.bottom-g.height-c)/g.height)}if(!f&&a>=t.right-g.width){h.x=Q.Forward;p.x=r*Math.abs((t.right-g.width-a)/g.width)}else if(!d&&i<=t.left+g.width){h.x=Q.Backward;p.x=r*Math.abs((t.left+g.width-i)/g.width)}return{direction:h,speed:p}}function oe(e){if(e===document.scrollingElement){const{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}const{top:t,left:n,right:r,bottom:o}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:o,width:e.clientWidth,height:e.clientHeight}}function se(e){return e.reduce(((e,t)=>(0,i.IH)(e,K(t))),x)}function ie(e){return e.reduce(((e,t)=>e+G(t)),0)}function ae(e){return e.reduce(((e,t)=>e+$(t)),0)}function ce(e,t){if(t===void 0){t=Y}if(!e){return}const{top:n,left:r,bottom:o,right:s}=t(e);const i=Z(e);if(!i){return}if(o<=0||s<=0||n>=window.innerHeight||r>=window.innerWidth){e.scrollIntoView({block:"center",inline:"center"})}}const le=[["x",["left","right"],ie],["y",["top","bottom"],ae]];class ue{constructor(e,t){this.rect=void 0;this.width=void 0;this.height=void 0;this.top=void 0;this.bottom=void 0;this.right=void 0;this.left=void 0;const n=q(t);const r=se(n);this.rect={...e};this.width=e.width;this.height=e.height;for(const[e,t,o]of le){for(const s of t){Object.defineProperty(this,s,{get:()=>{const t=o(n);const i=r[e]-t;return this.rect[s]+i},enumerable:true})}}Object.defineProperty(this,"rect",{enumerable:false})}}class de{constructor(e){this.target=void 0;this.listeners=[];this.removeAll=()=>{this.listeners.forEach((e=>{var t;return(t=this.target)==null?void 0:t.removeEventListener(...e)}))};this.target=e}add(e,t,n){var r;(r=this.target)==null?void 0:r.addEventListener(e,t,n);this.listeners.push([e,t,n])}}function fe(e){const{EventTarget:t}=(0,i.Jj)(e);return e instanceof t?e:(0,i.r3)(e)}function he(e,t){const n=Math.abs(e.x);const r=Math.abs(e.y);if(typeof t==="number"){return Math.sqrt(n**2+r**2)>t}if("x"in t&&"y"in t){return n>t.x&&r>t.y}if("x"in t){return n>t.x}if("y"in t){return r>t.y}return false}var pe;(function(e){e["Click"]="click";e["DragStart"]="dragstart";e["Keydown"]="keydown";e["ContextMenu"]="contextmenu";e["Resize"]="resize";e["SelectionChange"]="selectionchange";e["VisibilityChange"]="visibilitychange"})(pe||(pe={}));function ge(e){e.preventDefault()}function me(e){e.stopPropagation()}var ve;(function(e){e["Space"]="Space";e["Down"]="ArrowDown";e["Right"]="ArrowRight";e["Left"]="ArrowLeft";e["Up"]="ArrowUp";e["Esc"]="Escape";e["Enter"]="Enter";e["Tab"]="Tab"})(ve||(ve={}));const be={start:[ve.Space,ve.Enter],cancel:[ve.Esc],end:[ve.Space,ve.Enter,ve.Tab]};const ye=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case ve.Right:return{...n,x:n.x+25};case ve.Left:return{...n,x:n.x-25};case ve.Down:return{...n,y:n.y+25};case ve.Up:return{...n,y:n.y-25}}return undefined};class we{constructor(e){this.props=void 0;this.autoScrollEnabled=false;this.referenceCoordinates=void 0;this.listeners=void 0;this.windowListeners=void 0;this.props=e;const{event:{target:t}}=e;this.props=e;this.listeners=new de((0,i.r3)(t));this.windowListeners=new de((0,i.Jj)(t));this.handleKeyDown=this.handleKeyDown.bind(this);this.handleCancel=this.handleCancel.bind(this);this.attach()}attach(){this.handleStart();this.windowListeners.add(pe.Resize,this.handleCancel);this.windowListeners.add(pe.VisibilityChange,this.handleCancel);setTimeout((()=>this.listeners.add(pe.Keydown,this.handleKeyDown)))}handleStart(){const{activeNode:e,onStart:t}=this.props;const n=e.node.current;if(n){ce(n)}t(x)}handleKeyDown(e){if((0,i.vd)(e)){const{active:t,context:n,options:r}=this.props;const{keyboardCodes:o=be,coordinateGetter:s=ye,scrollBehavior:a="smooth"}=r;const{code:c}=e;if(o.end.includes(c)){this.handleEnd(e);return}if(o.cancel.includes(c)){this.handleCancel(e);return}const{collisionRect:l}=n.current;const u=l?{x:l.left,y:l.top}:x;if(!this.referenceCoordinates){this.referenceCoordinates=u}const d=s(e,{active:t,context:n.current,currentCoordinates:u});if(d){const t=(0,i.$X)(d,u);const r={x:0,y:0};const{scrollableAncestors:o}=n.current;for(const n of o){const o=e.code;const{isTop:s,isRight:i,isLeft:c,isBottom:l,maxScroll:u,minScroll:f}=te(n);const h=oe(n);const p={x:Math.min(o===ve.Right?h.right-h.width/2:h.right,Math.max(o===ve.Right?h.left:h.left+h.width/2,d.x)),y:Math.min(o===ve.Down?h.bottom-h.height/2:h.bottom,Math.max(o===ve.Down?h.top:h.top+h.height/2,d.y))};const g=o===ve.Right&&!i||o===ve.Left&&!c;const m=o===ve.Down&&!l||o===ve.Up&&!s;if(g&&p.x!==d.x){const e=n.scrollLeft+t.x;const s=o===ve.Right&&e<=u.x||o===ve.Left&&e>=f.x;if(s&&!t.y){n.scrollTo({left:e,behavior:a});return}if(s){r.x=n.scrollLeft-e}else{r.x=o===ve.Right?n.scrollLeft-u.x:n.scrollLeft-f.x}if(r.x){n.scrollBy({left:-r.x,behavior:a})}break}else if(m&&p.y!==d.y){const e=n.scrollTop+t.y;const s=o===ve.Down&&e<=u.y||o===ve.Up&&e>=f.y;if(s&&!t.x){n.scrollTo({top:e,behavior:a});return}if(s){r.y=n.scrollTop-e}else{r.y=o===ve.Down?n.scrollTop-u.y:n.scrollTop-f.y}if(r.y){n.scrollBy({top:-r.y,behavior:a})}break}}this.handleMove(e,(0,i.IH)((0,i.$X)(d,this.referenceCoordinates),r))}}}handleMove(e,t){const{onMove:n}=this.props;e.preventDefault();n(t)}handleEnd(e){const{onEnd:t}=this.props;e.preventDefault();this.detach();t()}handleCancel(e){const{onCancel:t}=this.props;e.preventDefault();this.detach();t()}detach(){this.listeners.removeAll();this.windowListeners.removeAll()}}we.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=be,onActivation:o}=t;let{active:s}=n;const{code:i}=e.nativeEvent;if(r.start.includes(i)){const t=s.activatorNode.current;if(t&&e.target!==t){return false}e.preventDefault();o==null?void 0:o({event:e.nativeEvent});return true}return false}}];function xe(e){return Boolean(e&&"distance"in e)}function De(e){return Boolean(e&&"delay"in e)}class ke{constructor(e,t,n){var r;if(n===void 0){n=fe(e.event.target)}this.props=void 0;this.events=void 0;this.autoScrollEnabled=true;this.document=void 0;this.activated=false;this.initialCoordinates=void 0;this.timeoutId=null;this.listeners=void 0;this.documentListeners=void 0;this.windowListeners=void 0;this.props=e;this.events=t;const{event:o}=e;const{target:s}=o;this.props=e;this.events=t;this.document=(0,i.r3)(s);this.documentListeners=new de(this.document);this.listeners=new de(n);this.windowListeners=new de((0,i.Jj)(s));this.initialCoordinates=(r=(0,i.DC)(o))!=null?r:x;this.handleStart=this.handleStart.bind(this);this.handleMove=this.handleMove.bind(this);this.handleEnd=this.handleEnd.bind(this);this.handleCancel=this.handleCancel.bind(this);this.handleKeydown=this.handleKeydown.bind(this);this.removeTextSelection=this.removeTextSelection.bind(this);this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;this.listeners.add(e.move.name,this.handleMove,{passive:false});this.listeners.add(e.end.name,this.handleEnd);if(e.cancel){this.listeners.add(e.cancel.name,this.handleCancel)}this.windowListeners.add(pe.Resize,this.handleCancel);this.windowListeners.add(pe.DragStart,ge);this.windowListeners.add(pe.VisibilityChange,this.handleCancel);this.windowListeners.add(pe.ContextMenu,ge);this.documentListeners.add(pe.Keydown,this.handleKeydown);if(t){if(n!=null&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options})){return this.handleStart()}if(De(t)){this.timeoutId=setTimeout(this.handleStart,t.delay);this.handlePending(t);return}if(xe(t)){this.handlePending(t);return}}this.handleStart()}detach(){this.listeners.removeAll();this.windowListeners.removeAll();setTimeout(this.documentListeners.removeAll,50);if(this.timeoutId!==null){clearTimeout(this.timeoutId);this.timeoutId=null}}handlePending(e,t){const{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){const{initialCoordinates:e}=this;const{onStart:t}=this.props;if(e){this.activated=true;this.documentListeners.add(pe.Click,me,{capture:true});this.removeTextSelection();this.documentListeners.add(pe.SelectionChange,this.removeTextSelection);t(e)}}handleMove(e){var t;const{activated:n,initialCoordinates:r,props:o}=this;const{onMove:s,options:{activationConstraint:a}}=o;if(!r){return}const c=(t=(0,i.DC)(e))!=null?t:x;const l=(0,i.$X)(r,c);if(!n&&a){if(xe(a)){if(a.tolerance!=null&&he(l,a.tolerance)){return this.handleCancel()}if(he(l,a.distance)){return this.handleStart()}}if(De(a)){if(he(l,a.tolerance)){return this.handleCancel()}}this.handlePending(a,l);return}if(e.cancelable){e.preventDefault()}s(c)}handleEnd(){const{onAbort:e,onEnd:t}=this.props;this.detach();if(!this.activated){e(this.props.active)}t()}handleCancel(){const{onAbort:e,onCancel:t}=this.props;this.detach();if(!this.activated){e(this.props.active)}t()}handleKeydown(e){if(e.code===ve.Esc){this.handleCancel()}}removeTextSelection(){var e;(e=this.document.getSelection())==null?void 0:e.removeAllRanges()}}const Ce={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class Se extends ke{constructor(e){const{event:t}=e;const n=(0,i.r3)(t.target);super(e,Ce,n)}}Se.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e;let{onActivation:r}=t;if(!n.isPrimary||n.button!==0){return false}r==null?void 0:r({event:n});return true}}];const Me={move:{name:"mousemove"},end:{name:"mouseup"}};var _e;(function(e){e[e["RightClick"]=2]="RightClick"})(_e||(_e={}));class Ee extends ke{constructor(e){super(e,Me,(0,i.r3)(e.event.target))}}Ee.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e;let{onActivation:r}=t;if(n.button===_e.RightClick){return false}r==null?void 0:r({event:n});return true}}];const Ne={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class Oe extends ke{constructor(e){super(e,Ne)}static setup(){window.addEventListener(Ne.move.name,e,{capture:false,passive:false});return function t(){window.removeEventListener(Ne.move.name,e)};function e(){}}}Oe.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e;let{onActivation:r}=t;const{touches:o}=n;if(o.length>1){return false}r==null?void 0:r({event:n});return true}}];var Re;(function(e){e[e["Pointer"]=0]="Pointer";e[e["DraggableRect"]=1]="DraggableRect"})(Re||(Re={}));var Te;(function(e){e[e["TreeOrder"]=0]="TreeOrder";e[e["ReversedTreeOrder"]=1]="ReversedTreeOrder"})(Te||(Te={}));function Ae(e){let{acceleration:t,activator:n=Re.Pointer,canScroll:o,draggingRect:s,enabled:a,interval:c=5,order:l=Te.TreeOrder,pointerCoordinates:u,scrollableAncestors:d,scrollableAncestorRects:f,delta:h,threshold:p}=e;const g=Fe({delta:h,disabled:!a});const[m,v]=(0,i.Yz)();const b=(0,r.useRef)({x:0,y:0});const y=(0,r.useRef)({x:0,y:0});const w=(0,r.useMemo)((()=>{switch(n){case Re.Pointer:return u?{top:u.y,bottom:u.y,left:u.x,right:u.x}:null;case Re.DraggableRect:return s}}),[n,s,u]);const x=(0,r.useRef)(null);const D=(0,r.useCallback)((()=>{const e=x.current;if(!e){return}const t=b.current.x*y.current.x;const n=b.current.y*y.current.y;e.scrollBy(t,n)}),[]);const k=(0,r.useMemo)((()=>l===Te.TreeOrder?[...d].reverse():d),[l,d]);(0,r.useEffect)((()=>{if(!a||!d.length||!w){v();return}for(const e of k){if((o==null?void 0:o(e))===false){continue}const n=d.indexOf(e);const r=f[n];if(!r){continue}const{direction:s,speed:i}=re(e,r,w,t,p);for(const e of["x","y"]){if(!g[e][s[e]]){i[e]=0;s[e]=0}}if(i.x>0||i.y>0){v();x.current=e;m(D,c);b.current=i;y.current=s;return}}b.current={x:0,y:0};y.current={x:0,y:0};v()}),[t,D,o,v,a,c,JSON.stringify(w),JSON.stringify(g),m,d,k,f,JSON.stringify(p)])}const Ie={x:{[Q.Backward]:false,[Q.Forward]:false},y:{[Q.Backward]:false,[Q.Forward]:false}};function Fe(e){let{delta:t,disabled:n}=e;const r=(0,i.D9)(t);return(0,i.Gj)((e=>{if(n||!r||!e){return Ie}const o={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[Q.Backward]:e.x[Q.Backward]||o.x===-1,[Q.Forward]:e.x[Q.Forward]||o.x===1},y:{[Q.Backward]:e.y[Q.Backward]||o.y===-1,[Q.Forward]:e.y[Q.Forward]||o.y===1}}}),[n,t,r])}function Le(e,t){const n=t!=null?e.get(t):undefined;const r=n?n.node.current:null;return(0,i.Gj)((e=>{var n;if(t==null){return null}return(n=r!=null?r:e)!=null?n:null}),[r,t])}function Pe(e,t){return(0,r.useMemo)((()=>e.reduce(((e,n)=>{const{sensor:r}=n;const o=r.activators.map((e=>({eventName:e.eventName,handler:t(e.handler,n)})));return[...e,...o]}),[])),[e,t])}var We;(function(e){e[e["Always"]=0]="Always";e[e["BeforeDragging"]=1]="BeforeDragging";e[e["WhileDragging"]=2]="WhileDragging"})(We||(We={}));var je;(function(e){e["Optimized"]="optimized"})(je||(je={}));const Be=new Map;function ze(e,t){let{dragging:n,dependencies:o,config:s}=t;const[a,c]=(0,r.useState)(null);const{frequency:l,measure:u,strategy:d}=s;const f=(0,r.useRef)(e);const h=b();const p=(0,i.Ey)(h);const g=(0,r.useCallback)((function(e){if(e===void 0){e=[]}if(p.current){return}c((t=>{if(t===null){return e}return t.concat(e.filter((e=>!t.includes(e))))}))}),[p]);const m=(0,r.useRef)(null);const v=(0,i.Gj)((t=>{if(h&&!n){return Be}if(!t||t===Be||f.current!==e||a!=null){const t=new Map;for(let n of e){if(!n){continue}if(a&&a.length>0&&!a.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}const e=n.node.current;const r=e?new ue(u(e),e):null;n.rect.current=r;if(r){t.set(n.id,r)}}return t}return t}),[e,a,n,h,u]);(0,r.useEffect)((()=>{f.current=e}),[e]);(0,r.useEffect)((()=>{if(h){return}g()}),[n,h]);(0,r.useEffect)((()=>{if(a&&a.length>0){c(null)}}),[JSON.stringify(a)]);(0,r.useEffect)((()=>{if(h||typeof l!=="number"||m.current!==null){return}m.current=setTimeout((()=>{g();m.current=null}),l)}),[l,h,g,...o]);return{droppableRects:v,measureDroppableContainers:g,measuringScheduled:a!=null};function b(){switch(d){case We.Always:return false;case We.BeforeDragging:return n;default:return!n}}}function Ye(e,t){return(0,i.Gj)((n=>{if(!e){return null}if(n){return n}return typeof t==="function"?t(e):e}),[t,e])}function Ve(e,t){return Ye(e,t)}function Ue(e){let{callback:t,disabled:n}=e;const o=(0,i.zX)(t);const s=(0,r.useMemo)((()=>{if(n||typeof window==="undefined"||typeof window.MutationObserver==="undefined"){return undefined}const{MutationObserver:e}=window;return new e(o)}),[o,n]);(0,r.useEffect)((()=>()=>s==null?void 0:s.disconnect()),[s]);return s}function He(e){let{callback:t,disabled:n}=e;const o=(0,i.zX)(t);const s=(0,r.useMemo)((()=>{if(n||typeof window==="undefined"||typeof window.ResizeObserver==="undefined"){return undefined}const{ResizeObserver:e}=window;return new e(o)}),[n]);(0,r.useEffect)((()=>()=>s==null?void 0:s.disconnect()),[s]);return s}function Xe(e){return new ue(Y(e),e)}function qe(e,t,n){if(t===void 0){t=Xe}const[o,s]=(0,r.useState)(null);function a(){s((r=>{if(!e){return null}if(e.isConnected===false){var o;return(o=r!=null?r:n)!=null?o:null}const s=t(e);if(JSON.stringify(r)===JSON.stringify(s)){return r}return s}))}const c=Ue({callback(t){if(!e){return}for(const n of t){const{type:t,target:r}=n;if(t==="childList"&&r instanceof HTMLElement&&r.contains(e)){a();break}}}});const l=He({callback:a});(0,i.LI)((()=>{a();if(e){l==null?void 0:l.observe(e);c==null?void 0:c.observe(document.body,{childList:true,subtree:true})}else{l==null?void 0:l.disconnect();c==null?void 0:c.disconnect()}}),[e]);return o}function Ze(e){const t=Ye(e);return L(e,t)}const Je=[];function Ge(e){const t=(0,r.useRef)(e);const n=(0,i.Gj)((n=>{if(!e){return Je}if(n&&n!==Je&&e&&t.current&&e.parentNode===t.current.parentNode){return n}return q(e)}),[e]);(0,r.useEffect)((()=>{t.current=e}),[e]);return n}function $e(e){const[t,n]=(0,r.useState)(null);const o=(0,r.useRef)(e);const s=(0,r.useCallback)((e=>{const t=J(e.target);if(!t){return}n((e=>{if(!e){return null}e.set(t,K(t));return new Map(e)}))}),[]);(0,r.useEffect)((()=>{const t=o.current;if(e!==t){r(t);const i=e.map((e=>{const t=J(e);if(t){t.addEventListener("scroll",s,{passive:true});return[t,K(t)]}return null})).filter((e=>e!=null));n(i.length?new Map(i):null);o.current=e}return()=>{r(e);r(t)};function r(e){e.forEach((e=>{const t=J(e);t==null?void 0:t.removeEventListener("scroll",s)}))}}),[s,e]);return(0,r.useMemo)((()=>{if(e.length){return t?Array.from(t.values()).reduce(((e,t)=>(0,i.IH)(e,t)),x):se(e)}return x}),[e,t])}function Ke(e,t){if(t===void 0){t=[]}const n=(0,r.useRef)(null);(0,r.useEffect)((()=>{n.current=null}),t);(0,r.useEffect)((()=>{const t=e!==x;if(t&&!n.current){n.current=e}if(!t&&n.current){n.current=null}}),[e]);return n.current?(0,i.$X)(e,n.current):x}function Qe(e){(0,r.useEffect)((()=>{if(!i.Nq){return}const t=e.map((e=>{let{sensor:t}=e;return t.setup==null?void 0:t.setup()}));return()=>{for(const e of t){e==null?void 0:e()}}}),e.map((e=>{let{sensor:t}=e;return t})))}function et(e,t){return(0,r.useMemo)((()=>e.reduce(((e,n)=>{let{eventName:r,handler:o}=n;e[r]=e=>{o(e,t)};return e}),{})),[e,t])}function tt(e){return(0,r.useMemo)((()=>e?U(e):null),[e])}const nt=[];function rt(e,t){if(t===void 0){t=Y}const[n]=e;const o=tt(n?(0,i.Jj)(n):null);const[s,a]=(0,r.useState)(nt);function c(){a((()=>{if(!e.length){return nt}return e.map((e=>ee(e)?o:new ue(t(e),e)))}))}const l=He({callback:c});(0,i.LI)((()=>{l==null?void 0:l.disconnect();c();e.forEach((e=>l==null?void 0:l.observe(e)))}),[e]);return s}function ot(e){if(!e){return null}if(e.children.length>1){return e}const t=e.children[0];return(0,i.Re)(t)?t:e}function st(e){let{measure:t}=e;const[n,o]=(0,r.useState)(null);const s=(0,r.useCallback)((e=>{for(const{target:n}of e){if((0,i.Re)(n)){o((e=>{const r=t(n);return e?{...e,width:r.width,height:r.height}:r}));break}}}),[t]);const a=He({callback:s});const c=(0,r.useCallback)((e=>{const n=ot(e);a==null?void 0:a.disconnect();if(n){a==null?void 0:a.observe(n)}o(n?t(n):null)}),[t,a]);const[l,u]=(0,i.wm)(c);return(0,r.useMemo)((()=>({nodeRef:l,rect:n,setRef:u})),[n,l,u])}const it=[{sensor:Se,options:{}},{sensor:we,options:{}}];const at={current:{}};const ct={draggable:{measure:V},droppable:{measure:V,strategy:We.WhileDragging,frequency:je.Optimized},dragOverlay:{measure:Y}};class lt extends Map{get(e){var t;return e!=null?(t=super.get(e))!=null?t:undefined:undefined}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter((e=>{let{disabled:t}=e;return!t}))}getNodeFor(e){var t,n;return(t=(n=this.get(e))==null?void 0:n.node.current)!=null?t:undefined}}const ut={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new lt,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:b},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:ct,measureDroppableContainers:b,windowRect:null,measuringScheduled:false};const dt={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:b,draggableNodes:new Map,over:null,measureDroppableContainers:b};const ft=(0,r.createContext)(dt);const ht=(0,r.createContext)(ut);function pt(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new lt}}}function gt(e,t){switch(t.type){case v.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case v.DragMove:if(e.draggable.active==null){return e}return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case v.DragEnd:case v.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case v.RegisterDroppable:{const{element:n}=t;const{id:r}=n;const o=new lt(e.droppable.containers);o.set(r,n);return{...e,droppable:{...e.droppable,containers:o}}}case v.SetDroppableDisabled:{const{id:n,key:r,disabled:o}=t;const s=e.droppable.containers.get(n);if(!s||r!==s.key){return e}const i=new lt(e.droppable.containers);i.set(n,{...s,disabled:o});return{...e,droppable:{...e.droppable,containers:i}}}case v.UnregisterDroppable:{const{id:n,key:r}=t;const o=e.droppable.containers.get(n);if(!o||r!==o.key){return e}const s=new lt(e.droppable.containers);s.delete(n);return{...e,droppable:{...e.droppable,containers:s}}}default:{return e}}}function mt(e){let{disabled:t}=e;const{active:n,activatorEvent:o,draggableNodes:s}=(0,r.useContext)(ft);const a=(0,i.D9)(o);const c=(0,i.D9)(n==null?void 0:n.id);(0,r.useEffect)((()=>{if(t){return}if(!o&&a&&c!=null){if(!(0,i.vd)(a)){return}if(document.activeElement===a.target){return}const e=s.get(c);if(!e){return}const{activatorNode:t,node:n}=e;if(!t.current&&!n.current){return}requestAnimationFrame((()=>{for(const e of[t.current,n.current]){if(!e){continue}const t=(0,i.so)(e);if(t){t.focus();break}}}))}}),[o,t,s,c,a]);return null}function vt(e,t){let{transform:n,...r}=t;return e!=null&&e.length?e.reduce(((e,t)=>t({transform:e,...r})),n):n}function bt(e){return(0,r.useMemo)((()=>({draggable:{...ct.draggable,...e==null?void 0:e.draggable},droppable:{...ct.droppable,...e==null?void 0:e.droppable},dragOverlay:{...ct.dragOverlay,...e==null?void 0:e.dragOverlay}})),[e==null?void 0:e.draggable,e==null?void 0:e.droppable,e==null?void 0:e.dragOverlay])}function yt(e){let{activeNode:t,measure:n,initialRect:o,config:s=true}=e;const a=(0,r.useRef)(false);const{x:c,y:l}=typeof s==="boolean"?{x:s,y:s}:s;(0,i.LI)((()=>{const e=!c&&!l;if(e||!t){a.current=false;return}if(a.current||!o){return}const r=t==null?void 0:t.node.current;if(!r||r.isConnected===false){return}const s=n(r);const i=L(s,o);if(!c){i.x=0}if(!l){i.y=0}a.current=true;if(Math.abs(i.x)>0||Math.abs(i.y)>0){const e=Z(r);if(e){e.scrollBy({top:i.y,left:i.x})}}}),[t,c,l,o,n])}const wt=(0,r.createContext)({...x,scaleX:1,scaleY:1});var xt;(function(e){e[e["Uninitialized"]=0]="Uninitialized";e[e["Initializing"]=1]="Initializing";e[e["Initialized"]=2]="Initialized"})(xt||(xt={}));const Dt=(0,r.memo)((function e(t){var n,a,c,l;let{id:u,accessibility:f,autoScroll:p=true,children:g,sensors:b=it,collisionDetection:y=T,measuring:w,modifiers:x,...D}=t;const k=(0,r.useReducer)(gt,undefined,pt);const[C,S]=k;const[M,E]=h();const[N,O]=(0,r.useState)(xt.Uninitialized);const R=N===xt.Initialized;const{draggable:{active:A,nodes:I,translate:L},droppable:{containers:P}}=C;const j=A!=null?I.get(A):null;const B=(0,r.useRef)({initial:null,translated:null});const z=(0,r.useMemo)((()=>{var e;return A!=null?{id:A,data:(e=j==null?void 0:j.data)!=null?e:at,rect:B}:null}),[A,j]);const Y=(0,r.useRef)(null);const[V,U]=(0,r.useState)(null);const[H,X]=(0,r.useState)(null);const q=(0,i.Ey)(D,Object.values(D));const Z=(0,i.Ld)("DndDescribedBy",u);const J=(0,r.useMemo)((()=>P.getEnabled()),[P]);const G=bt(w);const{droppableRects:$,measureDroppableContainers:K,measuringScheduled:Q}=ze(J,{dragging:R,dependencies:[L.x,L.y],config:G.droppable});const ee=Le(I,A);const te=(0,r.useMemo)((()=>H?(0,i.DC)(H):null),[H]);const ne=We();const re=Ve(ee,G.draggable.measure);yt({activeNode:A!=null?I.get(A):null,config:ne.layoutShiftCompensation,initialRect:re,measure:G.draggable.measure});const oe=qe(ee,G.draggable.measure,re);const se=qe(ee?ee.parentElement:null);const ie=(0,r.useRef)({activatorEvent:null,active:null,activeNode:ee,collisionRect:null,collisions:null,droppableRects:$,draggableNodes:I,draggingNode:null,draggingNodeRect:null,droppableContainers:P,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null});const ae=P.getNodeFor((n=ie.current.over)==null?void 0:n.id);const ce=st({measure:G.dragOverlay.measure});const le=(a=ce.nodeRef.current)!=null?a:ee;const ue=R?(c=ce.rect)!=null?c:oe:null;const de=Boolean(ce.nodeRef.current&&ce.rect);const fe=Ze(de?null:oe);const he=tt(le?(0,i.Jj)(le):null);const pe=Ge(R?ae!=null?ae:ee:null);const ge=rt(pe);const me=vt(x,{transform:{x:L.x-fe.x,y:L.y-fe.y,scaleX:1,scaleY:1},activatorEvent:H,active:z,activeNodeRect:oe,containerNodeRect:se,draggingNodeRect:ue,over:ie.current.over,overlayNodeRect:ce.rect,scrollableAncestors:pe,scrollableAncestorRects:ge,windowRect:he});const ve=te?(0,i.IH)(te,L):null;const be=$e(pe);const ye=Ke(be);const we=Ke(be,[oe]);const xe=(0,i.IH)(me,ye);const De=ue?W(ue,me):null;const ke=z&&De?y({active:z,collisionRect:De,droppableRects:$,droppableContainers:J,pointerCoordinates:ve}):null;const Ce=_(ke,"id");const[Se,Me]=(0,r.useState)(null);const _e=de?me:(0,i.IH)(me,we);const Ee=F(_e,(l=Se==null?void 0:Se.rect)!=null?l:null,oe);const Ne=(0,r.useRef)(null);const Oe=(0,r.useCallback)(((e,t)=>{let{sensor:n,options:r}=t;if(Y.current==null){return}const o=I.get(Y.current);if(!o){return}const i=e.nativeEvent;const a=new n({active:Y.current,activeNode:o,event:i,options:r,context:ie,onAbort(e){const t=I.get(e);if(!t){return}const{onDragAbort:n}=q.current;const r={id:e};n==null?void 0:n(r);M({type:"onDragAbort",event:r})},onPending(e,t,n,r){const o=I.get(e);if(!o){return}const{onDragPending:s}=q.current;const i={id:e,constraint:t,initialCoordinates:n,offset:r};s==null?void 0:s(i);M({type:"onDragPending",event:i})},onStart(e){const t=Y.current;if(t==null){return}const n=I.get(t);if(!n){return}const{onDragStart:r}=q.current;const o={activatorEvent:i,active:{id:t,data:n.data,rect:B}};(0,s.unstable_batchedUpdates)((()=>{r==null?void 0:r(o);O(xt.Initializing);S({type:v.DragStart,initialCoordinates:e,active:t});M({type:"onDragStart",event:o});U(Ne.current);X(i)}))},onMove(e){S({type:v.DragMove,coordinates:e})},onEnd:c(v.DragEnd),onCancel:c(v.DragCancel)});Ne.current=a;function c(e){return async function t(){const{active:n,collisions:r,over:o,scrollAdjustedTranslate:a}=ie.current;let c=null;if(n&&a){const{cancelDrop:t}=q.current;c={activatorEvent:i,active:n,collisions:r,delta:a,over:o};if(e===v.DragEnd&&typeof t==="function"){const n=await Promise.resolve(t(c));if(n){e=v.DragCancel}}}Y.current=null;(0,s.unstable_batchedUpdates)((()=>{S({type:e});O(xt.Uninitialized);Me(null);U(null);X(null);Ne.current=null;const t=e===v.DragEnd?"onDragEnd":"onDragCancel";if(c){const e=q.current[t];e==null?void 0:e(c);M({type:t,event:c})}}))}}}),[I]);const Re=(0,r.useCallback)(((e,t)=>(n,r)=>{const o=n.nativeEvent;const s=I.get(r);if(Y.current!==null||!s||o.dndKit||o.defaultPrevented){return}const i={active:s};const a=e(n,t.options,i);if(a===true){o.dndKit={capturedBy:t.sensor};Y.current=r;Oe(n,t)}}),[I,Oe]);const Te=Pe(b,Re);Qe(b);(0,i.LI)((()=>{if(oe&&N===xt.Initializing){O(xt.Initialized)}}),[oe,N]);(0,r.useEffect)((()=>{const{onDragMove:e}=q.current;const{active:t,activatorEvent:n,collisions:r,over:o}=ie.current;if(!t||!n){return}const i={active:t,activatorEvent:n,collisions:r,delta:{x:xe.x,y:xe.y},over:o};(0,s.unstable_batchedUpdates)((()=>{e==null?void 0:e(i);M({type:"onDragMove",event:i})}))}),[xe.x,xe.y]);(0,r.useEffect)((()=>{const{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:o}=ie.current;if(!e||Y.current==null||!t||!o){return}const{onDragOver:i}=q.current;const a=r.get(Ce);const c=a&&a.rect.current?{id:a.id,rect:a.rect.current,data:a.data,disabled:a.disabled}:null;const l={active:e,activatorEvent:t,collisions:n,delta:{x:o.x,y:o.y},over:c};(0,s.unstable_batchedUpdates)((()=>{Me(c);i==null?void 0:i(l);M({type:"onDragOver",event:l})}))}),[Ce]);(0,i.LI)((()=>{ie.current={activatorEvent:H,active:z,activeNode:ee,collisionRect:De,collisions:ke,droppableRects:$,draggableNodes:I,draggingNode:le,draggingNodeRect:ue,droppableContainers:P,over:Se,scrollableAncestors:pe,scrollAdjustedTranslate:xe};B.current={initial:ue,translated:De}}),[z,ee,ke,De,I,le,ue,$,P,Se,pe,xe]);Ae({...ne,delta:L,draggingRect:De,pointerCoordinates:ve,scrollableAncestors:pe,scrollableAncestorRects:ge});const Ie=(0,r.useMemo)((()=>{const e={active:z,activeNode:ee,activeNodeRect:oe,activatorEvent:H,collisions:ke,containerNodeRect:se,dragOverlay:ce,draggableNodes:I,droppableContainers:P,droppableRects:$,over:Se,measureDroppableContainers:K,scrollableAncestors:pe,scrollableAncestorRects:ge,measuringConfiguration:G,measuringScheduled:Q,windowRect:he};return e}),[z,ee,oe,H,ke,se,ce,I,P,$,Se,K,pe,ge,G,Q,he]);const Fe=(0,r.useMemo)((()=>{const e={activatorEvent:H,activators:Te,active:z,activeNodeRect:oe,ariaDescribedById:{draggable:Z},dispatch:S,draggableNodes:I,over:Se,measureDroppableContainers:K};return e}),[H,Te,z,oe,S,Z,I,Se,K]);return o().createElement(d.Provider,{value:E},o().createElement(ft.Provider,{value:Fe},o().createElement(ht.Provider,{value:Ie},o().createElement(wt.Provider,{value:Ee},g)),o().createElement(mt,{disabled:(f==null?void 0:f.restoreFocus)===false})),o().createElement(m,{...f,hiddenTextDescribedById:Z}));function We(){const e=(V==null?void 0:V.autoScrollEnabled)===false;const t=typeof p==="object"?p.enabled===false:p===false;const n=R&&!e&&!t;if(typeof p==="object"){return{...p,enabled:n}}return{enabled:n}}}));const kt=(0,r.createContext)(null);const Ct="button";const St="Draggable";function Mt(e){let{id:t,data:n,disabled:o=false,attributes:s}=e;const a=(0,i.Ld)(St);const{activators:c,activatorEvent:l,active:u,activeNodeRect:d,ariaDescribedById:f,draggableNodes:h,over:p}=(0,r.useContext)(ft);const{role:g=Ct,roleDescription:m="draggable",tabIndex:v=0}=s!=null?s:{};const b=(u==null?void 0:u.id)===t;const y=(0,r.useContext)(b?wt:kt);const[w,x]=(0,i.wm)();const[D,k]=(0,i.wm)();const C=et(c,t);const S=(0,i.Ey)(n);(0,i.LI)((()=>{h.set(t,{id:t,key:a,node:w,activatorNode:D,data:S});return()=>{const e=h.get(t);if(e&&e.key===a){h.delete(t)}}}),[h,t]);const M=(0,r.useMemo)((()=>({role:g,tabIndex:v,"aria-disabled":o,"aria-pressed":b&&g===Ct?true:undefined,"aria-roledescription":m,"aria-describedby":f.draggable})),[o,g,v,b,m,f.draggable]);return{active:u,activatorEvent:l,activeNodeRect:d,attributes:M,isDragging:b,listeners:o?undefined:C,node:w,over:p,setNodeRef:x,setActivatorNodeRef:k,transform:y}}function _t(){return(0,r.useContext)(ht)}const Et="Droppable";const Nt={timeout:25};function Ot(e){let{data:t,disabled:n=false,id:o,resizeObserverConfig:s}=e;const a=(0,i.Ld)(Et);const{active:c,dispatch:l,over:u,measureDroppableContainers:d}=(0,r.useContext)(ft);const f=(0,r.useRef)({disabled:n});const h=(0,r.useRef)(false);const p=(0,r.useRef)(null);const g=(0,r.useRef)(null);const{disabled:m,updateMeasurementsFor:b,timeout:y}={...Nt,...s};const w=(0,i.Ey)(b!=null?b:o);const x=(0,r.useCallback)((()=>{if(!h.current){h.current=true;return}if(g.current!=null){clearTimeout(g.current)}g.current=setTimeout((()=>{d(Array.isArray(w.current)?w.current:[w.current]);g.current=null}),y)}),[y]);const D=He({callback:x,disabled:m||!c});const k=(0,r.useCallback)(((e,t)=>{if(!D){return}if(t){D.unobserve(t);h.current=false}if(e){D.observe(e)}}),[D]);const[C,S]=(0,i.wm)(k);const M=(0,i.Ey)(t);(0,r.useEffect)((()=>{if(!D||!C.current){return}D.disconnect();h.current=false;D.observe(C.current)}),[C,D]);(0,r.useEffect)((()=>{l({type:v.RegisterDroppable,element:{id:o,key:a,disabled:n,node:C,rect:p,data:M}});return()=>l({type:v.UnregisterDroppable,key:a,id:o})}),[o]);(0,r.useEffect)((()=>{if(n!==f.current.disabled){l({type:v.SetDroppableDisabled,id:o,key:a,disabled:n});f.current.disabled=n}}),[o,a,n,l]);return{active:c,rect:p,isOver:(u==null?void 0:u.id)===o,node:C,over:u,setNodeRef:S}}function Rt(e){let{animation:t,children:n}=e;const[s,a]=(0,r.useState)(null);const[c,l]=(0,r.useState)(null);const u=(0,i.D9)(n);if(!n&&!s&&u){a(u)}(0,i.LI)((()=>{if(!c){return}const e=s==null?void 0:s.key;const n=s==null?void 0:s.props.id;if(e==null||n==null){a(null);return}Promise.resolve(t(n,c)).then((()=>{a(null)}))}),[t,s,c]);return o().createElement(o().Fragment,null,n,s?(0,r.cloneElement)(s,{ref:l}):null)}const Tt={x:0,y:0,scaleX:1,scaleY:1};function At(e){let{children:t}=e;return o().createElement(ft.Provider,{value:dt},o().createElement(wt.Provider,{value:Tt},t))}const It={position:"fixed",touchAction:"none"};const Ft=e=>{const t=(0,i.vd)(e);return t?"transform 250ms ease":undefined};const Lt=(0,r.forwardRef)(((e,t)=>{let{as:n,activatorEvent:r,adjustScale:s,children:a,className:c,rect:l,style:u,transform:d,transition:f=Ft}=e;if(!l){return null}const h=s?d:{...d,scaleX:1,scaleY:1};const p={...It,width:l.width,height:l.height,top:l.top,left:l.left,transform:i.ux.Transform.toString(h),transformOrigin:s&&r?k(r,l):undefined,transition:typeof f==="function"?f(r):f,...u};return o().createElement(n,{className:c,style:p,ref:t},a)}));const Pt=e=>t=>{let{active:n,dragOverlay:r}=t;const o={};const{styles:s,className:i}=e;if(s!=null&&s.active){for(const[e,t]of Object.entries(s.active)){if(t===undefined){continue}o[e]=n.node.style.getPropertyValue(e);n.node.style.setProperty(e,t)}}if(s!=null&&s.dragOverlay){for(const[e,t]of Object.entries(s.dragOverlay)){if(t===undefined){continue}r.node.style.setProperty(e,t)}}if(i!=null&&i.active){n.node.classList.add(i.active)}if(i!=null&&i.dragOverlay){r.node.classList.add(i.dragOverlay)}return function e(){for(const[e,t]of Object.entries(o)){n.node.style.setProperty(e,t)}if(i!=null&&i.active){n.node.classList.remove(i.active)}}};const Wt=e=>{let{transform:{initial:t,final:n}}=e;return[{transform:i.ux.Transform.toString(t)},{transform:i.ux.Transform.toString(n)}]};const jt={duration:250,easing:"ease",keyframes:Wt,sideEffects:Pt({styles:{active:{opacity:"0"}}})};function Bt(e){let{config:t,draggableNodes:n,droppableContainers:r,measuringConfiguration:o}=e;return(0,i.zX)(((e,s)=>{if(t===null){return}const a=n.get(e);if(!a){return}const c=a.node.current;if(!c){return}const l=ot(s);if(!l){return}const{transform:u}=(0,i.Jj)(s).getComputedStyle(s);const d=j(u);if(!d){return}const f=typeof t==="function"?t:zt(t);ce(c,o.draggable.measure);return f({active:{id:e,data:a.data,node:c,rect:o.draggable.measure(c)},draggableNodes:n,dragOverlay:{node:s,rect:o.dragOverlay.measure(l)},droppableContainers:r,measuringConfiguration:o,transform:d})}))}function zt(e){const{duration:t,easing:n,sideEffects:r,keyframes:o}={...jt,...e};return e=>{let{active:s,dragOverlay:i,transform:a,...c}=e;if(!t){return}const l={x:i.rect.left-s.rect.left,y:i.rect.top-s.rect.top};const u={scaleX:a.scaleX!==1?s.rect.width*a.scaleX/i.rect.width:1,scaleY:a.scaleY!==1?s.rect.height*a.scaleY/i.rect.height:1};const d={x:a.x-l.x,y:a.y-l.y,...u};const f=o({...c,active:s,dragOverlay:i,transform:{initial:a,final:d}});const[h]=f;const p=f[f.length-1];if(JSON.stringify(h)===JSON.stringify(p)){return}const g=r==null?void 0:r({active:s,dragOverlay:i,...c});const m=i.node.animate(f,{duration:t,easing:n,fill:"forwards"});return new Promise((e=>{m.onfinish=()=>{g==null?void 0:g();e()}}))}}let Yt=0;function Vt(e){return(0,r.useMemo)((()=>{if(e==null){return}Yt++;return Yt}),[e])}const Ut=o().memo((e=>{let{adjustScale:t=false,children:n,dropAnimation:s,style:i,transition:a,modifiers:c,wrapperElement:l="div",className:u,zIndex:d=999}=e;const{activatorEvent:f,active:h,activeNodeRect:p,containerNodeRect:g,draggableNodes:m,droppableContainers:v,dragOverlay:b,over:y,measuringConfiguration:w,scrollableAncestors:x,scrollableAncestorRects:D,windowRect:k}=_t();const C=(0,r.useContext)(wt);const S=Vt(h==null?void 0:h.id);const M=vt(c,{activatorEvent:f,active:h,activeNodeRect:p,containerNodeRect:g,draggingNodeRect:b.rect,over:y,overlayNodeRect:b.rect,scrollableAncestors:x,scrollableAncestorRects:D,transform:C,windowRect:k});const _=Ye(p);const E=Bt({config:s,draggableNodes:m,droppableContainers:v,measuringConfiguration:w});const N=_?b.setRef:undefined;return o().createElement(At,null,o().createElement(Rt,{animation:E},h&&S?o().createElement(Lt,{key:S,id:h.id,ref:N,as:l,activatorEvent:f,adjustScale:t,className:u,transition:a,rect:_,style:{zIndex:d,...i},transform:M},n):null))}))},5253:(e,t,n)=>{n.d(t,{hg:()=>u});var r=n(3618);function o(e){return t=>{let{transform:n}=t;return{...n,x:Math.ceil(n.x/e)*e,y:Math.ceil(n.y/e)*e}}}const s=e=>{let{transform:t}=e;return{...t,y:0}};function i(e,t,n){const r={...e};if(t.top+e.y<=n.top){r.y=n.top-t.top}else if(t.bottom+e.y>=n.top+n.height){r.y=n.top+n.height-t.bottom}if(t.left+e.x<=n.left){r.x=n.left-t.left}else if(t.right+e.x>=n.left+n.width){r.x=n.left+n.width-t.right}return r}const a=e=>{let{containerNodeRect:t,draggingNodeRect:n,transform:r}=e;if(!n||!t){return r}return i(r,n,t)};const c=e=>{let{draggingNodeRect:t,transform:n,scrollableAncestorRects:r}=e;const o=r[0];if(!t||!o){return n}return i(n,t,o)};const l=e=>{let{transform:t}=e;return{...t,x:0}};const u=e=>{let{transform:t,draggingNodeRect:n,windowRect:r}=e;if(!n||!r){return t}return i(t,n,r)};const d=e=>{let{activatorEvent:t,draggingNodeRect:n,transform:r}=e;if(n&&t){const e=getEventCoordinates(t);if(!e){return r}const o=e.x-n.left;const s=e.y-n.top;return{...r,x:r.x+o-n.width/2,y:r.y+s-n.height/2}}return r}},7274:(e,t,n)=>{n.d(t,{Fo:()=>k,cP:()=>S,is:()=>F,nB:()=>R,qw:()=>y});var r=n(7363);var o=n.n(r);var s=n(5298);var i=n(3618);function a(e,t,n){const r=e.slice();r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]);return r}function c(e,t,n){const r=e.slice();r[t]=e[n];r[n]=e[t];return r}function l(e,t){return e.reduce(((e,n,r)=>{const o=t.get(n);if(o){e[r]=o}return e}),Array(e.length))}function u(e){return e!==null&&e>=0}function d(e,t){if(e===t){return true}if(e.length!==t.length){return false}for(let n=0;n<e.length;n++){if(e[n]!==t[n]){return false}}return true}function f(e){if(typeof e==="boolean"){return{draggable:e,droppable:e}}return e}const h={scaleX:1,scaleY:1};const p=e=>{var t;let{rects:n,activeNodeRect:r,activeIndex:o,overIndex:s,index:i}=e;const a=(t=n[o])!=null?t:r;if(!a){return null}const c=g(n,i,o);if(i===o){const e=n[s];if(!e){return null}return{x:o<s?e.left+e.width-(a.left+a.width):e.left-a.left,y:0,...h}}if(i>o&&i<=s){return{x:-a.width-c,y:0,...h}}if(i<o&&i>=s){return{x:a.width+c,y:0,...h}}return{x:0,y:0,...h}};function g(e,t,n){const r=e[t];const o=e[t-1];const s=e[t+1];if(!r||!o&&!s){return 0}if(n<t){return o?r.left-(o.left+o.width):s.left-(r.left+r.width)}return s?s.left-(r.left+r.width):r.left-(o.left+o.width)}const m=e=>{let{rects:t,activeIndex:n,overIndex:r,index:o}=e;const s=a(t,r,n);const i=t[o];const c=s[o];if(!c||!i){return null}return{x:c.left-i.left,y:c.top-i.top,scaleX:c.width/i.width,scaleY:c.height/i.height}};const v=e=>{let{activeIndex:t,index:n,rects:r,overIndex:o}=e;let s;let i;if(n===t){s=r[n];i=r[o]}if(n===o){s=r[n];i=r[t]}if(!i||!s){return null}return{x:i.left-s.left,y:i.top-s.top,scaleX:i.width/s.width,scaleY:i.height/s.height}};const b={scaleX:1,scaleY:1};const y=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:o,rects:s,overIndex:i}=e;const a=(t=s[n])!=null?t:r;if(!a){return null}if(o===n){const e=s[i];if(!e){return null}return{x:0,y:n<i?e.top+e.height-(a.top+a.height):e.top-a.top,...b}}const c=w(s,o,n);if(o>n&&o<=i){return{x:0,y:-a.height-c,...b}}if(o<n&&o>=i){return{x:0,y:a.height+c,...b}}return{x:0,y:0,...b}};function w(e,t,n){const r=e[t];const o=e[t-1];const s=e[t+1];if(!r){return 0}if(n<t){return o?r.top-(o.top+o.height):s?s.top-(r.top+r.height):0}return s?s.top-(r.top+r.height):o?r.top-(o.top+o.height):0}const x="Sortable";const D=o().createContext({activeIndex:-1,containerId:x,disableTransforms:false,items:[],overIndex:-1,useDragOverlay:false,sortedRects:[],strategy:m,disabled:{draggable:false,droppable:false}});function k(e){let{children:t,id:n,items:a,strategy:c=m,disabled:u=false}=e;const{active:h,dragOverlay:p,droppableRects:g,over:v,measureDroppableContainers:b}=(0,s.Cj)();const y=(0,i.Ld)(x,n);const w=Boolean(p.rect!==null);const k=(0,r.useMemo)((()=>a.map((e=>typeof e==="object"&&"id"in e?e.id:e))),[a]);const C=h!=null;const S=h?k.indexOf(h.id):-1;const M=v?k.indexOf(v.id):-1;const _=(0,r.useRef)(k);const E=!d(k,_.current);const N=M!==-1&&S===-1||E;const O=f(u);(0,i.LI)((()=>{if(E&&C){b(k)}}),[E,k,C,b]);(0,r.useEffect)((()=>{_.current=k}),[k]);const R=(0,r.useMemo)((()=>({activeIndex:S,containerId:y,disabled:O,disableTransforms:N,items:k,overIndex:M,useDragOverlay:w,sortedRects:l(k,g),strategy:c})),[S,y,O.draggable,O.droppable,N,k,M,g,w,c]);return o().createElement(D.Provider,{value:R},t)}const C=e=>{let{id:t,items:n,activeIndex:r,overIndex:o}=e;return a(n,r,o).indexOf(t)};const S=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:o,items:s,newIndex:i,previousItems:a,previousContainerId:c,transition:l}=e;if(!l||!r){return false}if(a!==s&&o===i){return false}if(n){return true}return i!==o&&t===c};const M={duration:200,easing:"ease"};const _="transform";const E=i.ux.Transition.toString({property:_,duration:0,easing:"linear"});const N={roleDescription:"sortable"};function O(e){let{disabled:t,index:n,node:o,rect:a}=e;const[c,l]=(0,r.useState)(null);const u=(0,r.useRef)(n);(0,i.LI)((()=>{if(!t&&n!==u.current&&o.current){const e=a.current;if(e){const t=(0,s.VK)(o.current,{ignoreTransform:true});const n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};if(n.x||n.y){l(n)}}}if(n!==u.current){u.current=n}}),[t,n,o,a]);(0,r.useEffect)((()=>{if(c){l(null)}}),[c]);return c}function R(e){let{animateLayoutChanges:t=S,attributes:n,disabled:o,data:a,getNewIndex:c=C,id:l,strategy:d,resizeObserverConfig:f,transition:h=M}=e;const{items:p,containerId:g,activeIndex:m,disabled:v,disableTransforms:b,sortedRects:y,overIndex:w,useDragOverlay:x,strategy:k}=(0,r.useContext)(D);const R=T(o,v);const A=p.indexOf(l);const I=(0,r.useMemo)((()=>({sortable:{containerId:g,index:A,items:p},...a})),[g,a,A,p]);const F=(0,r.useMemo)((()=>p.slice(p.indexOf(l))),[p,l]);const{rect:L,node:P,isOver:W,setNodeRef:j}=(0,s.Zj)({id:l,data:I,disabled:R.droppable,resizeObserverConfig:{updateMeasurementsFor:F,...f}});const{active:B,activatorEvent:z,activeNodeRect:Y,attributes:V,setNodeRef:U,listeners:H,isDragging:X,over:q,setActivatorNodeRef:Z,transform:J}=(0,s.O1)({id:l,data:I,attributes:{...N,...n},disabled:R.draggable});const G=(0,i.HB)(j,U);const $=Boolean(B);const K=$&&!b&&u(m)&&u(w);const Q=!x&&X;const ee=Q&&K?J:null;const te=d!=null?d:k;const ne=K?ee!=null?ee:te({rects:y,activeNodeRect:Y,activeIndex:m,overIndex:w,index:A}):null;const re=u(m)&&u(w)?c({id:l,items:p,activeIndex:m,overIndex:w}):A;const oe=B==null?void 0:B.id;const se=(0,r.useRef)({activeId:oe,items:p,newIndex:re,containerId:g});const ie=p!==se.current.items;const ae=t({active:B,containerId:g,isDragging:X,isSorting:$,id:l,index:A,items:p,newIndex:se.current.newIndex,previousItems:se.current.items,previousContainerId:se.current.containerId,transition:h,wasDragging:se.current.activeId!=null});const ce=O({disabled:!ae,index:A,node:P,rect:L});(0,r.useEffect)((()=>{if($&&se.current.newIndex!==re){se.current.newIndex=re}if(g!==se.current.containerId){se.current.containerId=g}if(p!==se.current.items){se.current.items=p}}),[$,re,g,p]);(0,r.useEffect)((()=>{if(oe===se.current.activeId){return}if(oe&&!se.current.activeId){se.current.activeId=oe;return}const e=setTimeout((()=>{se.current.activeId=oe}),50);return()=>clearTimeout(e)}),[oe]);return{active:B,activeIndex:m,attributes:V,data:I,rect:L,index:A,newIndex:re,items:p,isOver:W,isSorting:$,isDragging:X,listeners:H,node:P,overIndex:w,over:q,setNodeRef:G,setActivatorNodeRef:Z,setDroppableNodeRef:j,setDraggableNodeRef:U,transform:ce!=null?ce:ne,transition:le()};function le(){if(ce||ie&&se.current.newIndex===A){return E}if(Q&&!(0,i.vd)(z)||!h){return undefined}if($||ae){return i.ux.Transition.toString({...h,property:_})}return undefined}}function T(e,t){var n,r;if(typeof e==="boolean"){return{draggable:e,droppable:false}}return{draggable:(n=e==null?void 0:e.draggable)!=null?n:t.draggable,droppable:(r=e==null?void 0:e.droppable)!=null?r:t.droppable}}function A(e){if(!e){return false}const t=e.data.current;if(t&&"sortable"in t&&typeof t.sortable==="object"&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable){return true}return false}const I=[s.g4.Down,s.g4.Right,s.g4.Up,s.g4.Left];const F=(e,t)=>{let{context:{active:n,collisionRect:r,droppableRects:o,droppableContainers:a,over:c,scrollableAncestors:l}}=t;if(I.includes(e.code)){e.preventDefault();if(!n||!r){return}const t=[];a.getEnabled().forEach((n=>{if(!n||n!=null&&n.disabled){return}const i=o.get(n.id);if(!i){return}switch(e.code){case s.g4.Down:if(r.top<i.top){t.push(n)}break;case s.g4.Up:if(r.top>i.top){t.push(n)}break;case s.g4.Left:if(r.left>i.left){t.push(n)}break;case s.g4.Right:if(r.left<i.left){t.push(n)}break}}));const u=(0,s.ey)({active:n,collisionRect:r,droppableRects:o,droppableContainers:t,pointerCoordinates:null});let d=(0,s._8)(u,"id");if(d===(c==null?void 0:c.id)&&u.length>1){d=u[1].id}if(d!=null){const e=a.get(n.id);const t=a.get(d);const c=t?o.get(t.id):null;const u=t==null?void 0:t.node.current;if(u&&c&&e&&t){const n=(0,s.hI)(u);const o=n.some(((e,t)=>l[t]!==e));const a=L(e,t);const d=P(e,t);const f=o||!a?{x:0,y:0}:{x:d?r.width-c.width:0,y:d?r.height-c.height:0};const h={x:c.left,y:c.top};const p=f.x&&f.y?h:(0,i.$X)(h,f);return p}}}return undefined};function L(e,t){if(!A(e)||!A(t)){return false}return e.data.current.sortable.containerId===t.data.current.sortable.containerId}function P(e,t){if(!A(e)||!A(t)){return false}if(!L(e,t)){return false}return e.data.current.sortable.index<t.data.current.sortable.index}},3618:(e,t,n)=>{n.d(t,{$X:()=>S,D9:()=>w,DC:()=>N,Ey:()=>v,FJ:()=>a,Gj:()=>b,HB:()=>s,IH:()=>C,Jj:()=>l,LI:()=>p,Ld:()=>D,Nq:()=>i,Re:()=>d,UG:()=>c,Yz:()=>m,qk:()=>u,r3:()=>h,so:()=>T,ux:()=>O,vZ:()=>f,vd:()=>_,wm:()=>y,zX:()=>g});var r=n(7363);var o=n.n(r);function s(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++){t[n]=arguments[n]}return(0,r.useMemo)((()=>e=>{t.forEach((t=>t(e)))}),t)}const i=typeof window!=="undefined"&&typeof window.document!=="undefined"&&typeof window.document.createElement!=="undefined";function a(e){const t=Object.prototype.toString.call(e);return t==="[object Window]"||t==="[object global]"}function c(e){return"nodeType"in e}function l(e){var t,n;if(!e){return window}if(a(e)){return e}if(!c(e)){return window}return(t=(n=e.ownerDocument)==null?void 0:n.defaultView)!=null?t:window}function u(e){const{Document:t}=l(e);return e instanceof t}function d(e){if(a(e)){return false}return e instanceof l(e).HTMLElement}function f(e){return e instanceof l(e).SVGElement}function h(e){if(!e){return document}if(a(e)){return e.document}if(!c(e)){return document}if(u(e)){return e}if(d(e)||f(e)){return e.ownerDocument}return document}const p=i?r.useLayoutEffect:r.useEffect;function g(e){const t=(0,r.useRef)(e);p((()=>{t.current=e}));return(0,r.useCallback)((function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++){n[r]=arguments[r]}return t.current==null?void 0:t.current(...n)}),[])}function m(){const e=(0,r.useRef)(null);const t=(0,r.useCallback)(((t,n)=>{e.current=setInterval(t,n)}),[]);const n=(0,r.useCallback)((()=>{if(e.current!==null){clearInterval(e.current);e.current=null}}),[]);return[t,n]}function v(e,t){if(t===void 0){t=[e]}const n=(0,r.useRef)(e);p((()=>{if(n.current!==e){n.current=e}}),t);return n}function b(e,t){const n=(0,r.useRef)();return(0,r.useMemo)((()=>{const t=e(n.current);n.current=t;return t}),[...t])}function y(e){const t=g(e);const n=(0,r.useRef)(null);const o=(0,r.useCallback)((e=>{if(e!==n.current){t==null?void 0:t(e,n.current)}n.current=e}),[]);return[n,o]}function w(e){const t=(0,r.useRef)();(0,r.useEffect)((()=>{t.current=e}),[e]);return t.current}let x={};function D(e,t){return(0,r.useMemo)((()=>{if(t){return t}const n=x[e]==null?0:x[e]+1;x[e]=n;return e+"-"+n}),[e,t])}function k(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++){r[o-1]=arguments[o]}return r.reduce(((t,n)=>{const r=Object.entries(n);for(const[n,o]of r){const r=t[n];if(r!=null){t[n]=r+e*o}}return t}),{...t})}}const C=k(1);const S=k(-1);function M(e){return"clientX"in e&&"clientY"in e}function _(e){if(!e){return false}const{KeyboardEvent:t}=l(e.target);return t&&e instanceof t}function E(e){if(!e){return false}const{TouchEvent:t}=l(e.target);return t&&e instanceof t}function N(e){if(E(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}else if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}if(M(e)){return{x:e.clientX,y:e.clientY}}return null}const O=Object.freeze({Translate:{toString(e){if(!e){return}const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e){return}const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(!e){return}return[O.Translate.toString(e),O.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}});const R="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function T(e){if(e.matches(R)){return e}return e.querySelector(R)}},7573:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(3152);var o=n(692);var s=n(8198);function i(e){(0,s.Z)(1,arguments);var t=(0,o.Z)(e);t.setSeconds(0,0);return t}function a(e,t){var n;(0,s.Z)(1,arguments);var a=i((0,o.Z)(e.start));var c=(0,o.Z)(e.end);var l=a.getTime();var u=c.getTime();if(l>=u){throw new RangeError("Invalid interval")}var d=[];var f=a;var h=Number((n=t===null||t===void 0?void 0:t.step)!==null&&n!==void 0?n:1);if(h<1||isNaN(h))throw new RangeError("`options.step` must be a number equal to or greater than 1");while(f.getTime()<=u){d.push((0,o.Z)(f));f=(0,r.Z)(f,h)}return d}},7662:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(1907);var o=n(692);var s=n(8198);function i(e,t){(0,s.Z)(2,arguments);var n=(0,o.Z)(e);var i=(0,r.Z)(t);n.setHours(i);return n}},91:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(1907);var o=n(692);var s=n(8198);function i(e,t){(0,s.Z)(2,arguments);var n=(0,o.Z)(e);var i=(0,r.Z)(t);n.setMinutes(i);return n}},9640:(e,t,n)=>{n.d(t,{y:()=>i});var r=n(7363);var o=n(7037);var s=n(3297);"use client";function i(e,t){const n=(0,s.NL)(t);const i=n.getQueryCache();return r.useSyncExternalStore(r.useCallback((e=>i.subscribe(o.V.batchCalls(e))),[i]),(()=>n.isFetching(e)),(()=>n.isFetching(e)))}},460:(e,t,n)=>{n.d(t,{T:()=>c});var r=n(6843);var o=n(2612);function s(e,t,n){return(0,r.L)(n?.in||e,+(0,o.Q)(e)+t)}const i=null&&s;var a=n(6012);function c(e,t,n){return s(e,t*a.vh,n)}const l=null&&c},8901:(e,t,n)=>{n.d(t,{x:()=>i});var r=n(2612);function o(e,t){const n=(0,r.Q)(e,t?.in);n.setSeconds(0,0);return n}const s=null&&o;function i(e,t){return+o(e)===+o(t)}const a=null&&i},9660:(e,t,n)=>{n.d(t,{Uy:()=>re});var r=Symbol.for("immer-nothing");var o=Symbol.for("immer-draftable");var s=Symbol.for("immer-state");var i=false?0:[];function a(e,...t){if(false){}throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var c=Object.getPrototypeOf;function l(e){return!!e&&!!e[s]}function u(e){if(!e)return false;return f(e)||Array.isArray(e)||!!e[o]||!!e.constructor?.[o]||w(e)||x(e)}var d=Object.prototype.constructor.toString();function f(e){if(!e||typeof e!=="object")return false;const t=c(e);if(t===null){return true}const n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;if(n===Object)return true;return typeof n=="function"&&Function.toString.call(n)===d}function h(e){if(!l(e))a(15,e);return e[s].base_}function p(e,t){if(g(e)===0){Reflect.ownKeys(e).forEach((n=>{t(n,e[n],e)}))}else{e.forEach(((n,r)=>t(r,n,e)))}}function g(e){const t=e[s];return t?t.type_:Array.isArray(e)?1:w(e)?2:x(e)?3:0}function m(e,t){return g(e)===2?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function v(e,t){return g(e)===2?e.get(t):e[t]}function b(e,t,n){const r=g(e);if(r===2)e.set(t,n);else if(r===3){e.add(n)}else e[t]=n}function y(e,t){if(e===t){return e!==0||1/e===1/t}else{return e!==e&&t!==t}}function w(e){return e instanceof Map}function x(e){return e instanceof Set}function D(e){return e.copy_||e.base_}function k(e,t){if(w(e)){return new Map(e)}if(x(e)){return new Set(e)}if(Array.isArray(e))return Array.prototype.slice.call(e);const n=f(e);if(t===true||t==="class_only"&&!n){const t=Object.getOwnPropertyDescriptors(e);delete t[s];let n=Reflect.ownKeys(t);for(let r=0;r<n.length;r++){const o=n[r];const s=t[o];if(s.writable===false){s.writable=true;s.configurable=true}if(s.get||s.set)t[o]={configurable:true,writable:true,enumerable:s.enumerable,value:e[o]}}return Object.create(c(e),t)}else{const t=c(e);if(t!==null&&n){return{...e}}const r=Object.create(t);return Object.assign(r,e)}}function C(e,t=false){if(M(e)||l(e)||!u(e))return e;if(g(e)>1){e.set=e.add=e.clear=e.delete=S}Object.freeze(e);if(t)Object.entries(e).forEach((([e,t])=>C(t,true)));return e}function S(){a(2)}function M(e){return Object.isFrozen(e)}var _={};function E(e){const t=_[e];if(!t){a(0,e)}return t}function N(e,t){if(!_[e])_[e]=t}var O;function R(){return O}function T(e,t){return{drafts_:[],parent_:e,immer_:t,canAutoFreeze_:true,unfinalizedDrafts_:0}}function A(e,t){if(t){E("Patches");e.patches_=[];e.inversePatches_=[];e.patchListener_=t}}function I(e){F(e);e.drafts_.forEach(P);e.drafts_=null}function F(e){if(e===O){O=e.parent_}}function L(e){return O=T(O,e)}function P(e){const t=e[s];if(t.type_===0||t.type_===1)t.revoke_();else t.revoked_=true}function W(e,t){t.unfinalizedDrafts_=t.drafts_.length;const n=t.drafts_[0];const o=e!==void 0&&e!==n;if(o){if(n[s].modified_){I(t);a(4)}if(u(e)){e=j(t,e);if(!t.parent_)z(t,e)}if(t.patches_){E("Patches").generateReplacementPatches_(n[s].base_,e,t.patches_,t.inversePatches_)}}else{e=j(t,n,[])}I(t);if(t.patches_){t.patchListener_(t.patches_,t.inversePatches_)}return e!==r?e:void 0}function j(e,t,n){if(M(t))return t;const r=t[s];if(!r){p(t,((o,s)=>B(e,r,t,o,s,n)));return t}if(r.scope_!==e)return t;if(!r.modified_){z(e,r.base_,true);return r.base_}if(!r.finalized_){r.finalized_=true;r.scope_.unfinalizedDrafts_--;const t=r.copy_;let o=t;let s=false;if(r.type_===3){o=new Set(t);t.clear();s=true}p(o,((o,i)=>B(e,r,t,o,i,n,s)));z(e,t,false);if(n&&e.patches_){E("Patches").generatePatches_(r,n,e.patches_,e.inversePatches_)}}return r.copy_}function B(e,t,n,r,o,s,i){if(false){}if(l(o)){const i=s&&t&&t.type_!==3&&!m(t.assigned_,r)?s.concat(r):void 0;const a=j(e,o,i);b(n,r,a);if(l(a)){e.canAutoFreeze_=false}else return}else if(i){n.add(o)}if(u(o)&&!M(o)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1){return}j(e,o);if((!t||!t.scope_.parent_)&&typeof r!=="symbol"&&Object.prototype.propertyIsEnumerable.call(n,r))z(e,o)}}function z(e,t,n=false){if(!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_){C(t,n)}}function Y(e,t){const n=Array.isArray(e);const r={type_:n?1:0,scope_:t?t.scope_:R(),modified_:false,finalized_:false,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:false};let o=r;let s=V;if(n){o=[r];s=U}const{revoke:i,proxy:a}=Proxy.revocable(o,s);r.draft_=a;r.revoke_=i;return a}var V={get(e,t){if(t===s)return e;const n=D(e);if(!m(n,t)){return X(e,n,t)}const r=n[t];if(e.finalized_||!u(r)){return r}if(r===H(e.base_,t)){J(e);return e.copy_[t]=$(r,e)}return r},has(e,t){return t in D(e)},ownKeys(e){return Reflect.ownKeys(D(e))},set(e,t,n){const r=q(D(e),t);if(r?.set){r.set.call(e.draft_,n);return true}if(!e.modified_){const r=H(D(e),t);const o=r?.[s];if(o&&o.base_===n){e.copy_[t]=n;e.assigned_[t]=false;return true}if(y(n,r)&&(n!==void 0||m(e.base_,t)))return true;J(e);Z(e)}if(e.copy_[t]===n&&(n!==void 0||t in e.copy_)||Number.isNaN(n)&&Number.isNaN(e.copy_[t]))return true;e.copy_[t]=n;e.assigned_[t]=true;return true},deleteProperty(e,t){if(H(e.base_,t)!==void 0||t in e.base_){e.assigned_[t]=false;J(e);Z(e)}else{delete e.assigned_[t]}if(e.copy_){delete e.copy_[t]}return true},getOwnPropertyDescriptor(e,t){const n=D(e);const r=Reflect.getOwnPropertyDescriptor(n,t);if(!r)return r;return{writable:true,configurable:e.type_!==1||t!=="length",enumerable:r.enumerable,value:n[t]}},defineProperty(){a(11)},getPrototypeOf(e){return c(e.base_)},setPrototypeOf(){a(12)}};var U={};p(V,((e,t)=>{U[e]=function(){arguments[0]=arguments[0][0];return t.apply(this,arguments)}}));U.deleteProperty=function(e,t){if(false){}return U.set.call(this,e,t,void 0)};U.set=function(e,t,n){if(false){}return V.set.call(this,e[0],t,n,e[0])};function H(e,t){const n=e[s];const r=n?D(n):e;return r[t]}function X(e,t,n){const r=q(t,n);return r?`value`in r?r.value:r.get?.call(e.draft_):void 0}function q(e,t){if(!(t in e))return void 0;let n=c(e);while(n){const e=Object.getOwnPropertyDescriptor(n,t);if(e)return e;n=c(n)}return void 0}function Z(e){if(!e.modified_){e.modified_=true;if(e.parent_){Z(e.parent_)}}}function J(e){if(!e.copy_){e.copy_=k(e.base_,e.scope_.immer_.useStrictShallowCopy_)}}var G=class{constructor(e){this.autoFreeze_=true;this.useStrictShallowCopy_=false;this.produce=(e,t,n)=>{if(typeof e==="function"&&typeof t!=="function"){const n=t;t=e;const r=this;return function e(o=n,...s){return r.produce(o,(e=>t.call(this,e,...s)))}}if(typeof t!=="function")a(6);if(n!==void 0&&typeof n!=="function")a(7);let o;if(u(e)){const r=L(this);const s=$(e,void 0);let i=true;try{o=t(s);i=false}finally{if(i)I(r);else F(r)}A(r,n);return W(o,r)}else if(!e||typeof e!=="object"){o=t(e);if(o===void 0)o=e;if(o===r)o=void 0;if(this.autoFreeze_)C(o,true);if(n){const t=[];const r=[];E("Patches").generateReplacementPatches_(e,o,t,r);n(t,r)}return o}else a(1,e)};this.produceWithPatches=(e,t)=>{if(typeof e==="function"){return(t,...n)=>this.produceWithPatches(t,(t=>e(t,...n)))}let n,r;const o=this.produce(e,t,((e,t)=>{n=e;r=t}));return[o,n,r]};if(typeof e?.autoFreeze==="boolean")this.setAutoFreeze(e.autoFreeze);if(typeof e?.useStrictShallowCopy==="boolean")this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){if(!u(e))a(8);if(l(e))e=K(e);const t=L(this);const n=$(e,void 0);n[s].isManual_=true;F(t);return n}finishDraft(e,t){const n=e&&e[s];if(!n||!n.isManual_)a(9);const{scope_:r}=n;A(r,t);return W(void 0,r)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let n;for(n=t.length-1;n>=0;n--){const r=t[n];if(r.path.length===0&&r.op==="replace"){e=r.value;break}}if(n>-1){t=t.slice(n+1)}const r=E("Patches").applyPatches_;if(l(e)){return r(e,t)}return this.produce(e,(e=>r(e,t)))}};function $(e,t){const n=w(e)?E("MapSet").proxyMap_(e,t):x(e)?E("MapSet").proxySet_(e,t):Y(e,t);const r=t?t.scope_:R();r.drafts_.push(n);return n}function K(e){if(!l(e))a(10,e);return Q(e)}function Q(e){if(!u(e)||M(e))return e;const t=e[s];let n;if(t){if(!t.modified_)return t.base_;t.finalized_=true;n=k(e,t.scope_.immer_.useStrictShallowCopy_)}else{n=k(e,true)}p(n,((e,t)=>{b(n,e,Q(t))}));if(t){t.finalized_=false}return n}function ee(){const e=16;if(false){}const t="replace";const n="add";const s="remove";function i(e,t,n,r){switch(e.type_){case 0:case 2:return f(e,t,n,r);case 1:return d(e,t,n,r);case 3:return h(e,t,n,r)}}function d(e,r,o,i){let{base_:a,assigned_:c}=e;let l=e.copy_;if(l.length<a.length){[a,l]=[l,a];[o,i]=[i,o]}for(let e=0;e<a.length;e++){if(c[e]&&l[e]!==a[e]){const n=r.concat([e]);o.push({op:t,path:n,value:k(l[e])});i.push({op:t,path:n,value:k(a[e])})}}for(let e=a.length;e<l.length;e++){const t=r.concat([e]);o.push({op:n,path:t,value:k(l[e])})}for(let e=l.length-1;a.length<=e;--e){const t=r.concat([e]);i.push({op:s,path:t})}}function f(e,r,o,i){const{base_:a,copy_:c}=e;p(e.assigned_,((e,l)=>{const u=v(a,e);const d=v(c,e);const f=!l?s:m(a,e)?t:n;if(u===d&&f===t)return;const h=r.concat(e);o.push(f===s?{op:f,path:h}:{op:f,path:h,value:d});i.push(f===n?{op:s,path:h}:f===s?{op:n,path:h,value:k(u)}:{op:t,path:h,value:k(u)})}))}function h(e,t,r,o){let{base_:i,copy_:a}=e;let c=0;i.forEach((e=>{if(!a.has(e)){const i=t.concat([c]);r.push({op:s,path:i,value:e});o.unshift({op:n,path:i,value:e})}c++}));c=0;a.forEach((e=>{if(!i.has(e)){const i=t.concat([c]);r.push({op:n,path:i,value:e});o.unshift({op:s,path:i,value:e})}c++}))}function b(e,n,o,s){o.push({op:t,path:[],value:n===r?void 0:n});s.push({op:t,path:[],value:e})}function y(r,o){o.forEach((o=>{const{path:i,op:c}=o;let l=r;for(let t=0;t<i.length-1;t++){const n=g(l);let r=i[t];if(typeof r!=="string"&&typeof r!=="number"){r=""+r}if((n===0||n===1)&&(r==="__proto__"||r==="constructor"))a(e+3);if(typeof l==="function"&&r==="prototype")a(e+3);l=v(l,r);if(typeof l!=="object")a(e+2,i.join("/"))}const u=g(l);const d=D(o.value);const f=i[i.length-1];switch(c){case t:switch(u){case 2:return l.set(f,d);case 3:a(e);default:return l[f]=d}case n:switch(u){case 1:return f==="-"?l.push(d):l.splice(f,0,d);case 2:return l.set(f,d);case 3:return l.add(d);default:return l[f]=d}case s:switch(u){case 1:return l.splice(f,1);case 2:return l.delete(f);case 3:return l.delete(o.value);default:return delete l[f]}default:a(e+1,c)}}));return r}function D(e){if(!u(e))return e;if(Array.isArray(e))return e.map(D);if(w(e))return new Map(Array.from(e.entries()).map((([e,t])=>[e,D(t)])));if(x(e))return new Set(Array.from(e).map(D));const t=Object.create(c(e));for(const n in e)t[n]=D(e[n]);if(m(e,o))t[o]=e[o];return t}function k(e){if(l(e)){return D(e)}else return e}N("Patches",{applyPatches_:y,generatePatches_:i,generateReplacementPatches_:b})}function te(){class e extends Map{constructor(e,t){super();this[s]={type_:2,parent_:t,scope_:t?t.scope_:R(),modified_:false,finalized_:false,copy_:void 0,assigned_:void 0,base_:e,draft_:this,isManual_:false,revoked_:false}}get size(){return D(this[s]).size}has(e){return D(this[s]).has(e)}set(e,t){const r=this[s];c(r);if(!D(r).has(e)||D(r).get(e)!==t){n(r);Z(r);r.assigned_.set(e,true);r.copy_.set(e,t);r.assigned_.set(e,true)}return this}delete(e){if(!this.has(e)){return false}const t=this[s];c(t);n(t);Z(t);if(t.base_.has(e)){t.assigned_.set(e,false)}else{t.assigned_.delete(e)}t.copy_.delete(e);return true}clear(){const e=this[s];c(e);if(D(e).size){n(e);Z(e);e.assigned_=new Map;p(e.base_,(t=>{e.assigned_.set(t,false)}));e.copy_.clear()}}forEach(e,t){const n=this[s];D(n).forEach(((n,r,o)=>{e.call(t,this.get(r),r,this)}))}get(e){const t=this[s];c(t);const r=D(t).get(e);if(t.finalized_||!u(r)){return r}if(r!==t.base_.get(e)){return r}const o=$(r,t);n(t);t.copy_.set(e,o);return o}keys(){return D(this[s]).keys()}values(){const e=this.keys();return{[Symbol.iterator]:()=>this.values(),next:()=>{const t=e.next();if(t.done)return t;const n=this.get(t.value);return{done:false,value:n}}}}entries(){const e=this.keys();return{[Symbol.iterator]:()=>this.entries(),next:()=>{const t=e.next();if(t.done)return t;const n=this.get(t.value);return{done:false,value:[t.value,n]}}}}[(s,Symbol.iterator)](){return this.entries()}}function t(t,n){return new e(t,n)}function n(e){if(!e.copy_){e.assigned_=new Map;e.copy_=new Map(e.base_)}}class r extends Set{constructor(e,t){super();this[s]={type_:3,parent_:t,scope_:t?t.scope_:R(),modified_:false,finalized_:false,copy_:void 0,base_:e,draft_:this,drafts_:new Map,revoked_:false,isManual_:false}}get size(){return D(this[s]).size}has(e){const t=this[s];c(t);if(!t.copy_){return t.base_.has(e)}if(t.copy_.has(e))return true;if(t.drafts_.has(e)&&t.copy_.has(t.drafts_.get(e)))return true;return false}add(e){const t=this[s];c(t);if(!this.has(e)){i(t);Z(t);t.copy_.add(e)}return this}delete(e){if(!this.has(e)){return false}const t=this[s];c(t);i(t);Z(t);return t.copy_.delete(e)||(t.drafts_.has(e)?t.copy_.delete(t.drafts_.get(e)):false)}clear(){const e=this[s];c(e);if(D(e).size){i(e);Z(e);e.copy_.clear()}}values(){const e=this[s];c(e);i(e);return e.copy_.values()}entries(){const e=this[s];c(e);i(e);return e.copy_.entries()}keys(){return this.values()}[(s,Symbol.iterator)](){return this.values()}forEach(e,t){const n=this.values();let r=n.next();while(!r.done){e.call(t,r.value,r.value,this);r=n.next()}}}function o(e,t){return new r(e,t)}function i(e){if(!e.copy_){e.copy_=new Set;e.base_.forEach((t=>{if(u(t)){const n=$(t,e);e.drafts_.set(t,n);e.copy_.add(n)}else{e.copy_.add(t)}}))}}function c(e){if(e.revoked_)a(3,JSON.stringify(D(e)))}N("MapSet",{proxyMap_:t,proxySet_:o})}var ne=new G;var re=ne.produce;var oe=ne.produceWithPatches.bind(ne);var se=ne.setAutoFreeze.bind(ne);var ie=ne.setUseStrictShallowCopy.bind(ne);var ae=ne.applyPatches.bind(ne);var ce=ne.createDraft.bind(ne);var le=ne.finishDraft.bind(ne);function ue(e){return e}function de(e){return e}},8640:(e,t,n)=>{n.d(t,{_W:()=>To});var r={};n.r(r);n.d(r,{Button:()=>Un,CaptionLabel:()=>Hn,Chevron:()=>Xn,Day:()=>qn,DayButton:()=>Zn,Dropdown:()=>Jn,DropdownNav:()=>Gn,Footer:()=>$n,Month:()=>Kn,MonthCaption:()=>Qn,MonthGrid:()=>er,Months:()=>tr,MonthsDropdown:()=>or,Nav:()=>sr,NextMonthButton:()=>ir,Option:()=>ar,PreviousMonthButton:()=>cr,Root:()=>lr,Select:()=>ur,Week:()=>dr,WeekNumber:()=>pr,WeekNumberHeader:()=>gr,Weekday:()=>fr,Weekdays:()=>hr,Weeks:()=>mr,YearsDropdown:()=>vr});var o={};n.r(o);n.d(o,{formatCaption:()=>xr,formatDay:()=>kr,formatMonthCaption:()=>Dr,formatMonthDropdown:()=>Cr,formatWeekNumber:()=>Sr,formatWeekNumberHeader:()=>Mr,formatWeekdayName:()=>_r,formatYearCaption:()=>Nr,formatYearDropdown:()=>Er});var s={};n.r(s);n.d(s,{labelCaption:()=>Lr,labelDay:()=>jr,labelDayButton:()=>Wr,labelGrid:()=>Fr,labelGridcell:()=>Pr,labelMonthDropdown:()=>zr,labelNav:()=>Br,labelNext:()=>Yr,labelPrevious:()=>Vr,labelWeekNumber:()=>Hr,labelWeekNumberHeader:()=>Xr,labelWeekday:()=>Ur,labelYearDropdown:()=>qr});var i=n(7363);var a;(function(e){e["Root"]="root";e["Chevron"]="chevron";e["Day"]="day";e["DayButton"]="day_button";e["CaptionLabel"]="caption_label";e["Dropdowns"]="dropdowns";e["Dropdown"]="dropdown";e["DropdownRoot"]="dropdown_root";e["Footer"]="footer";e["MonthGrid"]="month_grid";e["MonthCaption"]="month_caption";e["MonthsDropdown"]="months_dropdown";e["Month"]="month";e["Months"]="months";e["Nav"]="nav";e["NextMonthButton"]="button_next";e["PreviousMonthButton"]="button_previous";e["Week"]="week";e["Weeks"]="weeks";e["Weekday"]="weekday";e["Weekdays"]="weekdays";e["WeekNumber"]="week_number";e["WeekNumberHeader"]="week_number_header";e["YearsDropdown"]="years_dropdown"})(a||(a={}));var c;(function(e){e["disabled"]="disabled";e["hidden"]="hidden";e["outside"]="outside";e["focused"]="focused";e["today"]="today"})(c||(c={}));var l;(function(e){e["range_end"]="range_end";e["range_middle"]="range_middle";e["range_start"]="range_start";e["selected"]="selected"})(l||(l={}));const u=Symbol.for("constructDateFrom");const d={};const f={};function h(e,t){try{const n=d[e]||=new Intl.DateTimeFormat("en-GB",{timeZone:e,hour:"numeric",timeZoneName:"longOffset"}).format;const r=n(t).split("GMT")[1]||"";if(r in f)return f[r];return g(r,r.split(":"))}catch{if(e in f)return f[e];const t=e?.match(p);if(t)return g(e,t.slice(1));return NaN}}const p=/([+-]\d\d):?(\d\d)?/;function g(e,t){const n=+t[0];const r=+(t[1]||0);return f[e]=n>0?n*60+r:n*60-r}class m extends Date{constructor(...e){super();if(e.length>1&&typeof e[e.length-1]==="string"){this.timeZone=e.pop()}this.internal=new Date;if(isNaN(h(this.timeZone,this))){this.setTime(NaN)}else{if(!e.length){this.setTime(Date.now())}else if(typeof e[0]==="number"&&(e.length===1||e.length===2&&typeof e[1]!=="number")){this.setTime(e[0])}else if(typeof e[0]==="string"){this.setTime(+new Date(e[0]))}else if(e[0]instanceof Date){this.setTime(+e[0])}else{this.setTime(+new Date(...e));w(this,NaN);b(this)}}}static tz(e,...t){return t.length?new m(...t,e):new m(Date.now(),e)}withTimeZone(e){return new m(+this,e)}getTimezoneOffset(){return-h(this.timeZone,this)}setTime(e){Date.prototype.setTime.apply(this,arguments);b(this);return+this}[Symbol.for("constructDateFrom")](e){return new m(+new Date(e),this.timeZone)}}const v=/^(get|set)(?!UTC)/;Object.getOwnPropertyNames(Date.prototype).forEach((e=>{if(!v.test(e))return;const t=e.replace(v,"$1UTC");if(!m.prototype[t])return;if(e.startsWith("get")){m.prototype[e]=function(){return this.internal[t]()}}else{m.prototype[e]=function(){Date.prototype[t].apply(this.internal,arguments);y(this);return+this};m.prototype[t]=function(){Date.prototype[t].apply(this,arguments);b(this);return+this}}}));function b(e){e.internal.setTime(+e);e.internal.setUTCMinutes(e.internal.getUTCMinutes()-e.getTimezoneOffset())}function y(e){Date.prototype.setFullYear.call(e,e.internal.getUTCFullYear(),e.internal.getUTCMonth(),e.internal.getUTCDate());Date.prototype.setHours.call(e,e.internal.getUTCHours(),e.internal.getUTCMinutes(),e.internal.getUTCSeconds(),e.internal.getUTCMilliseconds());w(e)}function w(e){const t=h(e.timeZone,e);const n=new Date(+e);n.setUTCHours(n.getUTCHours()-1);const r=-new Date(+e).getTimezoneOffset();const o=-new Date(+n).getTimezoneOffset();const s=r-o;const i=Date.prototype.getHours.apply(e)!==e.internal.getUTCHours();if(s&&i)e.internal.setUTCMinutes(e.internal.getUTCMinutes()+s);const a=r-t;if(a)Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+a);const c=h(e.timeZone,e);const l=-new Date(+e).getTimezoneOffset();const u=l-c;const d=c!==t;const f=u-a;if(d&&f){Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+f);const t=h(e.timeZone,e);const n=c-t;if(n){e.internal.setUTCMinutes(e.internal.getUTCMinutes()+n);Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+n)}}}class x extends m{static tz(e,...t){return t.length?new x(...t,e):new x(Date.now(),e)}toISOString(){const[e,t,n]=this.tzComponents();const r=`${e}${t}:${n}`;return this.internal.toISOString().slice(0,-1)+r}toString(){return`${this.toDateString()} ${this.toTimeString()}`}toDateString(){const[e,t,n,r]=this.internal.toUTCString().split(" ");return`${e?.slice(0,-1)} ${n} ${t} ${r}`}toTimeString(){const e=this.internal.toUTCString().split(" ")[4];const[t,n,r]=this.tzComponents();return`${e} GMT${t}${n}${r} (${D(this.timeZone,this)})`}toLocaleString(e,t){return Date.prototype.toLocaleString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleDateString(e,t){return Date.prototype.toLocaleDateString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}toLocaleTimeString(e,t){return Date.prototype.toLocaleTimeString.call(this,e,{...t,timeZone:t?.timeZone||this.timeZone})}tzComponents(){const e=this.getTimezoneOffset();const t=e>0?"-":"+";const n=String(Math.floor(Math.abs(e)/60)).padStart(2,"0");const r=String(Math.abs(e)%60).padStart(2,"0");return[t,n,r]}withTimeZone(e){return new x(+this,e)}[Symbol.for("constructDateFrom")](e){return new x(+new Date(e),this.timeZone)}}function D(e,t){return new Intl.DateTimeFormat("en-GB",{timeZone:e,timeZoneName:"long"}).format(t).slice(12)}const k=e=>t=>TZDate.tz(e,+new Date(t));function C(e,t){const n=[];const r=new Date(t.start);r.setUTCSeconds(0,0);const o=new Date(t.end);o.setUTCSeconds(0,0);const s=+o;let i=tzOffset(e,r);while(+r<s){r.setUTCMonth(r.getUTCMonth()+1);const t=tzOffset(e,r);if(t!=i){const t=new Date(r);t.setUTCMonth(t.getUTCMonth()-1);const o=+r;i=tzOffset(e,t);while(+t<o){t.setUTCDate(t.getUTCDate()+1);const r=tzOffset(e,t);if(r!=i){const r=new Date(t);r.setUTCDate(r.getUTCDate()-1);const o=+t;i=tzOffset(e,r);while(+r<o){r.setUTCHours(r.getUTCHours()+1);const t=tzOffset(e,r);if(t!==i){n.push({date:new Date(r),change:t-i,offset:t})}i=t}}i=r}}i=t}return n}const S=7;const M=365.2425;const _=Math.pow(10,8)*24*60*60*1e3;const E=-_;const N=6048e5;const O=864e5;const R=6e4;const T=36e5;const A=1e3;const I=525600;const F=43200;const L=1440;const P=60;const W=3;const j=12;const B=4;const z=3600;const Y=60;const V=z*24;const U=V*7;const H=V*M;const X=H/12;const q=X*3;const Z=Symbol.for("constructDateFrom");function J(e,t){if(typeof e==="function")return e(t);if(e&&typeof e==="object"&&Z in e)return e[Z](t);if(e instanceof Date)return new e.constructor(t);return new Date(t)}const G=null&&J;function $(e,t){return J(t||e,e)}const K=null&&$;function Q(e,t,n){const r=$(e,n?.in);if(isNaN(t))return J(n?.in||e,NaN);if(!t)return r;r.setDate(r.getDate()+t);return r}const ee=null&&Q;function te(e,t,n){const r=$(e,n?.in);if(isNaN(t))return J(n?.in||e,NaN);if(!t){return r}const o=r.getDate();const s=J(n?.in||e,r.getTime());s.setMonth(r.getMonth()+t+1,0);const i=s.getDate();if(o>=i){return s}else{r.setFullYear(s.getFullYear(),s.getMonth(),o);return r}}const ne=null&&te;function re(e,t,n){return Q(e,t*7,n)}const oe=null&&re;function se(e,t,n){return te(e,t*12,n)}const ie=null&&se;function ae(e){const t=$(e);const n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));n.setUTCFullYear(t.getFullYear());return+e-+n}function ce(e,...t){const n=J.bind(null,e||t.find((e=>typeof e==="object")));return t.map(n)}function le(e,t){const n=$(e,t?.in);n.setHours(0,0,0,0);return n}const ue=null&&le;function de(e,t,n){const[r,o]=ce(n?.in,e,t);const s=le(r);const i=le(o);const a=+s-ae(s);const c=+i-ae(i);return Math.round((a-c)/O)}const fe=null&&de;function he(e,t,n){const[r,o]=ce(n?.in,e,t);const s=r.getFullYear()-o.getFullYear();const i=r.getMonth()-o.getMonth();return s*12+i}const pe=null&&he;function ge(e,t){const[n,r]=ce(e,t.start,t.end);return{start:n,end:r}}function me(e,t){const{start:n,end:r}=ge(t?.in,e);let o=+n>+r;const s=o?+n:+r;const i=o?r:n;i.setHours(0,0,0,0);i.setDate(1);let a=t?.step??1;if(!a)return[];if(a<0){a=-a;o=!o}const c=[];while(+i<=s){c.push(J(n,i));i.setMonth(i.getMonth()+a)}return o?c.reverse():c}const ve=null&&me;let be={};function ye(){return be}function we(e){be=e}function xe(e,t){const n=ye();const r=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0;const o=$(e,t?.in);const s=o.getDay();const i=(s<r?-7:0)+6-(s-r);o.setDate(o.getDate()+i);o.setHours(23,59,59,999);return o}const De=null&&xe;function ke(e,t){return xe(e,{...t,weekStartsOn:1})}const Ce=null&&ke;function Se(e,t){const n=$(e,t?.in);const r=n.getMonth();n.setFullYear(n.getFullYear(),r+1,0);n.setHours(23,59,59,999);return n}const Me=null&&Se;function _e(e,t){const n=$(e,t?.in);const r=n.getFullYear();n.setFullYear(r+1,0,0);n.setHours(23,59,59,999);return n}const Ee=null&&_e;const Ne={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};const Oe=(e,t,n)=>{let r;const o=Ne[e];if(typeof o==="string"){r=o}else if(t===1){r=o.one}else{r=o.other.replace("{{count}}",t.toString())}if(n?.addSuffix){if(n.comparison&&n.comparison>0){return"in "+r}else{return r+" ago"}}return r};function Re(e){return(t={})=>{const n=t.width?String(t.width):e.defaultWidth;const r=e.formats[n]||e.formats[e.defaultWidth];return r}}const Te={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"};const Ae={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"};const Ie={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"};const Fe={date:Re({formats:Te,defaultWidth:"full"}),time:Re({formats:Ae,defaultWidth:"full"}),dateTime:Re({formats:Ie,defaultWidth:"full"})};const Le={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};const Pe=(e,t,n,r)=>Le[e];function We(e){return(t,n)=>{const r=n?.context?String(n.context):"standalone";let o;if(r==="formatting"&&e.formattingValues){const t=e.defaultFormattingWidth||e.defaultWidth;const r=n?.width?String(n.width):t;o=e.formattingValues[r]||e.formattingValues[t]}else{const t=e.defaultWidth;const r=n?.width?String(n.width):e.defaultWidth;o=e.values[r]||e.values[t]}const s=e.argumentCallback?e.argumentCallback(t):t;return o[s]}}const je={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]};const Be={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]};const ze={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]};const Ye={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]};const Ve={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}};const Ue={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}};const He=(e,t)=>{const n=Number(e);const r=n%100;if(r>20||r<10){switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}}return n+"th"};const Xe={ordinalNumber:He,era:We({values:je,defaultWidth:"wide"}),quarter:We({values:Be,defaultWidth:"wide",argumentCallback:e=>e-1}),month:We({values:ze,defaultWidth:"wide"}),day:We({values:Ye,defaultWidth:"wide"}),dayPeriod:We({values:Ve,defaultWidth:"wide",formattingValues:Ue,defaultFormattingWidth:"wide"})};function qe(e){return(t,n={})=>{const r=n.width;const o=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth];const s=t.match(o);if(!s){return null}const i=s[0];const a=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth];const c=Array.isArray(a)?Je(a,(e=>e.test(i))):Ze(a,(e=>e.test(i)));let l;l=e.valueCallback?e.valueCallback(c):c;l=n.valueCallback?n.valueCallback(l):l;const u=t.slice(i.length);return{value:l,rest:u}}}function Ze(e,t){for(const n in e){if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n])){return n}}return undefined}function Je(e,t){for(let n=0;n<e.length;n++){if(t(e[n])){return n}}return undefined}function Ge(e){return(t,n={})=>{const r=t.match(e.matchPattern);if(!r)return null;const o=r[0];const s=t.match(e.parsePattern);if(!s)return null;let i=e.valueCallback?e.valueCallback(s[0]):s[0];i=n.valueCallback?n.valueCallback(i):i;const a=t.slice(o.length);return{value:i,rest:a}}}const $e=/^(\d+)(th|st|nd|rd)?/i;const Ke=/\d+/i;const Qe={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i};const et={any:[/^b/i,/^(a|c)/i]};const tt={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i};const nt={any:[/1/i,/2/i,/3/i,/4/i]};const rt={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i};const ot={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]};const st={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i};const it={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]};const at={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i};const ct={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}};const lt={ordinalNumber:Ge({matchPattern:$e,parsePattern:Ke,valueCallback:e=>parseInt(e,10)}),era:qe({matchPatterns:Qe,defaultMatchWidth:"wide",parsePatterns:et,defaultParseWidth:"any"}),quarter:qe({matchPatterns:tt,defaultMatchWidth:"wide",parsePatterns:nt,defaultParseWidth:"any",valueCallback:e=>e+1}),month:qe({matchPatterns:rt,defaultMatchWidth:"wide",parsePatterns:ot,defaultParseWidth:"any"}),day:qe({matchPatterns:st,defaultMatchWidth:"wide",parsePatterns:it,defaultParseWidth:"any"}),dayPeriod:qe({matchPatterns:at,defaultMatchWidth:"any",parsePatterns:ct,defaultParseWidth:"any"})};const ut={code:"en-US",formatDistance:Oe,formatLong:Fe,formatRelative:Pe,localize:Xe,match:lt,options:{weekStartsOn:0,firstWeekContainsDate:1}};const dt=null&&ut;function ft(e,t){const n=$(e,t?.in);n.setFullYear(n.getFullYear(),0,1);n.setHours(0,0,0,0);return n}const ht=null&&ft;function pt(e,t){const n=$(e,t?.in);const r=de(n,ft(n));const o=r+1;return o}const gt=null&&pt;function mt(e,t){const n=ye();const r=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??n.weekStartsOn??n.locale?.options?.weekStartsOn??0;const o=$(e,t?.in);const s=o.getDay();const i=(s<r?7:0)+s-r;o.setDate(o.getDate()-i);o.setHours(0,0,0,0);return o}const vt=null&&mt;function bt(e,t){return mt(e,{...t,weekStartsOn:1})}const yt=null&&bt;function wt(e,t){const n=$(e,t?.in);const r=n.getFullYear();const o=J(n,0);o.setFullYear(r+1,0,4);o.setHours(0,0,0,0);const s=bt(o);const i=J(n,0);i.setFullYear(r,0,4);i.setHours(0,0,0,0);const a=bt(i);if(n.getTime()>=s.getTime()){return r+1}else if(n.getTime()>=a.getTime()){return r}else{return r-1}}const xt=null&&wt;function Dt(e,t){const n=wt(e,t);const r=J(t?.in||e,0);r.setFullYear(n,0,4);r.setHours(0,0,0,0);return bt(r)}const kt=null&&Dt;function Ct(e,t){const n=$(e,t?.in);const r=+bt(n)-+Dt(n);return Math.round(r/N)+1}const St=null&&Ct;function Mt(e,t){const n=$(e,t?.in);const r=n.getFullYear();const o=ye();const s=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??o.firstWeekContainsDate??o.locale?.options?.firstWeekContainsDate??1;const i=J(t?.in||e,0);i.setFullYear(r+1,0,s);i.setHours(0,0,0,0);const a=mt(i,t);const c=J(t?.in||e,0);c.setFullYear(r,0,s);c.setHours(0,0,0,0);const l=mt(c,t);if(+n>=+a){return r+1}else if(+n>=+l){return r}else{return r-1}}const _t=null&&Mt;function Et(e,t){const n=ye();const r=t?.firstWeekContainsDate??t?.locale?.options?.firstWeekContainsDate??n.firstWeekContainsDate??n.locale?.options?.firstWeekContainsDate??1;const o=Mt(e,t);const s=J(t?.in||e,0);s.setFullYear(o,0,r);s.setHours(0,0,0,0);const i=mt(s,t);return i}const Nt=null&&Et;function Ot(e,t){const n=$(e,t?.in);const r=+mt(n,t)-+Et(n,t);return Math.round(r/N)+1}const Rt=null&&Ot;function Tt(e,t){const n=e<0?"-":"";const r=Math.abs(e).toString().padStart(t,"0");return n+r}const At={y(e,t){const n=e.getFullYear();const r=n>0?n:1-n;return Tt(t==="yy"?r%100:r,t.length)},M(e,t){const n=e.getMonth();return t==="M"?String(n+1):Tt(n+1,2)},d(e,t){return Tt(e.getDate(),t.length)},a(e,t){const n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];case"aaaa":default:return n==="am"?"a.m.":"p.m."}},h(e,t){return Tt(e.getHours()%12||12,t.length)},H(e,t){return Tt(e.getHours(),t.length)},m(e,t){return Tt(e.getMinutes(),t.length)},s(e,t){return Tt(e.getSeconds(),t.length)},S(e,t){const n=t.length;const r=e.getMilliseconds();const o=Math.trunc(r*Math.pow(10,n-3));return Tt(o,t.length)}};const It={am:"am",pm:"pm",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"};const Ft={G:function(e,t,n){const r=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(r,{width:"abbreviated"});case"GGGGG":return n.era(r,{width:"narrow"});case"GGGG":default:return n.era(r,{width:"wide"})}},y:function(e,t,n){if(t==="yo"){const t=e.getFullYear();const r=t>0?t:1-t;return n.ordinalNumber(r,{unit:"year"})}return At.y(e,t)},Y:function(e,t,n,r){const o=Mt(e,r);const s=o>0?o:1-o;if(t==="YY"){const e=s%100;return Tt(e,2)}if(t==="Yo"){return n.ordinalNumber(s,{unit:"year"})}return Tt(s,t.length)},R:function(e,t){const n=wt(e);return Tt(n,t.length)},u:function(e,t){const n=e.getFullYear();return Tt(n,t.length)},Q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(r);case"QQ":return Tt(r,2);case"Qo":return n.ordinalNumber(r,{unit:"quarter"});case"QQQ":return n.quarter(r,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(r,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(r,{width:"wide",context:"formatting"})}},q:function(e,t,n){const r=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(r);case"qq":return Tt(r,2);case"qo":return n.ordinalNumber(r,{unit:"quarter"});case"qqq":return n.quarter(r,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(r,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(r,{width:"wide",context:"standalone"})}},M:function(e,t,n){const r=e.getMonth();switch(t){case"M":case"MM":return At.M(e,t);case"Mo":return n.ordinalNumber(r+1,{unit:"month"});case"MMM":return n.month(r,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(r,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(r,{width:"wide",context:"formatting"})}},L:function(e,t,n){const r=e.getMonth();switch(t){case"L":return String(r+1);case"LL":return Tt(r+1,2);case"Lo":return n.ordinalNumber(r+1,{unit:"month"});case"LLL":return n.month(r,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(r,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(r,{width:"wide",context:"standalone"})}},w:function(e,t,n,r){const o=Ot(e,r);if(t==="wo"){return n.ordinalNumber(o,{unit:"week"})}return Tt(o,t.length)},I:function(e,t,n){const r=Ct(e);if(t==="Io"){return n.ordinalNumber(r,{unit:"week"})}return Tt(r,t.length)},d:function(e,t,n){if(t==="do"){return n.ordinalNumber(e.getDate(),{unit:"date"})}return At.d(e,t)},D:function(e,t,n){const r=pt(e);if(t==="Do"){return n.ordinalNumber(r,{unit:"dayOfYear"})}return Tt(r,t.length)},E:function(e,t,n){const r=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(r,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(r,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(r,{width:"short",context:"formatting"});case"EEEE":default:return n.day(r,{width:"wide",context:"formatting"})}},e:function(e,t,n,r){const o=e.getDay();const s=(o-r.weekStartsOn+8)%7||7;switch(t){case"e":return String(s);case"ee":return Tt(s,2);case"eo":return n.ordinalNumber(s,{unit:"day"});case"eee":return n.day(o,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(o,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(o,{width:"short",context:"formatting"});case"eeee":default:return n.day(o,{width:"wide",context:"formatting"})}},c:function(e,t,n,r){const o=e.getDay();const s=(o-r.weekStartsOn+8)%7||7;switch(t){case"c":return String(s);case"cc":return Tt(s,t.length);case"co":return n.ordinalNumber(s,{unit:"day"});case"ccc":return n.day(o,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(o,{width:"narrow",context:"standalone"});case"cccccc":return n.day(o,{width:"short",context:"standalone"});case"cccc":default:return n.day(o,{width:"wide",context:"standalone"})}},i:function(e,t,n){const r=e.getDay();const o=r===0?7:r;switch(t){case"i":return String(o);case"ii":return Tt(o,t.length);case"io":return n.ordinalNumber(o,{unit:"day"});case"iii":return n.day(r,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(r,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(r,{width:"short",context:"formatting"});case"iiii":default:return n.day(r,{width:"wide",context:"formatting"})}},a:function(e,t,n){const r=e.getHours();const o=r/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(o,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(o,{width:"wide",context:"formatting"})}},b:function(e,t,n){const r=e.getHours();let o;if(r===12){o=It.noon}else if(r===0){o=It.midnight}else{o=r/12>=1?"pm":"am"}switch(t){case"b":case"bb":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(o,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(o,{width:"wide",context:"formatting"})}},B:function(e,t,n){const r=e.getHours();let o;if(r>=17){o=It.evening}else if(r>=12){o=It.afternoon}else if(r>=4){o=It.morning}else{o=It.night}switch(t){case"B":case"BB":case"BBB":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(o,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(o,{width:"wide",context:"formatting"})}},h:function(e,t,n){if(t==="ho"){let t=e.getHours()%12;if(t===0)t=12;return n.ordinalNumber(t,{unit:"hour"})}return At.h(e,t)},H:function(e,t,n){if(t==="Ho"){return n.ordinalNumber(e.getHours(),{unit:"hour"})}return At.H(e,t)},K:function(e,t,n){const r=e.getHours()%12;if(t==="Ko"){return n.ordinalNumber(r,{unit:"hour"})}return Tt(r,t.length)},k:function(e,t,n){let r=e.getHours();if(r===0)r=24;if(t==="ko"){return n.ordinalNumber(r,{unit:"hour"})}return Tt(r,t.length)},m:function(e,t,n){if(t==="mo"){return n.ordinalNumber(e.getMinutes(),{unit:"minute"})}return At.m(e,t)},s:function(e,t,n){if(t==="so"){return n.ordinalNumber(e.getSeconds(),{unit:"second"})}return At.s(e,t)},S:function(e,t){return At.S(e,t)},X:function(e,t,n){const r=e.getTimezoneOffset();if(r===0){return"Z"}switch(t){case"X":return Pt(r);case"XXXX":case"XX":return Wt(r);case"XXXXX":case"XXX":default:return Wt(r,":")}},x:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"x":return Pt(r);case"xxxx":case"xx":return Wt(r);case"xxxxx":case"xxx":default:return Wt(r,":")}},O:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+Lt(r,":");case"OOOO":default:return"GMT"+Wt(r,":")}},z:function(e,t,n){const r=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+Lt(r,":");case"zzzz":default:return"GMT"+Wt(r,":")}},t:function(e,t,n){const r=Math.trunc(+e/1e3);return Tt(r,t.length)},T:function(e,t,n){return Tt(+e,t.length)}};function Lt(e,t=""){const n=e>0?"-":"+";const r=Math.abs(e);const o=Math.trunc(r/60);const s=r%60;if(s===0){return n+String(o)}return n+String(o)+t+Tt(s,2)}function Pt(e,t){if(e%60===0){const t=e>0?"-":"+";return t+Tt(Math.abs(e)/60,2)}return Wt(e,t)}function Wt(e,t=""){const n=e>0?"-":"+";const r=Math.abs(e);const o=Tt(Math.trunc(r/60),2);const s=Tt(r%60,2);return n+o+t+s}const jt=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}};const Bt=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}};const zt=(e,t)=>{const n=e.match(/(P+)(p+)?/)||[];const r=n[1];const o=n[2];if(!o){return jt(e,t)}let s;switch(r){case"P":s=t.dateTime({width:"short"});break;case"PP":s=t.dateTime({width:"medium"});break;case"PPP":s=t.dateTime({width:"long"});break;case"PPPP":default:s=t.dateTime({width:"full"});break}return s.replace("{{date}}",jt(r,t)).replace("{{time}}",Bt(o,t))};const Yt={p:Bt,P:zt};const Vt=/^D+$/;const Ut=/^Y+$/;const Ht=["D","DD","YY","YYYY"];function Xt(e){return Vt.test(e)}function qt(e){return Ut.test(e)}function Zt(e,t,n){const r=Jt(e,t,n);console.warn(r);if(Ht.includes(e))throw new RangeError(r)}function Jt(e,t,n){const r=e[0]==="Y"?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${r} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}function Gt(e){return e instanceof Date||typeof e==="object"&&Object.prototype.toString.call(e)==="[object Date]"}const $t=null&&Gt;function Kt(e){return!(!Gt(e)&&typeof e!=="number"||isNaN(+$(e)))}const Qt=null&&Kt;const en=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g;const tn=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;const nn=/^'([^]*?)'?$/;const rn=/''/g;const on=/[a-zA-Z]/;function sn(e,t,n){const r=ye();const o=n?.locale??r.locale??ut;const s=n?.firstWeekContainsDate??n?.locale?.options?.firstWeekContainsDate??r.firstWeekContainsDate??r.locale?.options?.firstWeekContainsDate??1;const i=n?.weekStartsOn??n?.locale?.options?.weekStartsOn??r.weekStartsOn??r.locale?.options?.weekStartsOn??0;const a=$(e,n?.in);if(!Kt(a)){throw new RangeError("Invalid time value")}let c=t.match(tn).map((e=>{const t=e[0];if(t==="p"||t==="P"){const n=Yt[t];return n(e,o.formatLong)}return e})).join("").match(en).map((e=>{if(e==="''"){return{isToken:false,value:"'"}}const t=e[0];if(t==="'"){return{isToken:false,value:an(e)}}if(Ft[t]){return{isToken:true,value:e}}if(t.match(on)){throw new RangeError("Format string contains an unescaped latin alphabet character `"+t+"`")}return{isToken:false,value:e}}));if(o.localize.preprocessor){c=o.localize.preprocessor(a,c)}const l={firstWeekContainsDate:s,weekStartsOn:i,locale:o};return c.map((r=>{if(!r.isToken)return r.value;const s=r.value;if(!n?.useAdditionalWeekYearTokens&&qt(s)||!n?.useAdditionalDayOfYearTokens&&Xt(s)){Zt(s,t,String(e))}const i=Ft[s[0]];return i(a,s,o.localize,l)})).join("")}function an(e){const t=e.match(nn);if(!t){return e}return t[1].replace(rn,"'")}const cn=null&&sn;function ln(e,t){return $(e,t?.in).getMonth()}const un=null&&ln;function dn(e,t){return $(e,t?.in).getFullYear()}const fn=null&&dn;function hn(e,t){return+$(e)>+$(t)}const pn=null&&hn;function gn(e,t){return+$(e)<+$(t)}const mn=null&&gn;function vn(e,t,n){const[r,o]=ce(n?.in,e,t);return+le(r)===+le(o)}const bn=null&&vn;function yn(e,t,n){const[r,o]=ce(n?.in,e,t);return r.getFullYear()===o.getFullYear()&&r.getMonth()===o.getMonth()}const wn=null&&yn;function xn(e,t,n){const[r,o]=ce(n?.in,e,t);return r.getFullYear()===o.getFullYear()}const Dn=null&&xn;function kn(e,t){let n;let r=t?.in;e.forEach((e=>{if(!r&&typeof e==="object")r=J.bind(null,e);const t=$(e,r);if(!n||n<t||isNaN(+t))n=t}));return J(r,n||NaN)}const Cn=null&&kn;function Sn(e,t){let n;let r=t?.in;e.forEach((e=>{if(!r&&typeof e==="object")r=J.bind(null,e);const t=$(e,r);if(!n||n>t||isNaN(+t))n=t}));return J(r,n||NaN)}const Mn=null&&Sn;function _n(e,t){const n=$(e,t?.in);const r=n.getFullYear();const o=n.getMonth();const s=J(n,0);s.setFullYear(r,o+1,0);s.setHours(0,0,0,0);return s.getDate()}const En=null&&_n;function Nn(e,t,n){const r=$(e,n?.in);const o=r.getFullYear();const s=r.getDate();const i=J(n?.in||e,0);i.setFullYear(o,t,15);i.setHours(0,0,0,0);const a=_n(i);r.setMonth(t,Math.min(s,a));return r}const On=null&&Nn;function Rn(e,t,n){const r=$(e,n?.in);if(isNaN(+r))return J(n?.in||e,NaN);r.setFullYear(t);return r}const Tn=null&&Rn;function An(e,t){const n=$(e,t?.in);n.setDate(1);n.setHours(0,0,0,0);return n}const In=null&&An;const Fn=5;const Ln=4;function Pn(e,t){const n=t.startOfMonth(e);const r=n.getDay()>0?n.getDay():7;const o=t.addDays(e,-r+1);const s=t.addDays(o,Fn*7-1);const i=t.getMonth(e)===t.getMonth(s)?Fn:Ln;return i}function Wn(e,t){const n=t.startOfMonth(e);const r=n.getDay();if(r===1){return n}else if(r===0){return t.addDays(n,-1*6)}else{return t.addDays(n,-1*(r-1))}}function jn(e,t){const n=Wn(e,t);const r=Pn(e,t);const o=t.addDays(n,r*7-1);return o}class Bn{constructor(e,t){this.Date=Date;this.today=()=>{if(this.overrides?.today){return this.overrides.today()}if(this.options.timeZone){return x.tz(this.options.timeZone)}return new this.Date};this.newDate=(e,t,n)=>{if(this.overrides?.newDate){return this.overrides.newDate(e,t,n)}if(this.options.timeZone){return new x(e,t,n,this.options.timeZone)}return new Date(e,t,n)};this.addDays=(e,t)=>this.overrides?.addDays?.(e,t)??Q(e,t);this.addMonths=(e,t)=>this.overrides?.addMonths?.(e,t)??te(e,t);this.addWeeks=(e,t)=>this.overrides?.addWeeks?.(e,t)??re(e,t);this.addYears=(e,t)=>this.overrides?.addYears?.(e,t)??se(e,t);this.differenceInCalendarDays=(e,t)=>this.overrides?.differenceInCalendarDays?.(e,t)??de(e,t);this.differenceInCalendarMonths=(e,t)=>this.overrides?.differenceInCalendarMonths?.(e,t)??he(e,t);this.eachMonthOfInterval=e=>this.overrides?.eachMonthOfInterval?.(e)??me(e);this.endOfBroadcastWeek=(e,t)=>this.overrides?.endOfBroadcastWeek?.(e,t)??jn(e,this);this.endOfISOWeek=e=>this.overrides?.endOfISOWeek?.(e)??ke(e);this.endOfMonth=e=>this.overrides?.endOfMonth?.(e)??Se(e);this.endOfWeek=(e,t)=>this.overrides?.endOfWeek?.(e,t??this.options)??xe(e,t??this.options);this.endOfYear=e=>this.overrides?.endOfYear?.(e)??_e(e);this.format=(e,t,n)=>{const r=this.overrides?.format?.(e,t,n??this.options)??sn(e,t,n??this.options);if(this.options.numerals&&this.options.numerals!=="latn"){return this.replaceDigits(r)}return r};this.getISOWeek=e=>this.overrides?.getISOWeek?.(e)??Ct(e);this.getMonth=e=>this.overrides?.getMonth?.(e)??ln(e);this.getYear=e=>this.overrides?.getYear?.(e)??dn(e);this.getWeek=(e,t)=>this.overrides?.getWeek?.(e,t??this.options)??Ot(e,t??this.options);this.isAfter=(e,t)=>this.overrides?.isAfter?.(e,t)??hn(e,t);this.isBefore=(e,t)=>this.overrides?.isBefore?.(e,t)??gn(e,t);this.isDate=e=>this.overrides?.isDate?.(e)??Gt(e);this.isSameDay=(e,t)=>this.overrides?.isSameDay?.(e,t)??vn(e,t);this.isSameMonth=(e,t)=>this.overrides?.isSameMonth?.(e,t)??yn(e,t);this.isSameYear=(e,t)=>this.overrides?.isSameYear?.(e,t)??xn(e,t);this.max=e=>this.overrides?.max?.(e)??kn(e);this.min=e=>this.overrides?.min?.(e)??Sn(e);this.setMonth=(e,t)=>this.overrides?.setMonth?.(e,t)??Nn(e,t);this.setYear=(e,t)=>this.overrides?.setYear?.(e,t)??Rn(e,t);this.startOfBroadcastWeek=(e,t)=>this.overrides?.startOfBroadcastWeek?.(e,t??this)??Wn(e,t??this);this.startOfDay=e=>this.overrides?.startOfDay?.(e)??le(e);this.startOfISOWeek=e=>this.overrides?.startOfISOWeek?.(e)??bt(e);this.startOfMonth=e=>this.overrides?.startOfMonth?.(e)??An(e);this.startOfWeek=e=>this.overrides?.startOfWeek?.(e)??mt(e,this.options);this.startOfYear=e=>this.overrides?.startOfYear?.(e)??ft(e);this.options={locale:ut,...e};this.overrides=t}getDigitMap(){const{numerals:e="latn"}=this.options;const t=new Intl.NumberFormat("en-US",{numberingSystem:e});const n={};for(let e=0;e<10;e++){n[e.toString()]=t.format(e)}return n}replaceDigits(e){const t=this.getDigitMap();return e.replace(/\d/g,(e=>t[e]||e))}formatNumber(e){return this.replaceDigits(e.toString())}}const zn=new Bn;const Yn=null&&zn;function Vn(e,t,n={}){const r=Object.entries(e).filter((([,e])=>e===true)).reduce(((e,[r])=>{if(n[r]){e.push(n[r])}else if(t[c[r]]){e.push(t[c[r]])}else if(t[l[r]]){e.push(t[l[r]])}return e}),[t[a.Day]]);return r}function Un(e){return i.createElement("button",{...e})}function Hn(e){return i.createElement("span",{...e})}function Xn(e){const{size:t=24,orientation:n="left",className:r}=e;return i.createElement("svg",{className:r,width:t,height:t,viewBox:"0 0 24 24"},n==="up"&&i.createElement("polygon",{points:"6.77 17 12.5 11.43 18.24 17 20 15.28 12.5 8 5 15.28"}),n==="down"&&i.createElement("polygon",{points:"6.77 8 12.5 13.57 18.24 8 20 9.72 12.5 17 5 9.72"}),n==="left"&&i.createElement("polygon",{points:"16 18.112 9.81111111 12 16 5.87733333 14.0888889 4 6 12 14.0888889 20"}),n==="right"&&i.createElement("polygon",{points:"8 18.612 14.1888889 12.5 8 6.37733333 9.91111111 4.5 18 12.5 9.91111111 20.5"}))}function qn(e){const{day:t,modifiers:n,...r}=e;return i.createElement("td",{...r})}function Zn(e){const{day:t,modifiers:n,...r}=e;const o=i.useRef(null);i.useEffect((()=>{if(n.focused)o.current?.focus()}),[n.focused]);return i.createElement("button",{ref:o,...r})}function Jn(e){const{options:t,className:n,components:r,classNames:o,...s}=e;const c=[o[a.Dropdown],n].join(" ");const l=t?.find((({value:e})=>e===s.value));return i.createElement("span",{"data-disabled":s.disabled,className:o[a.DropdownRoot]},i.createElement(r.Select,{className:c,...s},t?.map((({value:e,label:t,disabled:n})=>i.createElement(r.Option,{key:e,value:e,disabled:n},t)))),i.createElement("span",{className:o[a.CaptionLabel],"aria-hidden":true},l?.label,i.createElement(r.Chevron,{orientation:"down",size:18,className:o[a.Chevron]})))}function Gn(e){return i.createElement("div",{...e})}function $n(e){return i.createElement("div",{...e})}function Kn(e){const{calendarMonth:t,displayIndex:n,...r}=e;return i.createElement("div",{...r},e.children)}function Qn(e){const{calendarMonth:t,displayIndex:n,...r}=e;return i.createElement("div",{...r})}function er(e){return i.createElement("table",{...e})}function tr(e){return i.createElement("div",{...e})}const nr=(0,i.createContext)(undefined);function rr(){const e=(0,i.useContext)(nr);if(e===undefined){throw new Error("useDayPicker() must be used within a custom component.")}return e}function or(e){const{components:t}=rr();return i.createElement(t.Dropdown,{...e})}function sr(e){const{onPreviousClick:t,onNextClick:n,previousMonth:r,nextMonth:o,...s}=e;const{components:c,classNames:l,labels:{labelPrevious:u,labelNext:d}}=rr();return i.createElement("nav",{...s},i.createElement(c.PreviousMonthButton,{type:"button",className:l[a.PreviousMonthButton],tabIndex:r?undefined:-1,disabled:r?undefined:true,"aria-label":u(r),onClick:e.onPreviousClick},i.createElement(c.Chevron,{disabled:r?undefined:true,className:l[a.Chevron],orientation:"left"})),i.createElement(c.NextMonthButton,{type:"button",className:l[a.NextMonthButton],tabIndex:o?undefined:-1,disabled:o?undefined:true,"aria-label":d(o),onClick:e.onNextClick},i.createElement(c.Chevron,{disabled:o?undefined:true,orientation:"right",className:l[a.Chevron]})))}function ir(e){const{components:t}=rr();return i.createElement(t.Button,{...e})}function ar(e){return i.createElement("option",{...e})}function cr(e){const{components:t}=rr();return i.createElement(t.Button,{...e})}function lr(e){return i.createElement("div",{...e})}function ur(e){return i.createElement("select",{...e})}function dr(e){const{week:t,...n}=e;return i.createElement("tr",{...n})}function fr(e){return i.createElement("th",{...e})}function hr(e){return i.createElement("thead",{"aria-hidden":true},i.createElement("tr",{...e}))}function pr(e){const{week:t,...n}=e;return i.createElement("th",{...n})}function gr(e){return i.createElement("th",{...e})}function mr(e){return i.createElement("tbody",{...e})}function vr(e){const{components:t}=rr();return i.createElement(t.Dropdown,{...e})}function br(e){return{...r,...e}}function yr(e){const t={"data-mode":e.mode??undefined,"data-required":"required"in e?e.required:undefined,"data-multiple-months":e.numberOfMonths&&e.numberOfMonths>1||undefined,"data-week-numbers":e.showWeekNumber||undefined,"data-broadcast-calendar":e.broadcastCalendar||undefined};Object.entries(e).forEach((([e,n])=>{if(e.startsWith("data-")){t[e]=n}}));return t}function wr(){const e={};for(const t in a){e[a[t]]=`rdp-${a[t]}`}for(const t in c){e[c[t]]=`rdp-${c[t]}`}for(const t in l){e[l[t]]=`rdp-${l[t]}`}return e}function xr(e,t,n){return(n??new Bn(t)).format(e,"LLLL y")}const Dr=xr;function kr(e,t,n){return(n??new Bn(t)).format(e,"d")}function Cr(e,t=zn){return t.format(e,"LLLL")}function Sr(e){if(e<10){return`0${e.toLocaleString()}`}return`${e.toLocaleString()}`}function Mr(){return``}function _r(e,t,n){return(n??new Bn(t)).format(e,"cccccc")}function Er(e,t=zn){return t.format(e,"yyyy")}const Nr=Er;function Or(e){if(e?.formatMonthCaption&&!e.formatCaption){e.formatCaption=e.formatMonthCaption}if(e?.formatYearCaption&&!e.formatYearDropdown){e.formatYearDropdown=e.formatYearCaption}return{...o,...e}}function Rr(e,t,n,r,o){const{startOfMonth:s,startOfYear:i,endOfYear:a,eachMonthOfInterval:c,getMonth:l}=o;const u=c({start:i(e),end:a(e)});const d=u.map((e=>{const i=r.formatMonthDropdown(e,o);const a=l(e);const c=t&&e<s(t)||n&&e>s(n)||false;return{value:a,label:i,disabled:c}}));return d}function Tr(e,t={},n={}){let r={...t?.[a.Day]};Object.entries(e).filter((([,e])=>e===true)).forEach((([e])=>{r={...r,...n?.[e]}}));return r}function Ar(e,t,n){const r=e.today();const o=n?e.startOfBroadcastWeek(r,e):t?e.startOfISOWeek(r):e.startOfWeek(r);const s=[];for(let t=0;t<7;t++){const n=e.addDays(o,t);s.push(n)}return s}function Ir(e,t,n,r){if(!e)return undefined;if(!t)return undefined;const{startOfYear:o,endOfYear:s,addYears:i,getYear:a,isBefore:c,isSameYear:l}=r;const u=o(e);const d=s(t);const f=[];let h=u;while(c(h,d)||l(h,d)){f.push(h);h=i(h,1)}return f.map((e=>{const t=n.formatYearDropdown(e,r);return{value:a(e),label:t,disabled:false}}))}function Fr(e,t,n){return(n??new Bn(t)).format(e,"LLLL y")}const Lr=Fr;function Pr(e,t,n,r){let o=(r??new Bn(n)).format(e,"PPPP");if(t?.today){o=`Today, ${o}`}return o}function Wr(e,t,n,r){let o=(r??new Bn(n)).format(e,"PPPP");if(t.today)o=`Today, ${o}`;if(t.selected)o=`${o}, selected`;return o}const jr=Wr;function Br(){return""}function zr(e){return"Choose the Month"}function Yr(e){return"Go to the Next Month"}function Vr(e){return"Go to the Previous Month"}function Ur(e,t,n){return(n??new Bn(t)).format(e,"cccc")}function Hr(e,t){return`Week ${e}`}function Xr(e){return"Week Number"}function qr(e){return"Choose the Year"}function Zr(e,t,n,r){const o=e[0];const s=e[e.length-1];const{ISOWeek:i,fixedWeeks:a,broadcastCalendar:c}=n??{};const{addDays:l,differenceInCalendarDays:u,differenceInCalendarMonths:d,endOfBroadcastWeek:f,endOfISOWeek:h,endOfMonth:p,endOfWeek:g,isAfter:m,startOfBroadcastWeek:v,startOfISOWeek:b,startOfWeek:y}=r;const w=c?v(o,r):i?b(o):y(o);const x=c?f(s,r):i?h(p(s)):g(p(s));const D=u(x,w);const k=d(s,o)+1;const C=[];for(let e=0;e<=D;e++){const n=l(w,e);if(t&&m(n,t)){break}C.push(n)}const S=c?35:42;const M=S*k;if(a&&C.length<M){const e=M-C.length;for(let t=0;t<e;t++){const e=l(C[C.length-1],1);C.push(e)}}return C}function Jr(e){const t=[];return e.reduce(((e,t)=>{const n=[];const r=t.weeks.reduce(((e,t)=>[...e,...t.days]),n);return[...e,...r]}),t)}function Gr(e,t,n,r){const{numberOfMonths:o=1}=n;const s=[];for(let n=0;n<o;n++){const o=r.addMonths(e,n);if(t&&o>t){break}s.push(o)}return s}function $r(e,t){const{month:n,defaultMonth:r,today:o=t.today(),numberOfMonths:s=1,endMonth:i,startMonth:a}=e;let c=n||r||o;const{differenceInCalendarMonths:l,addMonths:u,startOfMonth:d}=t;if(i&&l(i,c)<0){const e=-1*(s-1);c=u(i,e)}if(a&&l(c,a)<0){c=a}return d(c)}class Kr{constructor(e,t,n=zn){this.date=e;this.displayMonth=t;this.outside=Boolean(t&&!n.isSameMonth(e,t));this.dateLib=n}isEqualTo(e){return this.dateLib.isSameDay(e.date,this.date)&&this.dateLib.isSameMonth(e.displayMonth,this.displayMonth)}}class Qr{constructor(e,t){this.date=e;this.weeks=t}}class eo{constructor(e,t){this.days=t;this.weekNumber=e}}function to(e,t,n,r){const{addDays:o,endOfBroadcastWeek:s,endOfISOWeek:i,endOfMonth:a,endOfWeek:c,getISOWeek:l,getWeek:u,startOfBroadcastWeek:d,startOfISOWeek:f,startOfWeek:h}=r;const p=e.reduce(((e,p)=>{const g=n.broadcastCalendar?d(p,r):n.ISOWeek?f(p):h(p);const m=n.broadcastCalendar?s(p,r):n.ISOWeek?i(a(p)):c(a(p));const v=t.filter((e=>e>=g&&e<=m));const b=n.broadcastCalendar?35:42;if(n.fixedWeeks&&v.length<b){const e=t.filter((e=>{const t=b-v.length;return e>m&&e<=o(m,t)}));v.push(...e)}const y=v.reduce(((e,t)=>{const o=n.ISOWeek?l(t):u(t);const s=e.find((e=>e.weekNumber===o));const i=new Kr(t,p,r);if(!s){e.push(new eo(o,[i]))}else{s.days.push(i)}return e}),[]);const w=new Qr(p,y);e.push(w);return e}),[]);if(!n.reverseMonths){return p}else{return p.reverse()}}function no(e,t){let{startMonth:n,endMonth:r}=e;const{startOfYear:o,startOfDay:s,startOfMonth:i,endOfMonth:a,addYears:c,endOfYear:l,newDate:u,today:d}=t;const{fromYear:f,toYear:h,fromMonth:p,toMonth:g}=e;if(!n&&p){n=p}if(!n&&f){n=t.newDate(f,0,1)}if(!r&&g){r=g}if(!r&&h){r=u(h,11,31)}const m=e.captionLayout==="dropdown"||e.captionLayout==="dropdown-years";if(n){n=i(n)}else if(f){n=u(f,0,1)}else if(!n&&m){n=o(c(e.today??d(),-100))}if(r){r=a(r)}else if(h){r=u(h,11,31)}else if(!r&&m){r=l(e.today??d())}return[n?s(n):n,r?s(r):r]}function ro(e,t,n,r){if(n.disableNavigation){return undefined}const{pagedNavigation:o,numberOfMonths:s=1}=n;const{startOfMonth:i,addMonths:a,differenceInCalendarMonths:c}=r;const l=o?s:1;const u=i(e);if(!t){return a(u,l)}const d=c(t,e);if(d<s){return undefined}return a(u,l)}function oo(e,t,n,r){if(n.disableNavigation){return undefined}const{pagedNavigation:o,numberOfMonths:s}=n;const{startOfMonth:i,addMonths:a,differenceInCalendarMonths:c}=r;const l=o?s??1:1;const u=i(e);if(!t){return a(u,-l)}const d=c(u,t);if(d<=0){return undefined}return a(u,-l)}function so(e){const t=[];return e.reduce(((e,t)=>[...e,...t.weeks]),t)}function io(e,t){const[n,r]=(0,i.useState)(e);const o=t===undefined?n:t;return[o,r]}function ao(e,t){const[n,r]=no(e,t);const{startOfMonth:o,endOfMonth:s}=t;const a=$r(e,t);const[c,l]=io(a,e.month?a:undefined);(0,i.useEffect)((()=>{const n=$r(e,t);l(n)}),[e.timeZone]);const u=Gr(c,r,e,t);const d=Zr(u,e.endMonth?s(e.endMonth):undefined,e,t);const f=to(u,d,e,t);const h=so(f);const p=Jr(f);const g=oo(c,n,e,t);const m=ro(c,r,e,t);const{disableNavigation:v,onMonthChange:b}=e;const y=e=>h.some((t=>t.days.some((t=>t.isEqualTo(e)))));const w=e=>{if(v){return}let t=o(e);if(n&&t<o(n)){t=o(n)}if(r&&t>o(r)){t=o(r)}l(t);b?.(t)};const x=e=>{if(y(e)){return}w(e.date)};const D={months:f,weeks:h,days:p,navStart:n,navEnd:r,previousMonth:g,nextMonth:m,goToMonth:w,goToDay:x};return D}function co(e,t,n,r){let o;let s=0;let i=false;while(s<e.length&&!i){const a=e[s];const l=t(a);if(!l[c.disabled]&&!l[c.hidden]&&!l[c.outside]){if(l[c.focused]){o=a;i=true}else if(r?.isEqualTo(a)){o=a;i=true}else if(n(a.date)){o=a;i=true}else if(l[c.today]){o=a;i=true}}s++}if(!o){o=e.find((e=>{const n=t(e);return!n[c.disabled]&&!n[c.hidden]&&!n[c.outside]}))}return o}function lo(e,t,n=false,r=zn){let{from:o,to:s}=e;const{differenceInCalendarDays:i,isSameDay:a}=r;if(o&&s){const e=i(s,o)<0;if(e){[o,s]=[s,o]}const r=i(t,o)>=(n?1:0)&&i(s,t)>=(n?1:0);return r}if(!n&&s){return a(s,t)}if(!n&&o){return a(o,t)}return false}const uo=(e,t)=>lo(e,t,false,defaultDateLib);function fo(e){return Boolean(e&&typeof e==="object"&&"before"in e&&"after"in e)}function ho(e){return Boolean(e&&typeof e==="object"&&"from"in e)}function po(e){return Boolean(e&&typeof e==="object"&&"after"in e)}function go(e){return Boolean(e&&typeof e==="object"&&"before"in e)}function mo(e){return Boolean(e&&typeof e==="object"&&"dayOfWeek"in e)}function vo(e,t){return Array.isArray(e)&&e.every(t.isDate)}function bo(e,t,n=zn){const r=!Array.isArray(t)?[t]:t;const{isSameDay:o,differenceInCalendarDays:s,isAfter:i}=n;return r.some((t=>{if(typeof t==="boolean"){return t}if(n.isDate(t)){return o(e,t)}if(vo(t,n)){return t.includes(e)}if(ho(t)){return lo(t,e,false,n)}if(mo(t)){if(!Array.isArray(t.dayOfWeek)){return t.dayOfWeek===e.getDay()}return t.dayOfWeek.includes(e.getDay())}if(fo(t)){const n=s(t.before,e);const r=s(t.after,e);const o=n>0;const a=r<0;const c=i(t.before,t.after);if(c){return a&&o}else{return o||a}}if(po(t)){return s(e,t.after)>0}if(go(t)){return s(t.before,e)>0}if(typeof t==="function"){return t(e)}return false}))}const yo=null&&bo;function wo(e,t,n,r,o,s,i){const{ISOWeek:a,broadcastCalendar:c}=s;const{addDays:l,addMonths:u,addWeeks:d,addYears:f,endOfBroadcastWeek:h,endOfISOWeek:p,endOfWeek:g,max:m,min:v,startOfBroadcastWeek:b,startOfISOWeek:y,startOfWeek:w}=i;const x={day:l,week:d,month:u,year:f,startOfWeek:e=>c?b(e,i):a?y(e):w(e),endOfWeek:e=>c?h(e,i):a?p(e):g(e)};let D=x[e](n,t==="after"?1:-1);if(t==="before"&&r){D=m([r,D])}else if(t==="after"&&o){D=v([o,D])}return D}function xo(e,t,n,r,o,s,i,a=0){if(a>365){return undefined}const c=wo(e,t,n.date,r,o,s,i);const l=Boolean(s.disabled&&bo(c,s.disabled,i));const u=Boolean(s.hidden&&bo(c,s.hidden,i));const d=c;const f=new Kr(c,d,i);if(!l&&!u){return f}return xo(e,t,f,r,o,s,i,a+1)}function Do(e,t,n,r,o){const{autoFocus:s}=e;const[a,c]=(0,i.useState)();const l=co(t.days,n,r||(()=>false),a);const[u,d]=(0,i.useState)(s?l:undefined);const f=()=>{c(u);d(undefined)};const h=(n,r)=>{if(!u)return;const s=xo(n,r,u,t.navStart,t.navEnd,e,o);if(!s)return;t.goToDay(s);d(s)};const p=e=>Boolean(l?.isEqualTo(e));const g={isFocusTarget:p,setFocused:d,focused:u,blur:f,moveFocus:h};return g}function ko(e,t,n){const{disabled:r,hidden:o,modifiers:s,showOutsideDays:i,broadcastCalendar:a,today:l}=t;const{isSameDay:u,isSameMonth:d,startOfMonth:f,isBefore:h,endOfMonth:p,isAfter:g}=n;const m=t.startMonth&&f(t.startMonth);const v=t.endMonth&&p(t.endMonth);const b={[c.focused]:[],[c.outside]:[],[c.disabled]:[],[c.hidden]:[],[c.today]:[]};const y={};for(const t of e){const{date:e,displayMonth:c}=t;const f=Boolean(c&&!d(e,c));const p=Boolean(m&&h(e,m));const w=Boolean(v&&g(e,v));const x=Boolean(r&&bo(e,r,n));const D=Boolean(o&&bo(e,o,n))||p||w||!a&&!i&&f||a&&i===false&&f;const k=u(e,l??n.today());if(f)b.outside.push(t);if(x)b.disabled.push(t);if(D)b.hidden.push(t);if(k)b.today.push(t);if(s){Object.keys(s).forEach((r=>{const o=s?.[r];const i=o?bo(e,o,n):false;if(!i)return;if(y[r]){y[r].push(t)}else{y[r]=[t]}}))}}return e=>{const t={[c.focused]:false,[c.disabled]:false,[c.hidden]:false,[c.outside]:false,[c.today]:false};const n={};for(const n in b){const r=b[n];t[n]=r.some((t=>t===e))}for(const t in y){n[t]=y[t].some((t=>t===e))}return{...t,...n}}}function Co(e,t){const{selected:n,required:r,onSelect:o}=e;const[s,i]=io(n,o?n:undefined);const a=!o?s:n;const{isSameDay:c}=t;const l=e=>a?.some((t=>c(t,e)))??false;const{min:u,max:d}=e;const f=(e,t,n)=>{let s=[...a??[]];if(l(e)){if(a?.length===u){return}if(r&&a?.length===1){return}s=a?.filter((t=>!c(t,e)))}else{if(a?.length===d){s=[e]}else{s=[...s,e]}}if(!o){i(s)}o?.(s,e,t,n);return s};return{selected:a,select:f,isSelected:l}}function So(e,t,n=0,r=0,o=false,s=zn){const{from:i,to:a}=t||{};const{isSameDay:c,isAfter:l,isBefore:u}=s;let d;if(!i&&!a){d={from:e,to:n>0?undefined:e}}else if(i&&!a){if(c(i,e)){if(o){d={from:i,to:undefined}}else{d=undefined}}else if(u(e,i)){d={from:e,to:i}}else{d={from:i,to:e}}}else if(i&&a){if(c(i,e)&&c(a,e)){if(o){d={from:i,to:a}}else{d=undefined}}else if(c(i,e)){d={from:i,to:n>0?undefined:e}}else if(c(a,e)){d={from:e,to:n>0?undefined:e}}else if(u(e,i)){d={from:e,to:a}}else if(l(e,i)){d={from:i,to:e}}else if(l(e,a)){d={from:i,to:e}}else{throw new Error("Invalid range")}}if(d?.from&&d?.to){const t=s.differenceInCalendarDays(d.to,d.from);if(r>0&&t>r){d={from:e,to:undefined}}else if(n>1&&t<n){d={from:e,to:undefined}}}return d}function Mo(e,t,n=zn){const r=!Array.isArray(t)?[t]:t;let o=e.from;const s=n.differenceInCalendarDays(e.to,e.from);const i=Math.min(s,6);for(let e=0;e<=i;e++){if(r.includes(o.getDay())){return true}o=n.addDays(o,1)}return false}function _o(e,t,n=zn){return lo(e,t.from,false,n)||lo(e,t.to,false,n)||lo(t,e.from,false,n)||lo(t,e.to,false,n)}function Eo(e,t,n=zn){const r=Array.isArray(t)?t:[t];const o=r.filter((e=>typeof e!=="function"));const s=o.some((t=>{if(typeof t==="boolean")return t;if(n.isDate(t)){return lo(e,t,false,n)}if(vo(t,n)){return t.some((t=>lo(e,t,false,n)))}if(ho(t)){if(t.from&&t.to){return _o(e,{from:t.from,to:t.to},n)}return false}if(mo(t)){return Mo(e,t.dayOfWeek,n)}if(fo(t)){const r=n.isAfter(t.before,t.after);if(r){return _o(e,{from:n.addDays(t.after,1),to:n.addDays(t.before,-1)},n)}return bo(e.from,t,n)||bo(e.to,t,n)}if(po(t)||go(t)){return bo(e.from,t,n)||bo(e.to,t,n)}return false}));if(s){return true}const i=r.filter((e=>typeof e==="function"));if(i.length){let t=e.from;const r=n.differenceInCalendarDays(e.to,e.from);for(let e=0;e<=r;e++){if(i.some((e=>e(t)))){return true}t=n.addDays(t,1)}}return false}function No(e,t){const{disabled:n,excludeDisabled:r,selected:o,required:s,onSelect:i}=e;const[a,c]=io(o,i?o:undefined);const l=!i?a:o;const u=e=>l&&lo(l,e,false,t);const d=(o,a,u)=>{const{min:d,max:f}=e;const h=o?So(o,l,d,f,s,t):undefined;if(r&&n&&h?.from&&h.to){if(Eo({from:h.from,to:h.to},n,t)){h.from=o;h.to=undefined}}if(!i){c(h)}i?.(h,o,a,u);return h};return{selected:l,select:d,isSelected:u}}function Oo(e,t){const{selected:n,required:r,onSelect:o}=e;const[s,i]=io(n,o?n:undefined);const a=!o?s:n;const{isSameDay:c}=t;const l=e=>a?c(a,e):false;const u=(e,t,n)=>{let s=e;if(!r&&a&&a&&c(e,a)){s=undefined}if(!o){i(s)}if(r){o?.(s,e,t,n)}else{o?.(s,e,t,n)}return s};return{selected:a,select:u,isSelected:l}}function Ro(e,t){const n=Oo(e,t);const r=Co(e,t);const o=No(e,t);switch(e.mode){case"single":return n;case"multiple":return r;case"range":return o;default:return undefined}}function To(e){const{components:t,formatters:n,labels:r,dateLib:o,locale:u,classNames:d}=(0,i.useMemo)((()=>{const t={...ut,...e.locale};const n=new Bn({locale:t,weekStartsOn:e.broadcastCalendar?1:e.weekStartsOn,firstWeekContainsDate:e.firstWeekContainsDate,useAdditionalWeekYearTokens:e.useAdditionalWeekYearTokens,useAdditionalDayOfYearTokens:e.useAdditionalDayOfYearTokens,timeZone:e.timeZone,numerals:e.numerals},e.dateLib);return{dateLib:n,components:br(e.components),formatters:Or(e.formatters),labels:{...s,...e.labels},locale:t,classNames:{...wr(),...e.classNames}}}),[e.locale,e.broadcastCalendar,e.weekStartsOn,e.firstWeekContainsDate,e.useAdditionalWeekYearTokens,e.useAdditionalDayOfYearTokens,e.timeZone,e.numerals,e.dateLib,e.components,e.formatters,e.labels,e.classNames]);const{captionLayout:f,mode:h,onDayBlur:p,onDayClick:g,onDayFocus:m,onDayKeyDown:v,onDayMouseEnter:b,onDayMouseLeave:y,onNextClick:w,onPrevClick:x,showWeekNumber:D,styles:k}=e;const{formatCaption:C,formatDay:S,formatMonthDropdown:M,formatWeekNumber:_,formatWeekNumberHeader:E,formatWeekdayName:N,formatYearDropdown:O}=n;const R=ao(e,o);const{days:T,months:A,navStart:I,navEnd:F,previousMonth:L,nextMonth:P,goToMonth:W}=R;const j=ko(T,e,o);const{isSelected:B,select:z,selected:Y}=Ro(e,o)??{};const{blur:V,focused:U,isFocusTarget:H,moveFocus:X,setFocused:q}=Do(e,R,j,B??(()=>false),o);const{labelDayButton:Z,labelGridcell:J,labelGrid:G,labelMonthDropdown:$,labelNav:K,labelWeekday:Q,labelWeekNumber:ee,labelWeekNumberHeader:te,labelYearDropdown:ne}=r;const re=(0,i.useMemo)((()=>Ar(o,e.ISOWeek)),[o,e.ISOWeek]);const oe=h!==undefined||g!==undefined;const se=(0,i.useCallback)((()=>{if(!L)return;W(L);x?.(L)}),[L,W,x]);const ie=(0,i.useCallback)((()=>{if(!P)return;W(P);w?.(P)}),[W,P,w]);const ae=(0,i.useCallback)(((e,t)=>n=>{n.preventDefault();n.stopPropagation();q(e);z?.(e.date,t,n);g?.(e.date,t,n)}),[z,g,q]);const ce=(0,i.useCallback)(((e,t)=>n=>{q(e);m?.(e.date,t,n)}),[m,q]);const le=(0,i.useCallback)(((e,t)=>n=>{V();p?.(e.date,t,n)}),[V,p]);const ue=(0,i.useCallback)(((t,n)=>r=>{const o={ArrowLeft:["day",e.dir==="rtl"?"after":"before"],ArrowRight:["day",e.dir==="rtl"?"before":"after"],ArrowDown:["week","after"],ArrowUp:["week","before"],PageUp:[r.shiftKey?"year":"month","before"],PageDown:[r.shiftKey?"year":"month","after"],Home:["startOfWeek","before"],End:["endOfWeek","after"]};if(o[r.key]){r.preventDefault();r.stopPropagation();const[e,t]=o[r.key];X(e,t)}v?.(t.date,n,r)}),[X,v,e.dir]);const de=(0,i.useCallback)(((e,t)=>n=>{b?.(e.date,t,n)}),[b]);const fe=(0,i.useCallback)(((e,t)=>n=>{y?.(e.date,t,n)}),[y]);const he=(0,i.useCallback)((e=>t=>{const n=Number(t.target.value);const r=o.setMonth(o.startOfMonth(e),n);W(r)}),[o,W]);const pe=(0,i.useCallback)((e=>t=>{const n=Number(t.target.value);const r=o.setYear(o.startOfMonth(e),n);W(r)}),[o,W]);const{className:ge,style:me}=(0,i.useMemo)((()=>({className:[d[a.Root],e.className].filter(Boolean).join(" "),style:{...k?.[a.Root],...e.style}})),[d,e.className,e.style,k]);const ve=yr(e);const be={dayPickerProps:e,selected:Y,select:z,isSelected:B,months:A,nextMonth:P,previousMonth:L,goToMonth:W,getModifiers:j,components:t,classNames:d,styles:k,labels:r,formatters:n};return i.createElement(nr.Provider,{value:be},i.createElement(t.Root,{className:ge,style:me,dir:e.dir,id:e.id,lang:e.lang,nonce:e.nonce,title:e.title,role:e.role,"aria-label":e["aria-label"],...ve},i.createElement(t.Months,{className:d[a.Months],style:k?.[a.Months]},!e.hideNavigation&&i.createElement(t.Nav,{className:d[a.Nav],style:k?.[a.Nav],"aria-label":K(),onPreviousClick:se,onNextClick:ie,previousMonth:L,nextMonth:P}),A.map(((r,s)=>{const p=Rr(r.date,I,F,n,o);const g=Ir(I,F,n,o);return i.createElement(t.Month,{className:d[a.Month],style:k?.[a.Month],key:s,displayIndex:s,calendarMonth:r},i.createElement(t.MonthCaption,{className:d[a.MonthCaption],style:k?.[a.MonthCaption],calendarMonth:r,displayIndex:s},f?.startsWith("dropdown")?i.createElement(t.DropdownNav,{className:d[a.Dropdowns],style:k?.[a.Dropdowns]},f==="dropdown"||f==="dropdown-months"?i.createElement(t.MonthsDropdown,{className:d[a.MonthsDropdown],"aria-label":$(),classNames:d,components:t,disabled:Boolean(e.disableNavigation),onChange:he(r.date),options:p,style:k?.[a.Dropdown],value:o.getMonth(r.date)}):i.createElement("span",{role:"status","aria-live":"polite"},M(r.date,o)),f==="dropdown"||f==="dropdown-years"?i.createElement(t.YearsDropdown,{className:d[a.YearsDropdown],"aria-label":ne(o.options),classNames:d,components:t,disabled:Boolean(e.disableNavigation),onChange:pe(r.date),options:g,style:k?.[a.Dropdown],value:o.getYear(r.date)}):i.createElement("span",{role:"status","aria-live":"polite"},O(r.date,o))):i.createElement(t.CaptionLabel,{className:d[a.CaptionLabel],role:"status","aria-live":"polite"},C(r.date,o.options,o))),i.createElement(t.MonthGrid,{role:"grid","aria-multiselectable":h==="multiple"||h==="range","aria-label":G(r.date,o.options,o)||undefined,className:d[a.MonthGrid],style:k?.[a.MonthGrid]},!e.hideWeekdays&&i.createElement(t.Weekdays,{className:d[a.Weekdays],style:k?.[a.Weekdays]},D&&i.createElement(t.WeekNumberHeader,{"aria-label":te(o.options),className:d[a.WeekNumberHeader],style:k?.[a.WeekNumberHeader],scope:"col"},E()),re.map(((e,n)=>i.createElement(t.Weekday,{"aria-label":Q(e,o.options,o),className:d[a.Weekday],key:n,style:k?.[a.Weekday],scope:"col"},N(e,o.options,o))))),i.createElement(t.Weeks,{className:d[a.Weeks],style:k?.[a.Weeks]},r.weeks.map(((n,r)=>i.createElement(t.Week,{className:d[a.Week],key:n.weekNumber,style:k?.[a.Week],week:n},D&&i.createElement(t.WeekNumber,{week:n,style:k?.[a.WeekNumber],"aria-label":ee(n.weekNumber,{locale:u}),className:d[a.WeekNumber],scope:"row",role:"rowheader"},_(n.weekNumber)),n.days.map((n=>{const{date:r}=n;const s=j(n);s[c.focused]=!s.hidden&&Boolean(U?.isEqualTo(n));s[l.selected]=!s.disabled&&(B?.(r)||s.selected);if(ho(Y)){const{from:e,to:t}=Y;s[l.range_start]=Boolean(e&&t&&o.isSameDay(r,e));s[l.range_end]=Boolean(e&&t&&o.isSameDay(r,t));s[l.range_middle]=lo(Y,r,true,o)}const u=Tr(s,k,e.modifiersStyles);const f=Vn(s,d,e.modifiersClassNames);const h=!oe&&!s.hidden?J(r,s,o.options,o):undefined;return i.createElement(t.Day,{key:`${o.format(r,"yyyy-MM-dd")}_${o.format(n.displayMonth,"yyyy-MM")}`,day:n,modifiers:s,className:f.join(" "),style:u,role:"gridcell","aria-selected":s.selected||undefined,"aria-label":h,"data-day":o.format(r,"yyyy-MM-dd"),"data-month":n.outside?o.format(r,"yyyy-MM"):undefined,"data-selected":s.selected||undefined,"data-disabled":s.disabled||undefined,"data-hidden":s.hidden||undefined,"data-outside":n.outside||undefined,"data-focused":s.focused||undefined,"data-today":s.today||undefined},!s.hidden&&oe?i.createElement(t.DayButton,{className:d[a.DayButton],style:k?.[a.DayButton],type:"button",day:n,modifiers:s,disabled:s.disabled||undefined,tabIndex:H(n)?0:-1,"aria-label":Z(r,s,o.options,o),onClick:ae(n,s),onBlur:le(n,s),onFocus:ce(n,s),onKeyDown:ue(n,s),onMouseEnter:de(n,s),onMouseLeave:fe(n,s)},S(r,o.options,o)):!s.hidden&&S(n.date,o.options,o))}))))))))}))),e.footer&&i.createElement(t.Footer,{className:d[a.Footer],style:k?.[a.Footer],role:"status","aria-live":"polite"},e.footer)))}const Ao=null&&MonthCaption;const Io=null&&Week;const Fo=null&&useDayPicker},4805:(e,t,n)=>{n.d(t,{Dq:()=>De,Gc:()=>M,Qr:()=>j,RV:()=>_,cI:()=>He});var r=n(7363);var o=e=>e.type==="checkbox";var s=e=>e instanceof Date;var i=e=>e==null;const a=e=>typeof e==="object";var c=e=>!i(e)&&!Array.isArray(e)&&a(e)&&!s(e);var l=e=>c(e)&&e.target?o(e.target)?e.target.checked:e.target.value:e;var u=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e;var d=(e,t)=>e.has(u(t));var f=e=>{const t=e.constructor&&e.constructor.prototype;return c(t)&&t.hasOwnProperty("isPrototypeOf")};var h=typeof window!=="undefined"&&typeof window.HTMLElement!=="undefined"&&typeof document!=="undefined";function p(e){let t;const n=Array.isArray(e);const r=typeof FileList!=="undefined"?e instanceof FileList:false;if(e instanceof Date){t=new Date(e)}else if(e instanceof Set){t=new Set(e)}else if(!(h&&(e instanceof Blob||r))&&(n||c(e))){t=n?[]:{};if(!n&&!f(e)){t=e}else{for(const n in e){if(e.hasOwnProperty(n)){t[n]=p(e[n])}}}}else{return e}return t}var g=e=>Array.isArray(e)?e.filter(Boolean):[];var m=e=>e===undefined;var v=(e,t,n)=>{if(!t||!c(e)){return n}const r=g(t.split(/[,[\].]+?/)).reduce(((e,t)=>i(e)?e:e[t]),e);return m(r)||r===e?m(e[t])?n:e[t]:r};var b=e=>typeof e==="boolean";var y=e=>/^\w*$/.test(e);var w=e=>g(e.replace(/["|']|\]/g,"").split(/\.|\[/));var x=(e,t,n)=>{let r=-1;const o=y(t)?[t]:w(t);const s=o.length;const i=s-1;while(++r<s){const t=o[r];let s=n;if(r!==i){const n=e[t];s=c(n)||Array.isArray(n)?n:!isNaN(+o[r+1])?[]:{}}if(t==="__proto__"||t==="constructor"||t==="prototype"){return}e[t]=s;e=e[t]}return e};const D={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"};const k={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"};const C={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"};const S=r.createContext(null);const M=()=>r.useContext(S);const _=e=>{const{children:t,...n}=e;return r.createElement(S.Provider,{value:n},t)};var E=(e,t,n,r=true)=>{const o={defaultValues:t._defaultValues};for(const s in e){Object.defineProperty(o,s,{get:()=>{const o=s;if(t._proxyFormState[o]!==k.all){t._proxyFormState[o]=!r||k.all}n&&(n[o]=true);return e[o]}})}return o};var N=e=>c(e)&&!Object.keys(e).length;var O=(e,t,n,r)=>{n(e);const{name:o,...s}=e;return N(s)||Object.keys(s).length>=Object.keys(t).length||Object.keys(s).find((e=>t[e]===(!r||k.all)))};var R=e=>Array.isArray(e)?e:[e];var T=(e,t,n)=>!e||!t||e===t||R(e).some((e=>e&&(n?e===t:e.startsWith(t)||t.startsWith(e))));function A(e){const t=r.useRef(e);t.current=e;r.useEffect((()=>{const n=!e.disabled&&t.current.subject&&t.current.subject.subscribe({next:t.current.next});return()=>{n&&n.unsubscribe()}}),[e.disabled])}function I(e){const t=M();const{control:n=t.control,disabled:o,name:s,exact:i}=e||{};const[a,c]=r.useState(n._formState);const l=r.useRef(true);const u=r.useRef({isDirty:false,isLoading:false,dirtyFields:false,touchedFields:false,validatingFields:false,isValidating:false,isValid:false,errors:false});const d=r.useRef(s);d.current=s;A({disabled:o,next:e=>l.current&&T(d.current,e.name,i)&&O(e,u.current,n._updateFormState)&&c({...n._formState,...e}),subject:n._subjects.state});r.useEffect((()=>{l.current=true;u.current.isValid&&n._updateValid(true);return()=>{l.current=false}}),[n]);return r.useMemo((()=>E(a,n,u.current,false)),[a,n])}var F=e=>typeof e==="string";var L=(e,t,n,r,o)=>{if(F(e)){r&&t.watch.add(e);return v(n,e,o)}if(Array.isArray(e)){return e.map((e=>(r&&t.watch.add(e),v(n,e))))}r&&(t.watchAll=true);return n};function P(e){const t=M();const{control:n=t.control,name:o,defaultValue:s,disabled:i,exact:a}=e||{};const c=r.useRef(o);c.current=o;A({disabled:i,subject:n._subjects.values,next:e=>{if(T(c.current,e.name,a)){u(p(L(c.current,n._names,e.values||n._formValues,false,s)))}}});const[l,u]=r.useState(n._getWatch(o,s));r.useEffect((()=>n._removeUnmounted()));return l}function W(e){const t=M();const{name:n,disabled:o,control:s=t.control,shouldUnregister:i}=e;const a=d(s._names.array,n);const c=P({control:s,name:n,defaultValue:v(s._formValues,n,v(s._defaultValues,n,e.defaultValue)),exact:true});const u=I({control:s,name:n,exact:true});const f=r.useRef(s.register(n,{...e.rules,value:c,...b(e.disabled)?{disabled:e.disabled}:{}}));const h=r.useMemo((()=>Object.defineProperties({},{invalid:{enumerable:true,get:()=>!!v(u.errors,n)},isDirty:{enumerable:true,get:()=>!!v(u.dirtyFields,n)},isTouched:{enumerable:true,get:()=>!!v(u.touchedFields,n)},isValidating:{enumerable:true,get:()=>!!v(u.validatingFields,n)},error:{enumerable:true,get:()=>v(u.errors,n)}})),[u,n]);const g=r.useMemo((()=>({name:n,value:c,...b(o)||u.disabled?{disabled:u.disabled||o}:{},onChange:e=>f.current.onChange({target:{value:l(e),name:n},type:D.CHANGE}),onBlur:()=>f.current.onBlur({target:{value:v(s._formValues,n),name:n},type:D.BLUR}),ref:e=>{const t=v(s._fields,n);if(t&&e){t._f.ref={focus:()=>e.focus(),select:()=>e.select(),setCustomValidity:t=>e.setCustomValidity(t),reportValidity:()=>e.reportValidity()}}}})),[n,s._formValues,o,u.disabled,c,s._fields]);r.useEffect((()=>{const e=s._options.shouldUnregister||i;const t=(e,t)=>{const n=v(s._fields,e);if(n&&n._f){n._f.mount=t}};t(n,true);if(e){const e=p(v(s._options.defaultValues,n));x(s._defaultValues,n,e);if(m(v(s._formValues,n))){x(s._formValues,n,e)}}!a&&s.register(n);return()=>{(a?e&&!s._state.action:e)?s.unregister(n):t(n,false)}}),[n,s,a,i]);r.useEffect((()=>{s._updateDisabledField({disabled:o,fields:s._fields,name:n})}),[o,n,s]);return r.useMemo((()=>({field:g,formState:u,fieldState:h})),[g,u,h])}const j=e=>e.render(W(e));const B=e=>{const t={};for(const n of Object.keys(e)){if(a(e[n])&&e[n]!==null){const r=B(e[n]);for(const e of Object.keys(r)){t[`${n}.${e}`]=r[e]}}else{t[n]=e[n]}}return t};const z="post";function Y(e){const t=M();const[n,r]=React.useState(false);const{control:o=t.control,onSubmit:s,children:i,action:a,method:c=z,headers:l,encType:u,onError:d,render:f,onSuccess:h,validateStatus:p,...g}=e;const m=async t=>{let n=false;let r="";await o.handleSubmit((async e=>{const i=new FormData;let f="";try{f=JSON.stringify(e)}catch(e){}const g=B(o._formValues);for(const e in g){i.append(e,g[e])}if(s){await s({data:e,event:t,method:c,formData:i,formDataJson:f})}if(a){try{const e=[l&&l["Content-Type"],u].some((e=>e&&e.includes("json")));const t=await fetch(String(a),{method:c,headers:{...l,...u?{"Content-Type":u}:{}},body:e?f:i});if(t&&(p?!p(t.status):t.status<200||t.status>=300)){n=true;d&&d({response:t});r=String(t.status)}else{h&&h({response:t})}}catch(e){n=true;d&&d({error:e})}}}))(t);if(n&&e.control){e.control._subjects.state.next({isSubmitSuccessful:false});e.control.setError("root.server",{type:r})}};React.useEffect((()=>{r(true)}),[]);return f?React.createElement(React.Fragment,null,f({submit:m})):React.createElement("form",{noValidate:n,action:a,method:c,encType:u,onSubmit:m,...g},i)}var V=(e,t,n,r,o)=>t?{...n[e],types:{...n[e]&&n[e].types?n[e].types:{},[r]:o||true}}:{};var U=()=>{const e=typeof performance==="undefined"?Date.now():performance.now()*1e3;return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(t=>{const n=(Math.random()*16+e)%16|0;return(t=="x"?n:n&3|8).toString(16)}))};var H=(e,t,n={})=>n.shouldFocus||m(n.shouldFocus)?n.focusName||`${e}.${m(n.focusIndex)?t:n.focusIndex}.`:"";var X=e=>({isOnSubmit:!e||e===k.onSubmit,isOnBlur:e===k.onBlur,isOnChange:e===k.onChange,isOnAll:e===k.all,isOnTouch:e===k.onTouched});var q=(e,t,n)=>!n&&(t.watchAll||t.watch.has(e)||[...t.watch].some((t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length)))));const Z=(e,t,n,r)=>{for(const o of n||Object.keys(e)){const n=v(e,o);if(n){const{_f:e,...s}=n;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],o)&&!r){return true}else if(e.ref&&t(e.ref,e.name)&&!r){return true}else{if(Z(s,t)){break}}}else if(c(s)){if(Z(s,t)){break}}}}return};var J=(e,t,n)=>{const r=R(v(e,n));x(r,"root",t[n]);x(e,n,r);return e};var G=e=>e.type==="file";var $=e=>typeof e==="function";var K=e=>{if(!h){return false}const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)};var Q=e=>F(e);var ee=e=>e.type==="radio";var te=e=>e instanceof RegExp;const ne={value:false,isValid:false};const re={value:true,isValid:true};var oe=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter((e=>e&&e.checked&&!e.disabled)).map((e=>e.value));return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!m(e[0].attributes.value)?m(e[0].value)||e[0].value===""?re:{value:e[0].value,isValid:true}:re:ne}return ne};const se={isValid:false,value:null};var ie=e=>Array.isArray(e)?e.reduce(((e,t)=>t&&t.checked&&!t.disabled?{isValid:true,value:t.value}:e),se):se;function ae(e,t,n="validate"){if(Q(e)||Array.isArray(e)&&e.every(Q)||b(e)&&!e){return{type:n,message:Q(e)?e:"",ref:t}}}var ce=e=>c(e)&&!te(e)?e:{value:e,message:""};var le=async(e,t,n,r,s,a)=>{const{ref:l,refs:u,required:d,maxLength:f,minLength:h,min:p,max:g,pattern:y,validate:w,name:x,valueAsNumber:D,mount:k}=e._f;const S=v(n,x);if(!k||t.has(x)){return{}}const M=u?u[0]:l;const _=e=>{if(s&&M.reportValidity){M.setCustomValidity(b(e)?"":e||"");M.reportValidity()}};const E={};const O=ee(l);const R=o(l);const T=O||R;const A=(D||G(l))&&m(l.value)&&m(S)||K(l)&&l.value===""||S===""||Array.isArray(S)&&!S.length;const I=V.bind(null,x,r,E);const L=(e,t,n,r=C.maxLength,o=C.minLength)=>{const s=e?t:n;E[x]={type:e?r:o,message:s,ref:l,...I(e?r:o,s)}};if(a?!Array.isArray(S)||!S.length:d&&(!T&&(A||i(S))||b(S)&&!S||R&&!oe(u).isValid||O&&!ie(u).isValid)){const{value:e,message:t}=Q(d)?{value:!!d,message:d}:ce(d);if(e){E[x]={type:C.required,message:t,ref:M,...I(C.required,t)};if(!r){_(t);return E}}}if(!A&&(!i(p)||!i(g))){let e;let t;const n=ce(g);const o=ce(p);if(!i(S)&&!isNaN(S)){const r=l.valueAsNumber||(S?+S:S);if(!i(n.value)){e=r>n.value}if(!i(o.value)){t=r<o.value}}else{const r=l.valueAsDate||new Date(S);const s=e=>new Date((new Date).toDateString()+" "+e);const i=l.type=="time";const a=l.type=="week";if(F(n.value)&&S){e=i?s(S)>s(n.value):a?S>n.value:r>new Date(n.value)}if(F(o.value)&&S){t=i?s(S)<s(o.value):a?S<o.value:r<new Date(o.value)}}if(e||t){L(!!e,n.message,o.message,C.max,C.min);if(!r){_(E[x].message);return E}}}if((f||h)&&!A&&(F(S)||a&&Array.isArray(S))){const e=ce(f);const t=ce(h);const n=!i(e.value)&&S.length>+e.value;const o=!i(t.value)&&S.length<+t.value;if(n||o){L(n,e.message,t.message);if(!r){_(E[x].message);return E}}}if(y&&!A&&F(S)){const{value:e,message:t}=ce(y);if(te(e)&&!S.match(e)){E[x]={type:C.pattern,message:t,ref:l,...I(C.pattern,t)};if(!r){_(t);return E}}}if(w){if($(w)){const e=await w(S,n);const t=ae(e,M);if(t){E[x]={...t,...I(C.validate,t.message)};if(!r){_(t.message);return E}}}else if(c(w)){let e={};for(const t in w){if(!N(e)&&!r){break}const o=ae(await w[t](S,n),M,t);if(o){e={...o,...I(t,o.message)};_(o.message);if(r){E[x]=e}}}if(!N(e)){E[x]={ref:M,...e};if(!r){return E}}}}_(true);return E};var ue=(e,t)=>[...e,...R(t)];var de=e=>Array.isArray(e)?e.map((()=>undefined)):undefined;function fe(e,t,n){return[...e.slice(0,t),...R(n),...e.slice(t)]}var he=(e,t,n)=>{if(!Array.isArray(e)){return[]}if(m(e[n])){e[n]=undefined}e.splice(n,0,e.splice(t,1)[0]);return e};var pe=(e,t)=>[...R(t),...R(e)];function ge(e,t){let n=0;const r=[...e];for(const e of t){r.splice(e-n,1);n++}return g(r).length?r:[]}var me=(e,t)=>m(t)?[]:ge(e,R(t).sort(((e,t)=>e-t)));var ve=(e,t,n)=>{[e[t],e[n]]=[e[n],e[t]]};function be(e,t){const n=t.slice(0,-1).length;let r=0;while(r<n){e=m(e)?r++:e[t[r++]]}return e}function ye(e){for(const t in e){if(e.hasOwnProperty(t)&&!m(e[t])){return false}}return true}function we(e,t){const n=Array.isArray(t)?t:y(t)?[t]:w(t);const r=n.length===1?e:be(e,n);const o=n.length-1;const s=n[o];if(r){delete r[s]}if(o!==0&&(c(r)&&N(r)||Array.isArray(r)&&ye(r))){we(e,n.slice(0,-1))}return e}var xe=(e,t,n)=>{e[t]=n;return e};function De(e){const t=M();const{control:n=t.control,name:o,keyName:s="id",shouldUnregister:i,rules:a}=e;const[c,l]=r.useState(n._getFieldArray(o));const u=r.useRef(n._getFieldArray(o).map(U));const d=r.useRef(c);const f=r.useRef(o);const h=r.useRef(false);f.current=o;d.current=c;n._names.array.add(o);a&&n.register(o,a);A({next:({values:e,name:t})=>{if(t===f.current||!t){const t=v(e,f.current);if(Array.isArray(t)){l(t);u.current=t.map(U)}}},subject:n._subjects.array});const g=r.useCallback((e=>{h.current=true;n._updateFieldArray(o,e)}),[n,o]);const m=(e,t)=>{const r=R(p(e));const s=ue(n._getFieldArray(o),r);n._names.focus=H(o,s.length-1,t);u.current=ue(u.current,r.map(U));g(s);l(s);n._updateFieldArray(o,s,ue,{argA:de(e)})};const b=(e,t)=>{const r=R(p(e));const s=pe(n._getFieldArray(o),r);n._names.focus=H(o,0,t);u.current=pe(u.current,r.map(U));g(s);l(s);n._updateFieldArray(o,s,pe,{argA:de(e)})};const y=e=>{const t=me(n._getFieldArray(o),e);u.current=me(u.current,e);g(t);l(t);!Array.isArray(v(n._fields,o))&&x(n._fields,o,undefined);n._updateFieldArray(o,t,me,{argA:e})};const w=(e,t,r)=>{const s=R(p(t));const i=fe(n._getFieldArray(o),e,s);n._names.focus=H(o,e,r);u.current=fe(u.current,e,s.map(U));g(i);l(i);n._updateFieldArray(o,i,fe,{argA:e,argB:de(t)})};const D=(e,t)=>{const r=n._getFieldArray(o);ve(r,e,t);ve(u.current,e,t);g(r);l(r);n._updateFieldArray(o,r,ve,{argA:e,argB:t},false)};const C=(e,t)=>{const r=n._getFieldArray(o);he(r,e,t);he(u.current,e,t);g(r);l(r);n._updateFieldArray(o,r,he,{argA:e,argB:t},false)};const S=(e,t)=>{const r=p(t);const s=xe(n._getFieldArray(o),e,r);u.current=[...s].map(((t,n)=>!t||n===e?U():u.current[n]));g(s);l([...s]);n._updateFieldArray(o,s,xe,{argA:e,argB:r},true,false)};const _=e=>{const t=R(p(e));u.current=t.map(U);g([...t]);l([...t]);n._updateFieldArray(o,[...t],(e=>e),{},true,false)};r.useEffect((()=>{n._state.action=false;q(o,n._names)&&n._subjects.state.next({...n._formState});if(h.current&&(!X(n._options.mode).isOnSubmit||n._formState.isSubmitted)){if(n._options.resolver){n._executeSchema([o]).then((e=>{const t=v(e.errors,o);const r=v(n._formState.errors,o);if(r?!t&&r.type||t&&(r.type!==t.type||r.message!==t.message):t&&t.type){t?x(n._formState.errors,o,t):we(n._formState.errors,o);n._subjects.state.next({errors:n._formState.errors})}}))}else{const e=v(n._fields,o);if(e&&e._f&&!(X(n._options.reValidateMode).isOnSubmit&&X(n._options.mode).isOnSubmit)){le(e,n._names.disabled,n._formValues,n._options.criteriaMode===k.all,n._options.shouldUseNativeValidation,true).then((e=>!N(e)&&n._subjects.state.next({errors:J(n._formState.errors,e,o)})))}}}n._subjects.values.next({name:o,values:{...n._formValues}});n._names.focus&&Z(n._fields,((e,t)=>{if(n._names.focus&&t.startsWith(n._names.focus)&&e.focus){e.focus();return 1}return}));n._names.focus="";n._updateValid();h.current=false}),[c,o,n]);r.useEffect((()=>{!v(n._formValues,o)&&n._updateFieldArray(o);return()=>{(n._options.shouldUnregister||i)&&n.unregister(o)}}),[o,n,s,i]);return{swap:r.useCallback(D,[g,o,n]),move:r.useCallback(C,[g,o,n]),prepend:r.useCallback(b,[g,o,n]),append:r.useCallback(m,[g,o,n]),remove:r.useCallback(y,[g,o,n]),insert:r.useCallback(w,[g,o,n]),update:r.useCallback(S,[g,o,n]),replace:r.useCallback(_,[g,o,n]),fields:r.useMemo((()=>c.map(((e,t)=>({...e,[s]:u.current[t]||U()})))),[c,s])}}var ke=()=>{let e=[];const t=t=>{for(const n of e){n.next&&n.next(t)}};const n=t=>{e.push(t);return{unsubscribe:()=>{e=e.filter((e=>e!==t))}}};const r=()=>{e=[]};return{get observers(){return e},next:t,subscribe:n,unsubscribe:r}};var Ce=e=>i(e)||!a(e);function Se(e,t){if(Ce(e)||Ce(t)){return e===t}if(s(e)&&s(t)){return e.getTime()===t.getTime()}const n=Object.keys(e);const r=Object.keys(t);if(n.length!==r.length){return false}for(const o of n){const n=e[o];if(!r.includes(o)){return false}if(o!=="ref"){const e=t[o];if(s(n)&&s(e)||c(n)&&c(e)||Array.isArray(n)&&Array.isArray(e)?!Se(n,e):n!==e){return false}}}return true}var Me=e=>e.type===`select-multiple`;var _e=e=>ee(e)||o(e);var Ee=e=>K(e)&&e.isConnected;var Ne=e=>{for(const t in e){if($(e[t])){return true}}return false};function Oe(e,t={}){const n=Array.isArray(e);if(c(e)||n){for(const n in e){if(Array.isArray(e[n])||c(e[n])&&!Ne(e[n])){t[n]=Array.isArray(e[n])?[]:{};Oe(e[n],t[n])}else if(!i(e[n])){t[n]=true}}}return t}function Re(e,t,n){const r=Array.isArray(e);if(c(e)||r){for(const r in e){if(Array.isArray(e[r])||c(e[r])&&!Ne(e[r])){if(m(t)||Ce(n[r])){n[r]=Array.isArray(e[r])?Oe(e[r],[]):{...Oe(e[r])}}else{Re(e[r],i(t)?{}:t[r],n[r])}}else{n[r]=!Se(e[r],t[r])}}}return n}var Te=(e,t)=>Re(e,t,Oe(t));var Ae=(e,{valueAsNumber:t,valueAsDate:n,setValueAs:r})=>m(e)?e:t?e===""?NaN:e?+e:e:n&&F(e)?new Date(e):r?r(e):e;function Ie(e){const t=e.ref;if(G(t)){return t.files}if(ee(t)){return ie(e.refs).value}if(Me(t)){return[...t.selectedOptions].map((({value:e})=>e))}if(o(t)){return oe(e.refs).value}return Ae(m(t.value)?e.ref.value:t.value,e)}var Fe=(e,t,n,r)=>{const o={};for(const n of e){const e=v(t,n);e&&x(o,n,e._f)}return{criteriaMode:n,names:[...e],fields:o,shouldUseNativeValidation:r}};var Le=e=>m(e)?e:te(e)?e.source:c(e)?te(e.value)?e.value.source:e.value:e;const Pe="AsyncFunction";var We=e=>!!e&&!!e.validate&&!!($(e.validate)&&e.validate.constructor.name===Pe||c(e.validate)&&Object.values(e.validate).find((e=>e.constructor.name===Pe)));var je=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate);function Be(e,t,n){const r=v(e,n);if(r||y(n)){return{error:r,name:n}}const o=n.split(".");while(o.length){const r=o.join(".");const s=v(t,r);const i=v(e,r);if(s&&!Array.isArray(s)&&n!==r){return{name:n}}if(i&&i.type){return{name:r,error:i}}o.pop()}return{name:n}}var ze=(e,t,n,r,o)=>{if(o.isOnAll){return false}else if(!n&&o.isOnTouch){return!(t||e)}else if(n?r.isOnBlur:o.isOnBlur){return!e}else if(n?r.isOnChange:o.isOnChange){return e}return true};var Ye=(e,t)=>!g(v(e,t)).length&&we(e,t);const Ve={mode:k.onSubmit,reValidateMode:k.onChange,shouldFocusError:true};function Ue(e={}){let t={...Ve,...e};let n={submitCount:0,isDirty:false,isLoading:$(t.defaultValues),isValidating:false,isSubmitted:false,isSubmitting:false,isSubmitSuccessful:false,isValid:false,touchedFields:{},dirtyFields:{},validatingFields:{},errors:t.errors||{},disabled:t.disabled||false};let r={};let a=c(t.defaultValues)||c(t.values)?p(t.defaultValues||t.values)||{}:{};let u=t.shouldUnregister?{}:p(a);let f={action:false,mount:false,watch:false};let y={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set};let w;let C=0;const S={isDirty:false,dirtyFields:false,validatingFields:false,touchedFields:false,isValidating:false,isValid:false,errors:false};const M={values:ke(),array:ke(),state:ke()};const _=X(t.mode);const E=X(t.reValidateMode);const O=t.criteriaMode===k.all;const T=e=>t=>{clearTimeout(C);C=setTimeout(e,t)};const A=async e=>{if(!t.disabled&&(S.isValid||e)){const e=t.resolver?N((await V()).errors):await H(r,true);if(e!==n.isValid){M.state.next({isValid:e})}}};const I=(e,r)=>{if(!t.disabled&&(S.isValidating||S.validatingFields)){(e||Array.from(y.mount)).forEach((e=>{if(e){r?x(n.validatingFields,e,r):we(n.validatingFields,e)}}));M.state.next({validatingFields:n.validatingFields,isValidating:!N(n.validatingFields)})}};const P=(e,o=[],s,i,c=true,l=true)=>{if(i&&s&&!t.disabled){f.action=true;if(l&&Array.isArray(v(r,e))){const t=s(v(r,e),i.argA,i.argB);c&&x(r,e,t)}if(l&&Array.isArray(v(n.errors,e))){const t=s(v(n.errors,e),i.argA,i.argB);c&&x(n.errors,e,t);Ye(n.errors,e)}if(S.touchedFields&&l&&Array.isArray(v(n.touchedFields,e))){const t=s(v(n.touchedFields,e),i.argA,i.argB);c&&x(n.touchedFields,e,t)}if(S.dirtyFields){n.dirtyFields=Te(a,u)}M.state.next({name:e,isDirty:ee(e,o),dirtyFields:n.dirtyFields,errors:n.errors,isValid:n.isValid})}else{x(u,e,o)}};const W=(e,t)=>{x(n.errors,e,t);M.state.next({errors:n.errors})};const j=e=>{n.errors=e;M.state.next({errors:n.errors,isValid:false})};const B=(e,t,n,o)=>{const s=v(r,e);if(s){const r=v(u,e,m(n)?v(a,e):n);m(r)||o&&o.defaultChecked||t?x(u,e,t?r:Ie(s._f)):re(e,r);f.mount&&A()}};const z=(e,o,s,i,c)=>{let l=false;let u=false;const d={name:e};if(!t.disabled){const t=!!(v(r,e)&&v(r,e)._f&&v(r,e)._f.disabled);if(!s||i){if(S.isDirty){u=n.isDirty;n.isDirty=d.isDirty=ee();l=u!==d.isDirty}const r=t||Se(v(a,e),o);u=!!(!t&&v(n.dirtyFields,e));r||t?we(n.dirtyFields,e):x(n.dirtyFields,e,true);d.dirtyFields=n.dirtyFields;l=l||S.dirtyFields&&u!==!r}if(s){const t=v(n.touchedFields,e);if(!t){x(n.touchedFields,e,s);d.touchedFields=n.touchedFields;l=l||S.touchedFields&&t!==s}}l&&c&&M.state.next(d)}return l?d:{}};const Y=(e,r,o,s)=>{const i=v(n.errors,e);const a=S.isValid&&b(r)&&n.isValid!==r;if(t.delayError&&o){w=T((()=>W(e,o)));w(t.delayError)}else{clearTimeout(C);w=null;o?x(n.errors,e,o):we(n.errors,e)}if((o?!Se(i,o):i)||!N(s)||a){const t={...s,...a&&b(r)?{isValid:r}:{},errors:n.errors,name:e};n={...n,...t};M.state.next(t)}};const V=async e=>{I(e,true);const n=await t.resolver(u,t.context,Fe(e||y.mount,r,t.criteriaMode,t.shouldUseNativeValidation));I(e);return n};const U=async e=>{const{errors:t}=await V(e);if(e){for(const r of e){const e=v(t,r);e?x(n.errors,r,e):we(n.errors,r)}}else{n.errors=t}return t};const H=async(e,r,o={valid:true})=>{for(const s in e){const i=e[s];if(i){const{_f:e,...a}=i;if(e){const a=y.array.has(e.name);const c=i._f&&We(i._f);if(c&&S.validatingFields){I([s],true)}const l=await le(i,y.disabled,u,O,t.shouldUseNativeValidation&&!r,a);if(c&&S.validatingFields){I([s])}if(l[e.name]){o.valid=false;if(r){break}}!r&&(v(l,e.name)?a?J(n.errors,l,e.name):x(n.errors,e.name,l[e.name]):we(n.errors,e.name))}!N(a)&&await H(a,r,o)}}return o.valid};const Q=()=>{for(const e of y.unMount){const t=v(r,e);t&&(t._f.refs?t._f.refs.every((e=>!Ee(e))):!Ee(t._f.ref))&&ge(e)}y.unMount=new Set};const ee=(e,n)=>!t.disabled&&(e&&n&&x(u,e,n),!Se(ue(),a));const te=(e,t,n)=>L(e,y,{...f.mount?u:m(t)?a:F(e)?{[e]:t}:t},n,t);const ne=e=>g(v(f.mount?u:a,e,t.shouldUnregister?v(a,e,[]):[]));const re=(e,t,n={})=>{const s=v(r,e);let a=t;if(s){const n=s._f;if(n){!n.disabled&&x(u,e,Ae(t,n));a=K(n.ref)&&i(t)?"":t;if(Me(n.ref)){[...n.ref.options].forEach((e=>e.selected=a.includes(e.value)))}else if(n.refs){if(o(n.ref)){n.refs.length>1?n.refs.forEach((e=>(!e.defaultChecked||!e.disabled)&&(e.checked=Array.isArray(a)?!!a.find((t=>t===e.value)):a===e.value))):n.refs[0]&&(n.refs[0].checked=!!a)}else{n.refs.forEach((e=>e.checked=e.value===a))}}else if(G(n.ref)){n.ref.value=""}else{n.ref.value=a;if(!n.ref.type){M.values.next({name:e,values:{...u}})}}}}(n.shouldDirty||n.shouldTouch)&&z(e,a,n.shouldTouch,n.shouldDirty,true);n.shouldValidate&&ce(e)};const oe=(e,t,n)=>{for(const o in t){const i=t[o];const a=`${e}.${o}`;const l=v(r,a);(y.array.has(e)||c(i)||l&&!l._f)&&!s(i)?oe(a,i,n):re(a,i,n)}};const se=(e,t,o={})=>{const s=v(r,e);const c=y.array.has(e);const l=p(t);x(u,e,l);if(c){M.array.next({name:e,values:{...u}});if((S.isDirty||S.dirtyFields)&&o.shouldDirty){M.state.next({name:e,dirtyFields:Te(a,u),isDirty:ee(e,l)})}}else{s&&!s._f&&!i(l)?oe(e,l,o):re(e,l,o)}q(e,y)&&M.state.next({...n});M.values.next({name:f.mount?e:undefined,values:{...u}})};const ie=async e=>{f.mount=true;const o=e.target;let i=o.name;let a=true;const c=v(r,i);const d=()=>o.type?Ie(c._f):l(e);const h=e=>{a=Number.isNaN(e)||s(e)&&isNaN(e.getTime())||Se(e,v(u,i,e))};if(c){let o;let s;const l=d();const f=e.type===D.BLUR||e.type===D.FOCUS_OUT;const p=!je(c._f)&&!t.resolver&&!v(n.errors,i)&&!c._f.deps||ze(f,v(n.touchedFields,i),n.isSubmitted,E,_);const g=q(i,y,f);x(u,i,l);if(f){c._f.onBlur&&c._f.onBlur(e);w&&w(0)}else if(c._f.onChange){c._f.onChange(e)}const m=z(i,l,f,false);const b=!N(m)||g;!f&&M.values.next({name:i,type:e.type,values:{...u}});if(p){if(S.isValid){if(t.mode==="onBlur"&&f){A()}else if(!f){A()}}return b&&M.state.next({name:i,...g?{}:m})}!f&&g&&M.state.next({...n});if(t.resolver){const{errors:e}=await V([i]);h(l);if(a){const t=Be(n.errors,r,i);const a=Be(e,r,t.name||i);o=a.error;i=a.name;s=N(e)}}else{I([i],true);o=(await le(c,y.disabled,u,O,t.shouldUseNativeValidation))[i];I([i]);h(l);if(a){if(o){s=false}else if(S.isValid){s=await H(r,true)}}}if(a){c._f.deps&&ce(c._f.deps);Y(i,s,o,m)}}};const ae=(e,t)=>{if(v(n.errors,t)&&e.focus){e.focus();return 1}return};const ce=async(e,o={})=>{let s;let i;const a=R(e);if(t.resolver){const t=await U(m(e)?e:a);s=N(t);i=e?!a.some((e=>v(t,e))):s}else if(e){i=(await Promise.all(a.map((async e=>{const t=v(r,e);return await H(t&&t._f?{[e]:t}:t)})))).every(Boolean);!(!i&&!n.isValid)&&A()}else{i=s=await H(r)}M.state.next({...!F(e)||S.isValid&&s!==n.isValid?{}:{name:e},...t.resolver||!e?{isValid:s}:{},errors:n.errors});o.shouldFocus&&!i&&Z(r,ae,e?a:y.mount);return i};const ue=e=>{const t={...f.mount?u:a};return m(e)?t:F(e)?v(t,e):e.map((e=>v(t,e)))};const de=(e,t)=>({invalid:!!v((t||n).errors,e),isDirty:!!v((t||n).dirtyFields,e),error:v((t||n).errors,e),isValidating:!!v(n.validatingFields,e),isTouched:!!v((t||n).touchedFields,e)});const fe=e=>{e&&R(e).forEach((e=>we(n.errors,e)));M.state.next({errors:e?n.errors:{}})};const he=(e,t,o)=>{const s=(v(r,e,{_f:{}})._f||{}).ref;const i=v(n.errors,e)||{};const{ref:a,message:c,type:l,...u}=i;x(n.errors,e,{...u,...t,ref:s});M.state.next({name:e,errors:n.errors,isValid:false});o&&o.shouldFocus&&s&&s.focus&&s.focus()};const pe=(e,t)=>$(e)?M.values.subscribe({next:n=>e(te(undefined,t),n)}):te(e,t,true);const ge=(e,o={})=>{for(const s of e?R(e):y.mount){y.mount.delete(s);y.array.delete(s);if(!o.keepValue){we(r,s);we(u,s)}!o.keepError&&we(n.errors,s);!o.keepDirty&&we(n.dirtyFields,s);!o.keepTouched&&we(n.touchedFields,s);!o.keepIsValidating&&we(n.validatingFields,s);!t.shouldUnregister&&!o.keepDefaultValue&&we(a,s)}M.values.next({values:{...u}});M.state.next({...n,...!o.keepDirty?{}:{isDirty:ee()}});!o.keepIsValid&&A()};const me=({disabled:e,name:t,field:n,fields:r})=>{if(b(e)&&f.mount||!!e||y.disabled.has(t)){e?y.disabled.add(t):y.disabled.delete(t);z(t,Ie(n?n._f:v(r,t)._f),false,false,true)}};const ve=(e,n={})=>{let o=v(r,e);const s=b(n.disabled)||b(t.disabled);x(r,e,{...o||{},_f:{...o&&o._f?o._f:{ref:{name:e}},name:e,mount:true,...n}});y.mount.add(e);if(o){me({field:o,disabled:b(n.disabled)?n.disabled:t.disabled,name:e})}else{B(e,true,n.value)}return{...s?{disabled:n.disabled||t.disabled}:{},...t.progressive?{required:!!n.required,min:Le(n.min),max:Le(n.max),minLength:Le(n.minLength),maxLength:Le(n.maxLength),pattern:Le(n.pattern)}:{},name:e,onChange:ie,onBlur:ie,ref:s=>{if(s){ve(e,n);o=v(r,e);const t=m(s.value)?s.querySelectorAll?s.querySelectorAll("input,select,textarea")[0]||s:s:s;const i=_e(t);const c=o._f.refs||[];if(i?c.find((e=>e===t)):t===o._f.ref){return}x(r,e,{_f:{...o._f,...i?{refs:[...c.filter(Ee),t,...Array.isArray(v(a,e))?[{}]:[]],ref:{type:t.type,name:e}}:{ref:t}}});B(e,false,undefined,t)}else{o=v(r,e,{});if(o._f){o._f.mount=false}(t.shouldUnregister||n.shouldUnregister)&&!(d(y.array,e)&&f.action)&&y.unMount.add(e)}}}};const be=()=>t.shouldFocusError&&Z(r,ae,y.mount);const ye=e=>{if(b(e)){M.state.next({disabled:e});Z(r,((t,n)=>{const o=v(r,n);if(o){t.disabled=o._f.disabled||e;if(Array.isArray(o._f.refs)){o._f.refs.forEach((t=>{t.disabled=o._f.disabled||e}))}}}),0,false)}};const xe=(e,o)=>async s=>{let i=undefined;if(s){s.preventDefault&&s.preventDefault();s.persist&&s.persist()}let a=p(u);if(y.disabled.size){for(const e of y.disabled){x(a,e,undefined)}}M.state.next({isSubmitting:true});if(t.resolver){const{errors:e,values:t}=await V();n.errors=e;a=t}else{await H(r)}we(n.errors,"root");if(N(n.errors)){M.state.next({errors:{}});try{await e(a,s)}catch(e){i=e}}else{if(o){await o({...n.errors},s)}be();setTimeout(be)}M.state.next({isSubmitted:true,isSubmitting:false,isSubmitSuccessful:N(n.errors)&&!i,submitCount:n.submitCount+1,errors:n.errors});if(i){throw i}};const De=(e,t={})=>{if(v(r,e)){if(m(t.defaultValue)){se(e,p(v(a,e)))}else{se(e,t.defaultValue);x(a,e,p(t.defaultValue))}if(!t.keepTouched){we(n.touchedFields,e)}if(!t.keepDirty){we(n.dirtyFields,e);n.isDirty=t.defaultValue?ee(e,p(v(a,e))):ee()}if(!t.keepError){we(n.errors,e);S.isValid&&A()}M.state.next({...n})}};const Ce=(e,o={})=>{const s=e?p(e):a;const i=p(s);const c=N(e);const l=c?a:i;if(!o.keepDefaultValues){a=s}if(!o.keepValues){if(o.keepDirtyValues){const e=new Set([...y.mount,...Object.keys(Te(a,u))]);for(const t of Array.from(e)){v(n.dirtyFields,t)?x(l,t,v(u,t)):se(t,v(l,t))}}else{if(h&&m(e)){for(const e of y.mount){const t=v(r,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(K(e)){const t=e.closest("form");if(t){t.reset();break}}}}}r={}}u=t.shouldUnregister?o.keepDefaultValues?p(a):{}:p(l);M.array.next({values:{...l}});M.values.next({values:{...l}})}y={mount:o.keepDirtyValues?y.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:false,focus:""};f.mount=!S.isValid||!!o.keepIsValid||!!o.keepDirtyValues;f.watch=!!t.shouldUnregister;M.state.next({submitCount:o.keepSubmitCount?n.submitCount:0,isDirty:c?false:o.keepDirty?n.isDirty:!!(o.keepDefaultValues&&!Se(e,a)),isSubmitted:o.keepIsSubmitted?n.isSubmitted:false,dirtyFields:c?{}:o.keepDirtyValues?o.keepDefaultValues&&u?Te(a,u):n.dirtyFields:o.keepDefaultValues&&e?Te(a,e):o.keepDirty?n.dirtyFields:{},touchedFields:o.keepTouched?n.touchedFields:{},errors:o.keepErrors?n.errors:{},isSubmitSuccessful:o.keepIsSubmitSuccessful?n.isSubmitSuccessful:false,isSubmitting:false})};const Ne=(e,t)=>Ce($(e)?e(u):e,t);const Oe=(e,t={})=>{const n=v(r,e);const o=n&&n._f;if(o){const e=o.refs?o.refs[0]:o.ref;if(e.focus){e.focus();t.shouldSelect&&$(e.select)&&e.select()}}};const Re=e=>{n={...n,...e}};const Pe=()=>$(t.defaultValues)&&t.defaultValues().then((e=>{Ne(e,t.resetOptions);M.state.next({isLoading:false})}));return{control:{register:ve,unregister:ge,getFieldState:de,handleSubmit:xe,setError:he,_executeSchema:V,_getWatch:te,_getDirty:ee,_updateValid:A,_removeUnmounted:Q,_updateFieldArray:P,_updateDisabledField:me,_getFieldArray:ne,_reset:Ce,_resetDefaultValues:Pe,_updateFormState:Re,_disableForm:ye,_subjects:M,_proxyFormState:S,_setErrors:j,get _fields(){return r},get _formValues(){return u},get _state(){return f},set _state(e){f=e},get _defaultValues(){return a},get _names(){return y},set _names(e){y=e},get _formState(){return n},set _formState(e){n=e},get _options(){return t},set _options(e){t={...t,...e}}},trigger:ce,register:ve,handleSubmit:xe,watch:pe,setValue:se,getValues:ue,reset:Ne,resetField:De,clearErrors:fe,unregister:ge,setError:he,setFocus:Oe,getFieldState:de}}function He(e={}){const t=r.useRef(undefined);const n=r.useRef(undefined);const[o,s]=r.useState({isDirty:false,isValidating:false,isLoading:$(e.defaultValues),isSubmitted:false,isSubmitting:false,isSubmitSuccessful:false,isValid:false,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||false,defaultValues:$(e.defaultValues)?undefined:e.defaultValues});if(!t.current){t.current={...Ue(e),formState:o}}const i=t.current.control;i._options=e;A({subject:i._subjects.state,next:e=>{if(O(e,i._proxyFormState,i._updateFormState,true)){s({...i._formState})}}});r.useEffect((()=>i._disableForm(e.disabled)),[i,e.disabled]);r.useEffect((()=>{if(i._proxyFormState.isDirty){const e=i._getDirty();if(e!==o.isDirty){i._subjects.state.next({isDirty:e})}}}),[i,o.isDirty]);r.useEffect((()=>{if(e.values&&!Se(e.values,n.current)){i._reset(e.values,i._options.resetOptions);n.current=e.values;s((e=>({...e})))}else{i._resetDefaultValues()}}),[e.values,i]);r.useEffect((()=>{if(e.errors){i._setErrors(e.errors)}}),[e.errors,i]);r.useEffect((()=>{if(!i._state.mount){i._updateValid();i._state.mount=true}if(i._state.watch){i._state.watch=false;i._subjects.state.next({...i._formState})}i._removeUnmounted()}));r.useEffect((()=>{e.shouldUnregister&&i._subjects.values.next({values:i._getWatch()})}),[e.shouldUnregister,i]);t.current.formState=E(o,i);return t.current}}}]);