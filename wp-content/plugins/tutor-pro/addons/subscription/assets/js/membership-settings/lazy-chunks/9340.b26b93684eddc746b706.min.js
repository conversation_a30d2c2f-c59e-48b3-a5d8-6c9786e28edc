"use strict";(self["webpackChunktutor_pro"]=self["webpackChunktutor_pro"]||[]).push([[9340],{8350:(e,t,r)=>{r.r(t);r.d(t,{default:()=>vu});var n=r(6657);var o=r(8003);var i=r(7536);var a=r(1605);var u=r(7976);var l=r(1457);var c=r(7363);var s=r.n(c);function d(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var f=s().forwardRef((function(e,t){var r=e.id,o=r===void 0?(0,l.x0)():r,i=e.name,a=e.labelCss,u=e.inputCss,c=e.label,s=c===void 0?"":c,d=e.checked,f=e.value,p=e.disabled,b=p===void 0?false:p,h=e.onChange,g=e.onBlur,y=e.isIndeterminate,m=y===void 0?false:y;var w=function e(t){h===null||h===void 0?void 0:h(!m?t.target.checked:true,t)};return(0,n.tZ)("label",{htmlFor:o,css:[v.container({disabled:b}),a,true?"":0,true?"":0]},(0,n.tZ)("input",{ref:t,id:o,name:i,type:"checkbox",value:f,checked:!!d,disabled:b,"aria-invalid":e["aria-invalid"],onChange:w,onBlur:g,css:[u,v.checkbox({label:!!s,isIndeterminate:m,disabled:b}),true?"":0,true?"":0]}),(0,n.tZ)("span",null),(0,n.tZ)("span",{css:[v.label({isDisabled:b}),a,true?"":0,true?"":0],title:s===null||s===void 0?void 0:s.toString()},s))}));var p=true?{name:"1sfig4b",styles:"cursor:not-allowed"}:0;var v={container:function e(t){var r=t.disabled,o=r===void 0?false:r;return(0,n.iv)("position:relative;display:flex;align-items:center;cursor:pointer;user-select:none;color:",a.Jv.text.title,";",o&&p,";"+(true?"":0),true?"":0)},label:function e(t){var r=t.isDisabled,o=r===void 0?false:r;return(0,n.iv)(u.c.caption(),";margin-top:",a.W0[2],";color:",a.Jv.text.title,";",o&&(0,n.iv)("color:",a.Jv.text.disable,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},checkbox:function e(t){var r=t.label,o=t.isIndeterminate,i=t.disabled;return(0,n.iv)("position:absolute;opacity:0!important;height:0;width:0;&+span{position:relative;cursor:pointer;display:inline-flex;align-items:center;",r&&(0,n.iv)("margin-right:",a.W0[10],";"+(true?"":0),true?"":0),";}&+span::before{content:'';background-color:",a.Jv.background.white,";border:1px solid ",a.Jv.stroke["default"],";border-radius:3px;width:20px;height:20px;}&:checked+span::before{background-image:url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0wLjE2NTM0NCA0Ljg5OTQ2QzAuMTEzMjM1IDQuODQ0OTcgMC4wNzE3MzQ2IDQuNzgxMTUgMC4wNDI5ODg3IDQuNzExM0MtMC4wMTQzMjk2IDQuNTU1NjQgLTAuMDE0MzI5NiA0LjM4NDQ5IDAuMDQyOTg4NyA0LjIyODg0QzAuMDcxMTU0OSA0LjE1ODY4IDAuMTEyNzIzIDQuMDk0NzUgMC4xNjUzNDQgNC4wNDA2OEwxLjAzMzgyIDMuMjAzNkMxLjA4NDkzIDMuMTQzNCAxLjE0ODkgMy4wOTU1NyAxLjIyMDk2IDMuMDYzNjlDMS4yOTAzMiAzLjAzMjEzIDEuMzY1NTQgMy4wMTU2OSAxLjQ0MTY3IDMuMDE1NDRDMS41MjQxOCAzLjAxMzgzIDEuNjA2MDUgMy4wMzAyOSAxLjY4MTU5IDMuMDYzNjlDMS43NTYyNiAzLjA5NzA3IDEuODIzODYgMy4xNDQ1NyAxLjg4MDcxIDMuMjAzNkw0LjUwMDU1IDUuODQyNjhMMTAuMTI0MSAwLjE4ODIwNUMxMC4xNzk0IDAuMTI5NTQ0IDEwLjI0NTQgMC4wODIwNTQyIDEwLjMxODQgMC4wNDgyOTA4QzEwLjM5NDEgMC4wMTU0NjYxIDEwLjQ3NTkgLTAuMDAwOTcyMDU3IDEwLjU1ODMgNC40NDIyOGUtMDVDMTAuNjM1NyAwLjAwMDQ3NTMxOCAxMC43MTIxIDAuMDE3NDc5NSAxMC43ODI0IDAuMDQ5OTI0MkMxMC44NTI3IDAuMDgyMzY4OSAxMC45MTU0IDAuMTI5NTA5IDEwLjk2NjIgMC4xODgyMDVMMTEuODM0NyAxLjAzNzM0QzExLjg4NzMgMS4wOTE0MiAxMS45Mjg4IDEuMTU1MzQgMTEuOTU3IDEuMjI1NUMxMi4wMTQzIDEuMzgxMTYgMTIuMDE0MyAxLjU1MjMxIDExLjk1NyAxLjcwNzk2QzExLjkyODMgMS43Nzc4MSAxMS44ODY4IDEuODQxNjMgMTEuODM0NyAxLjg5NjEzTDQuOTIyOCA4LjgwOTgyQzQuODcxMjkgOC44NzAyMSA0LjgwNzQ3IDguOTE4NzUgNC43MzU2NiA4Ljk1MjE1QzQuNTgyMDIgOS4wMTU5NSA0LjQwOTQ5IDkuMDE1OTUgNC4yNTU4NCA4Ljk1MjE1QzQuMTg0MDQgOC45MTg3NSA0LjEyMDIyIDguODcwMjEgNC4wNjg3MSA4LjgwOTgyTDAuMTY1MzQ0IDQuODk5NDZaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K');background-repeat:no-repeat;background-size:10px 10px;background-position:center center;border-color:transparent;background-color:",a.Jv.icon.brand,";border-radius:",a.E0[4],";",i&&(0,n.iv)("background-color:",a.Jv.icon.disable["default"],";"+(true?"":0),true?"":0),";}",o&&(0,n.iv)("&+span::before{background-image:url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='2' fill='none'%3E%3Crect width='10' height='1.5' y='.25' fill='%23fff' rx='.75'/%3E%3C/svg%3E\");background-repeat:no-repeat;background-size:10px;background-position:center center;background-color:",a.Jv.brand.blue,";border:0.5px solid ",a.Jv.stroke.white,";}"+(true?"":0),true?"":0)," ",i&&(0,n.iv)("&+span{cursor:not-allowed;&::before{border-color:",a.Jv.stroke.disable,";}}"+(true?"":0),true?"":0)," &:focus-visible{&+span{border-radius:",a.E0[2],";outline:2px solid ",a.Jv.stroke.brand,";outline-offset:1px;}}"+(true?"":0),true?"":0)}};const b=f;var h=r(3508);var g=["css"];function y(){y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return y.apply(this,arguments)}function m(e,t){if(e==null)return{};var r=w(e,t);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++){n=i[o];if(t.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(e,n))continue;r[n]=e[n]}}return r}function w(e,t){if(e==null)return{};var r={};var n=Object.keys(e);var o,i;for(i=0;i<n.length;i++){o=n[i];if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}var C=function e(t){var r=t.field,o=t.fieldState,i=t.disabled,a=t.value,u=t.onChange,l=t.label,c=t.description,s=t.isHidden,d=t.labelCss;return(0,n.tZ)(h.Z,{field:r,fieldState:o,isHidden:s},(function(e){var t=e.css,o=m(e,g);return(0,n.tZ)("div",null,(0,n.tZ)(b,y({},r,o,{inputCss:t,labelCss:d,value:a,disabled:i,checked:r.value,label:l,onChange:function e(){r.onChange(!r.value);if(u){u(!r.value)}}})),c&&(0,n.tZ)("p",{css:Z.description},c))}))};const x=C;var Z={description:(0,n.iv)(u.c.small()," color:",a.Jv.text.hints,";padding-left:30px;margin-top:",a.W0[6],";"+(true?"":0),true?"":0)};var O=r(733);var _=r(546);var k=r(8640);var j=r(5885);var S=r(7935);var E=r(1007);var A=r(4428);var M=r(7901);var I=r(6877);function P(e){"@babel/helpers - typeof";return P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},P(e)}var D=["css"];function T(e,t,r){t=W(t);if(t in e){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true})}else{e[t]=r}return e}function W(e){var t=J(e,"string");return P(t)==="symbol"?t:String(t)}function J(e,t){if(P(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==undefined){var n=r.call(e,t||"default");if(P(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function L(){L=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return L.apply(this,arguments)}function N(e,t){if(e==null)return{};var r=F(e,t);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++){n=i[o];if(t.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(e,n))continue;r[n]=e[n]}}return r}function F(e,t){if(e==null)return{};var r={};var n=Object.keys(e);var o,i;for(i=0;i<n.length;i++){o=n[i];if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}function B(e,t){return H(e)||U(e,t)||R(e,t)||z()}function z(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function R(e,t){if(!e)return;if(typeof e==="string")return Q(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor)r=e.constructor.name;if(r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Q(e,t)}function Q(e,t){if(t==null||t>e.length)t=e.length;for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function U(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function H(e){if(Array.isArray(e))return e}function V(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var G=function e(){if(!wp.date){return}var t=wp.date.format;return{formatMonthDropdown:function e(r){return t("F",r)},formatMonthCaption:function e(r){return t("F",r)},formatCaption:function e(r){return t("F",r)},formatWeekdayName:function e(r){return t("D",r)}}};var Y=function e(t){if(!t)return undefined;return(0,O.Z)(new Date(t))?new Date(t.length===10?t+"T00:00:00":t):undefined};var q=function e(t){var r=t.label,o=t.field,i=t.fieldState,a=t.disabled,u=t.disabledBefore,l=t.disabledAfter,s=t.loading,d=t.placeholder,f=t.helpText,p=t.isClearable,v=p===void 0?true:p,b=t.onChange,g=t.dateFormat,y=g===void 0?E.E_.yearMonthDay:g;var m=(0,c.useRef)(null);var w=(0,c.useState)(false),C=B(w,2),x=C[0],Z=C[1];var O=Y(o.value);var M=O?(0,_.Z)(O,y):"";var I=(0,A.l)({isOpen:x,isDropdown:true}),P=I.triggerRef,W=I.position,J=I.popoverRef;var F=function e(){var t;Z(false);(t=m.current)===null||t===void 0?void 0:t.focus()};var z=Y(u);var R=Y(l);return(0,n.tZ)(h.Z,{label:r,field:o,fieldState:i,disabled:a,loading:s,placeholder:d,helpText:f},(function(e){var t,r;var i=e.css,a=N(e,D);return(0,n.tZ)("div",null,(0,n.tZ)("div",{css:K.wrapper,ref:P},(0,n.tZ)("input",L({},a,{css:[i,K.input,true?"":0,true?"":0],ref:function e(t){o.ref(t);m.current=t},type:"text",value:M,onClick:function e(t){t.stopPropagation();Z((function(e){return!e}))},onKeyDown:function e(t){if(t.key==="Enter"){t.preventDefault();Z((function(e){return!e}))}},autoComplete:"off","data-input":true})),(0,n.tZ)(S.Z,{name:"calendarLine",width:30,height:32,style:K.icon}),v&&o.value&&(0,n.tZ)(j.Z,{variant:"text",buttonCss:K.clearButton,onClick:function e(){o.onChange("")}},(0,n.tZ)(S.Z,{name:"times",width:12,height:12}))),(0,n.tZ)(A.h,{isOpen:x,onClickOutside:F,onEscape:F},(0,n.tZ)("div",{css:[K.pickerWrapper,(t={},T(t,E.dZ?"right":"left",W.left),T(t,"top",W.top),t),true?"":0,true?"":0],ref:J},(0,n.tZ)(k._W,{dir:E.dZ?"rtl":"ltr",animate:true,mode:"single",formatters:G(),disabled:[!!z&&{before:z},!!R&&{after:R}],selected:O,onSelect:function e(t){if(t){var r=(0,_.Z)(t,E.E_.yearMonthDay);o.onChange(r);F();if(b){b(r)}}},showOutsideDays:true,captionLayout:"dropdown",autoFocus:true,defaultMonth:O||new Date,startMonth:z||new Date((new Date).getFullYear()-10,0),endMonth:R||new Date((new Date).getFullYear()+10,11),weekStartsOn:(r=wp.date)===null||r===void 0?void 0:r.getSettings().l10n.startOfWeek}))))}))};const $=q;var K={wrapper:true?{name:"1wo2jxd",styles:"position:relative;&:hover,&:focus-within{&>button{opacity:1;}}"}:0,input:(0,n.iv)("&[data-input]{padding-left:",a.W0[40],";}"+(true?"":0),true?"":0),icon:(0,n.iv)("position:absolute;top:50%;left:",a.W0[8],";transform:translateY(-50%);color:",a.Jv.icon["default"],";"+(true?"":0),true?"":0),pickerWrapper:(0,n.iv)(u.c.body("regular"),";position:absolute;background-color:",a.Jv.background.white,";box-shadow:",a.AF.popover,";border-radius:",a.E0[6],";.rdp-root{--rdp-day-height:40px;--rdp-day-width:40px;--rdp-day_button-height:40px;--rdp-day_button-width:40px;--rdp-nav-height:40px;--rdp-today-color:",a.Jv.text.title,";--rdp-caption-font-size:",a.JB[18],";--rdp-accent-color:",a.Jv.action.primary["default"],";--rdp-background-color:",a.Jv.background.hover,";--rdp-accent-color-dark:",a.Jv.action.primary.active,";--rdp-background-color-dark:",a.Jv.action.primary.hover,";--rdp-selected-color:",a.Jv.text.white,";--rdp-day_button-border-radius:",a.E0.circle,";--rdp-outside-opacity:0.5;--rdp-disabled-opacity:0.25;}.rdp-months{margin:",a.W0[16],";}.rdp-month_grid{margin:0px;}.rdp-day{padding:0px;}.rdp-nav{--rdp-accent-color:",a.Jv.text.primary,";button{border-radius:",a.E0.circle,";&:hover,&:focus,&:active{background-color:",a.Jv.background.hover,";color:",a.Jv.text.primary,";}&:focus-visible:not(:disabled){--rdp-accent-color:",a.Jv.text.white,";background-color:",a.Jv.background.brand,";}}}.rdp-dropdown_root{.rdp-caption_label{padding:",a.W0[8],";}}.rdp-today{.rdp-day_button{font-weight:",a.Ue.bold,";}}.rdp-selected{color:var(--rdp-selected-color);background-color:var(--rdp-accent-color);border-radius:",a.E0.circle,";font-weight:",a.Ue.regular,";.rdp-day_button{&:hover,&:focus,&:active{background-color:var(--rdp-accent-color);color:",a.Jv.text.primary,";}&:focus-visible{outline:2px solid var(--rdp-accent-color);outline-offset:2px;}&:not(.rdp-outside){color:var(--rdp-selected-color);}}}.rdp-day_button{&:hover,&:focus,&:active{background-color:var(--rdp-background-color);color:",a.Jv.text.primary,";}&:focus-visible:not([disabled]){color:var(--rdp-selected-color);opacity:1;background-color:var(--rdp-accent-color);}}"+(true?"":0),true?"":0),clearButton:(0,n.iv)("position:absolute;top:50%;right:",a.W0[4],";transform:translateY(-50%);width:32px;height:32px;",M.i.flexCenter(),";opacity:0;transition:background-color 0.3s ease-in-out,opacity 0.3s ease-in-out;border-radius:",a.E0[2],";:hover{background-color:",a.Jv.background.hover,";}"+(true?"":0),true?"":0)};var X=r(4805);var ee=r(5114);var te=r(8015);var re=["className","variant","size","children","type","disabled","roundedFull","loading"];function ne(){ne=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return ne.apply(this,arguments)}function oe(e,t){if(e==null)return{};var r=ie(e,t);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++){n=i[o];if(t.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(e,n))continue;r[n]=e[n]}}return r}function ie(e,t){if(e==null)return{};var r={};var n=Object.keys(e);var o,i;for(i=0;i<n.length;i++){o=n[i];if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}function ae(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var ue=s().forwardRef((function(e,t){var r=e.className,o=e.variant,i=e.size,a=e.children,u=e.type,l=u===void 0?"button":u,c=e.disabled,s=c===void 0?false:c,d=e.roundedFull,f=d===void 0?true:d,p=e.loading,v=oe(e,re);return(0,n.tZ)("button",ne({type:l,ref:t,css:se({variant:o,size:i,rounded:f?"true":"false"}),className:r,disabled:s},v),(0,n.tZ)("span",{css:ce.buttonSpan},p?(0,n.tZ)(te.ZP,{size:24}):a))}));const le=ue;var ce={buttonSpan:(0,n.iv)(M.i.flexCenter(),";z-index:",a.W5.positive,";"+(true?"":0),true?"":0),base:(0,n.iv)(M.i.resetButton,";",u.c.small("medium"),";display:flex;gap:",a.W0[4],";width:100%;justify-content:center;align-items:center;white-space:nowrap;position:relative;overflow:hidden;transition:box-shadow 0.5s ease;&:focus-visible{outline:2px solid ",a.Jv.stroke.brand,";outline-offset:1px;}&:disabled{cursor:not-allowed;background:",a.Jv.action.primary.disable,";pointer-events:none;color:",a.Jv.text.disable,";border-color:",a.Jv.stroke.disable,";}"+(true?"":0),true?"":0),default:function e(t){return(0,n.iv)("background:",!t?a.Jv.ai.gradient_1:a.Jv.ai.gradient_1_rtl,";color:",a.Jv.text.white,";&::before{content:'';position:absolute;inset:0;background:",!t?a.Jv.ai.gradient_2:a.Jv.ai.gradient_2_rtl,";opacity:0;transition:opacity 0.5s ease;}&:hover::before{opacity:1;}"+(true?"":0),true?"":0)},secondary:(0,n.iv)("background-color:",a.Jv.action.secondary["default"],";color:",a.Jv.text.brand,";border-radius:",a.E0[6],";&:hover{background-color:",a.Jv.action.secondary.hover,";}"+(true?"":0),true?"":0),outline:(0,n.iv)("position:relative;&::before{content:'';position:absolute;inset:0;background:",a.Jv.ai.gradient_1,";color:",a.Jv.text.primary,";border:1px solid transparent;-webkit-mask:linear-gradient(#fff 0 0) padding-box,linear-gradient(#fff 0 0);mask:linear-gradient(#fff 0 0) padding-box,linear-gradient(#fff 0 0);-webkit-mask-composite:xor;mask-composite:exclude;}&:hover{&::before{background:",a.Jv.ai.gradient_2,";}}"+(true?"":0),true?"":0),primaryOutline:(0,n.iv)("border:1px solid ",a.Jv.brand.blue,";color:",a.Jv.brand.blue,";&:hover{background-color:",a.Jv.brand.blue,";color:",a.Jv.text.white,";}"+(true?"":0),true?"":0),primary:(0,n.iv)("background-color:",a.Jv.brand.blue,";color:",a.Jv.text.white,";"+(true?"":0),true?"":0),ghost:(0,n.iv)("background-color:transparent;color:",a.Jv.text.subdued,";border-radius:",a.E0[4],";&:hover{color:",a.Jv.text.primary,";}"+(true?"":0),true?"":0),plain:(0,n.iv)("span{background:",!E.dZ?a.Jv.text.ai.gradient:a.Jv.ai.gradient_1_rtl,";background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;&:hover{background:",!E.dZ?a.Jv.ai.gradient_2:a.Jv.ai.gradient_2_rtl,";background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;}}"+(true?"":0),true?"":0),size:{default:(0,n.iv)("height:32px;padding-inline:",a.W0[12],";padding-block:",a.W0[4],";"+(true?"":0),true?"":0),sm:(0,n.iv)("height:24px;padding-inline:",a.W0[10],";"+(true?"":0),true?"":0),icon:true?{name:"68x97p",styles:"width:32px;height:32px"}:0},rounded:{true:(0,n.iv)("border-radius:",a.E0[54],";&::before{border-radius:",a.E0[54],";}"+(true?"":0),true?"":0),false:(0,n.iv)("border-radius:",a.E0[4],";&::before{border-radius:",a.E0[4],";}"+(true?"":0),true?"":0)}};var se=(0,ee.Y)({variants:{variant:{default:ce["default"](E.dZ),primary:ce.primary,secondary:ce.secondary,outline:ce.outline,primary_outline:ce.primaryOutline,ghost:ce.ghost,plain:ce.plain},size:{default:ce.size["default"],sm:ce.size.sm,icon:ce.size.icon},rounded:{true:ce.rounded["true"],false:ce.rounded["false"]}},defaultVariants:{variant:"default",size:"default",rounded:"true"}},ce.base);var de=r(5404);var fe=r(6828);var pe=r(7363);function ve(){ve=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return ve.apply(this,arguments)}function be(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var he=6;var ge=function e(t){var r;var o=t.label,i=t.rows,a=i===void 0?he:i,u=t.columns,l=t.maxLimit,s=t.field,d=t.fieldState,f=t.disabled,p=t.readOnly,v=t.loading,b=t.placeholder,g=t.helpText,y=t.onChange,m=t.onKeyDown,w=t.isHidden,C=t.enableResize,x=C===void 0?true:C,Z=t.isSecondary,O=Z===void 0?false:Z,_=t.isMagicAi,k=_===void 0?false:_,j=t.inputCss,S=t.maxHeight,E=t.autoResize,A=E===void 0?false:E;var M=(r=s.value)!==null&&r!==void 0?r:"";var I=(0,c.useRef)(null);var P=undefined;if(l){P={maxLimit:l,inputCharacter:M.toString().length}}var D=function e(){if(I.current){if(S){I.current.style.maxHeight="".concat(S,"px")}I.current.style.height="auto";I.current.style.height="".concat(I.current.scrollHeight,"px")}};(0,c.useLayoutEffect)((function(){if(A){D()}}),[]);return(0,n.tZ)(h.Z,{label:o,field:s,fieldState:d,disabled:f,readOnly:p,loading:v,placeholder:b,helpText:g,isHidden:w,characterCount:P,isSecondary:O,isMagicAi:k},(function(e){return(0,n.tZ)(pe.Fragment,null,(0,n.tZ)("div",{css:we.container(x,j)},(0,n.tZ)("textarea",ve({},s,e,{ref:function e(t){s.ref(t);I.current=t},style:{maxHeight:S?"".concat(S,"px"):"none"},className:"tutor-input-field",value:M,onChange:function e(t){var r=t.target.value;if(l&&r.trim().length>l){return}s.onChange(r);if(y){y(r)}if(A){D()}},onKeyDown:function e(t){m===null||m===void 0?void 0:m(t.key)},autoComplete:"off",rows:a,cols:u}))))}))};const ye=(0,fe.v)(ge);var me=true?{name:"1dz94pb",styles:"resize:vertical"}:0;var we={container:function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;var r=arguments.length>1?arguments[1]:undefined;return(0,n.iv)("position:relative;display:flex;textarea{",u.c.body(),";height:auto;padding:",a.W0[8]," ",a.W0[12],";resize:none;",t&&me,";&.tutor-input-field{",r,";}}"+(true?"":0),true?"":0)}};var Ce=r(7917);var xe=function e(t){var r=t.options,o=t.onChange;return(0,n.tZ)("div",{css:Ze.wrapper},(0,n.tZ)(Ce.Z,{each:r},(function(e,t){return(0,n.tZ)("button",{type:"button",key:t,onClick:function t(){return o(e.value)},css:Ze.item},e.label)})))};var Ze={wrapper:(0,n.iv)("display:flex;flex-direction:column;padding-block:",a.W0[8],";max-height:400px;overflow-y:auto;"+(true?"":0),true?"":0),item:(0,n.iv)(M.i.resetButton,";",u.c.caption(),";width:100%;padding:",a.W0[4]," ",a.W0[16],";color:",a.Jv.text.subdued,";display:flex;align-items:center;&:hover{background-color:",a.Jv.background.hover,";color:",a.Jv.text.title,";}"+(true?"":0),true?"":0)};var Oe=r(276);function _e(e,t){return Ae(e)||Ee(e,t)||je(e,t)||ke()}function ke(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function je(e,t){if(!e)return;if(typeof e==="string")return Se(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor)r=e.constructor.name;if(r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Se(e,t)}function Se(e,t){if(t==null||t>e.length)t=e.length;for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Ee(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Ae(e){if(Array.isArray(e))return e}var Me=function e(t){var r=t.options,n=t.isOpen,o=t.onSelect,i=t.onClose,a=t.selectedValue;var u=(0,c.useState)(-1),l=_e(u,2),s=l[0],d=l[1];var f=(0,c.useCallback)((function(e){if(!n)return;var t=function e(t,n){var o;var i=t;var a=n==="down"?1:-1;do{i+=a;if(i<0)i=r.length-1;if(i>=r.length)i=0}while(i>=0&&i<r.length&&r[i].disabled);if((o=r[i])!==null&&o!==void 0&&o.disabled){return t}return i};switch(e.key){case"ArrowDown":e.preventDefault();d((function(e){var r=t(e===-1?0:e,"down");return r}));break;case"ArrowUp":e.preventDefault();d((function(e){var r=t(e===-1?0:e,"up");return r}));break;case"Enter":e.preventDefault();e.stopPropagation();if(s>=0&&s<r.length){var a=r[s];if(!a.disabled){i();o(a)}}break;case"Escape":e.preventDefault();e.stopPropagation();i();break;default:break}}),[n,r,s,o,i]);(0,c.useEffect)((function(){if(n){if(s===-1){var e=r.findIndex((function(e){return e.value===a}));var t=e>=0?e:r.findIndex((function(e){return!e.disabled}));d(t)}document.addEventListener("keydown",f,true);return function(){return document.removeEventListener("keydown",f,true)}}}),[n,f,r,a,s]);(0,c.useEffect)((function(){if(!n){d(-1)}}),[n]);var p=(0,c.useCallback)((function(e){var t;if(!((t=r[e])!==null&&t!==void 0&&t.disabled)){d(e)}}),[r]);return{activeIndex:s,setActiveIndex:p}};var Ie=r(4033);function Pe(e){"@babel/helpers - typeof";return Pe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Pe(e)}var De=["css"];function Te(){Te=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return Te.apply(this,arguments)}function We(e,t){if(e==null)return{};var r=Je(e,t);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++){n=i[o];if(t.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(e,n))continue;r[n]=e[n]}}return r}function Je(e,t){if(e==null)return{};var r={};var n=Object.keys(e);var o,i;for(i=0;i<n.length;i++){o=n[i];if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}function Le(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ne(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Le(Object(r),!0).forEach((function(t){Fe(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Le(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Fe(e,t,r){t=Be(t);if(t in e){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true})}else{e[t]=r}return e}function Be(e){var t=ze(e,"string");return Pe(t)==="symbol"?t:String(t)}function ze(e,t){if(Pe(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==undefined){var n=r.call(e,t||"default");if(Pe(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Re(e,t){return Ge(e)||Ve(e,t)||Ue(e,t)||Qe()}function Qe(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Ue(e,t){if(!e)return;if(typeof e==="string")return He(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor)r=e.constructor.name;if(r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return He(e,t)}function He(e,t){if(t==null||t>e.length)t=e.length;for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Ve(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Ge(e){if(Array.isArray(e))return e}function Ye(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var qe=true?{name:"1d3w5wq",styles:"width:100%"}:0;var $e=function e(t){var r;var i=t.options,a=t.field,u=t.fieldState,s=t.onChange,d=s===void 0?l.ZT:s,f=t.label,p=t.placeholder,v=p===void 0?"":p,b=t.disabled,g=t.readOnly,y=t.loading,m=t.isSearchable,w=m===void 0?false:m,C=t.isInlineLabel,x=t.hideCaret,Z=t.listLabel,O=t.isClearable,_=O===void 0?false:O,k=t.helpText,M=t.removeOptionsMinWidth,I=M===void 0?false:M,P=t.leftIcon,D=t.removeBorder,T=t.dataAttribute,W=t.isSecondary,J=W===void 0?false:W,L=t.isMagicAi,N=L===void 0?false:L,F=t.isAiOutline,B=F===void 0?false:F,z=t.selectOnFocus;var R=(0,c.useCallback)((function(){return i.find((function(e){return e.value===a.value}))||{label:"",value:"",description:""}}),[a.value,i]);var Q=(0,c.useMemo)((function(){return i.some((function(e){return(0,Ie.$K)(e.description)}))}),[i]);var U=(0,c.useState)((r=R())===null||r===void 0?void 0:r.label),H=Re(U,2),V=H[0],G=H[1];var Y=(0,c.useState)(false),q=Re(Y,2),$=q[0],K=q[1];var X=(0,c.useState)(""),ee=Re(X,2),te=ee[0],re=ee[1];var ne=(0,c.useState)(false),oe=Re(ne,2),ie=oe[0],ae=oe[1];var ue=(0,c.useRef)(null);var le=(0,c.useRef)(null);var ce=(0,c.useRef)(null);var se=(0,c.useMemo)((function(){if(w){return i.filter((function(e){var t=e.label;return t.toLowerCase().includes(te.toLowerCase())}))}return i}),[te,w,i]);var de=(0,c.useMemo)((function(){return i.find((function(e){return e.value===a.value}))}),[a.value,i]);var fe=(0,A.l)({isOpen:ie,isDropdown:true,dependencies:[se.length]}),pe=fe.triggerRef,ve=fe.triggerWidth,be=fe.position,he=fe.popoverRef;var ge=Ne({},(0,Ie.$K)(T)&&Fe({},T,true));(0,c.useEffect)((function(){var e;G((e=R())===null||e===void 0?void 0:e.label)}),[a.value,R]);(0,c.useEffect)((function(){if(ie){var e;G((e=R())===null||e===void 0?void 0:e.label)}}),[R,ie]);var ye=function e(t,r){r===null||r===void 0?void 0:r.stopPropagation();if(!t.disabled){a.onChange(t.value);d(t);re("");K(false);ae(false)}};var me=Me({options:se,isOpen:ie,selectedValue:a.value,onSelect:ye,onClose:function e(){ae(false);K(false);re("")}}),we=me.activeIndex,Ce=me.setActiveIndex;(0,c.useEffect)((function(){if(ie&&we>=0&&ce.current){ce.current.scrollIntoView({block:"nearest",behavior:"smooth"})}}),[ie,we]);return(0,n.tZ)(h.Z,{fieldState:u,field:a,label:f,disabled:b||i.length===0,readOnly:g,loading:y,isInlineLabel:C,helpText:k,removeBorder:D,isSecondary:J,isMagicAi:N},(function(e){var t,r,l;var c=e.css,s=We(e,De);return(0,n.tZ)("div",{css:rt.mainWrapper},(0,n.tZ)("div",{css:rt.inputWrapper(B),ref:pe},(0,n.tZ)("div",{css:rt.leftIcon},(0,n.tZ)(Oe.Z,{when:P},P),(0,n.tZ)(Oe.Z,{when:de===null||de===void 0?void 0:de.icon},(function(e){return(0,n.tZ)(S.Z,{name:e,width:32,height:32})}))),(0,n.tZ)("div",{css:qe},(0,n.tZ)("input",Te({},s,ge,{ref:function e(t){a.ref(t);ue.current=t},className:"tutor-input-field",css:[c,rt.input({hasLeftIcon:!!P||!!(de!==null&&de!==void 0&&de.icon),hasDescription:Q,hasError:!!u.error,isMagicAi:N,isAiOutline:B}),true?"":0,true?"":0],autoComplete:"off",readOnly:g||!w,placeholder:v,value:$?te:V,title:V,onClick:function e(t){var r;t.stopPropagation();ae((function(e){return!e}));(r=ue.current)===null||r===void 0?void 0:r.focus()},onKeyDown:function e(t){if(t.key==="Enter"){var r;t.preventDefault();ae((function(e){return!e}));(r=ue.current)===null||r===void 0?void 0:r.focus()}if(t.key==="Tab"){ae(false)}},onFocus:z&&w?function(e){e.target.select()}:undefined,onChange:function e(t){G(t.target.value);if(w){K(true);re(t.target.value)}},"data-select":true})),(0,n.tZ)(Oe.Z,{when:Q},(0,n.tZ)("span",{css:rt.description({hasLeftIcon:!!P}),title:(t=R())===null||t===void 0?void 0:t.description},(r=R())===null||r===void 0?void 0:r.description))),!x&&!y&&(0,n.tZ)("button",{tabIndex:-1,type:"button",css:rt.caretButton({isOpen:ie}),onClick:function e(){var t;ae((function(e){return!e}));(t=ue.current)===null||t===void 0?void 0:t.focus()},disabled:b||g||i.length===0},(0,n.tZ)(S.Z,{name:"chevronDown",width:20,height:20}))),(0,n.tZ)(A.h,{isOpen:ie,onClickOutside:function e(){ae(false);K(false);re("")},onEscape:function e(){ae(false);K(false);re("")}},(0,n.tZ)("div",{css:[rt.optionsWrapper,(l={},Fe(l,E.dZ?"right":"left",be.left),Fe(l,"top",be.top),Fe(l,"maxWidth",ve),l),true?"":0,true?"":0],ref:he},(0,n.tZ)("ul",{css:[rt.options(I),true?"":0,true?"":0]},!!Z&&(0,n.tZ)("li",{css:rt.listLabel},Z),(0,n.tZ)(Oe.Z,{when:se.length>0,fallback:(0,n.tZ)("li",{css:rt.emptyState},(0,o.__)("No options available","tutor"))},se.map((function(e,t){return(0,n.tZ)("li",{key:String(e.value),ref:e.value===a.value?le:we===t?ce:null,css:rt.optionItem({isSelected:e.value===a.value,isActive:t===we,isDisabled:!!e.disabled})},(0,n.tZ)("button",{type:"button",css:rt.label,onClick:function t(r){if(!e.disabled){ye(e,r)}},disabled:e.disabled,title:e.label,onMouseOver:function e(){return Ce(t)},onMouseLeave:function e(){return t!==we&&Ce(-1)},onFocus:function e(){return Ce(t)},"aria-selected":we===t},(0,n.tZ)(Oe.Z,{when:e.icon},(0,n.tZ)(S.Z,{name:e.icon,width:32,height:32})),(0,n.tZ)("span",null,e.label)))}))),_&&(0,n.tZ)("div",{css:rt.clearButton({isDisabled:V===""})},(0,n.tZ)(j.Z,{variant:"text",disabled:V==="",icon:(0,n.tZ)(S.Z,{name:"delete"}),onClick:function e(){a.onChange(null);G("");re("");ae(false)}},(0,o.__)("Clear","tutor")))))))}))};const Ke=$e;var Xe=true?{name:"21xn5r",styles:"transform:rotate(180deg)"}:0;var et=true?{name:"16gsvie",styles:"min-width:200px"}:0;var tt=true?{name:"kqjaov",styles:"position:relative;border:none;background:transparent"}:0;var rt={mainWrapper:true?{name:"1d3w5wq",styles:"width:100%"}:0,inputWrapper:function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;return(0,n.iv)("width:100%;display:flex;justify-content:space-between;align-items:center;position:relative;",t&&(0,n.iv)("&::before{content:'';position:absolute;inset:0;background:",a.Jv.ai.gradient_1,";color:",a.Jv.text.primary,";border:1px solid transparent;-webkit-mask:linear-gradient(#fff 0 0) padding-box,linear-gradient(#fff 0 0);-webkit-mask-composite:xor;mask-composite:exclude;border-radius:6px;}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},leftIcon:(0,n.iv)("position:absolute;left:",a.W0[8],";",M.i.display.flex(),";align-items:center;height:100%;color:",a.Jv.icon["default"],";"+(true?"":0),true?"":0),input:function e(t){var r=t.hasLeftIcon,o=t.hasDescription,i=t.hasError,l=i===void 0?false:i,c=t.isMagicAi,s=c===void 0?false:c,d=t.isAiOutline,f=d===void 0?false:d;return(0,n.iv)("&[data-select]{",u.c.body(),";width:100%;cursor:pointer;padding-right:",a.W0[32],";",M.i.textEllipsis,";background-color:transparent;background-color:",a.Jv.background.white,";",r&&(0,n.iv)("padding-left:",a.W0[48],";"+(true?"":0),true?"":0)," ",o&&(0,n.iv)("&.tutor-input-field{height:56px;padding-bottom:",a.W0[24],";}"+(true?"":0),true?"":0)," ",l&&(0,n.iv)("background-color:",a.Jv.background.status.errorFail,";"+(true?"":0),true?"":0)," ",f&&tt," :focus{",M.i.inputFocus,";",s&&(0,n.iv)("outline-color:",a.Jv.stroke.magicAi,";background-color:",a.Jv.background.magicAi[8],";"+(true?"":0),true?"":0)," ",l&&(0,n.iv)("border-color:",a.Jv.stroke.danger,";background-color:",a.Jv.background.status.errorFail,";"+(true?"":0),true?"":0),";}}"+(true?"":0),true?"":0)},description:function e(t){var r=t.hasLeftIcon;return(0,n.iv)(u.c.small(),";",M.i.text.ellipsis(1)," color:",a.Jv.text.hints,";position:absolute;bottom:",a.W0[8],";padding-inline:calc(",a.W0[16]," + 1px) ",a.W0[32],";",r&&(0,n.iv)("padding-left:calc(",a.W0[48]," + 1px);"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},listLabel:(0,n.iv)(u.c.body(),";color:",a.Jv.text.subdued,";min-height:40px;display:flex;align-items:center;padding-left:",a.W0[16],";"+(true?"":0),true?"":0),clearButton:function e(t){var r=t.isDisabled,o=r===void 0?false:r;return(0,n.iv)("padding:",a.W0[4]," ",a.W0[8],";border-top:1px solid ",a.Jv.stroke["default"],";&>button{padding:0;width:100%;font-size:",a.JB[12],";",!o&&(0,n.iv)("color:",a.Jv.text.title,";&:hover{text-decoration:underline;}"+(true?"":0),true?"":0),";>span{justify-content:center;}}"+(true?"":0),true?"":0)},optionsWrapper:true?{name:"1n0kzcr",styles:"position:absolute;width:100%"}:0,options:function e(t){return(0,n.iv)("z-index:",a.W5.dropdown,";background-color:",a.Jv.background.white,";list-style-type:none;box-shadow:",a.AF.popover,";padding:",a.W0[4]," 0;margin:0;max-height:500px;border-radius:",a.E0[6],";",M.i.overflowYAuto,";scrollbar-gutter:auto;",!t&&et,";"+(true?"":0),true?"":0)},optionItem:function e(t){var r=t.isSelected,o=r===void 0?false:r,i=t.isActive,l=i===void 0?false:i,c=t.isDisabled,s=c===void 0?false:c;return(0,n.iv)(u.c.body(),";min-height:36px;height:100%;width:100%;display:flex;align-items:center;transition:background-color 0.3s ease-in-out;cursor:",s?"not-allowed":"pointer",";opacity:",s?.5:1,";",l&&(0,n.iv)("background-color:",a.Jv.background.hover,";"+(true?"":0),true?"":0)," &:hover{background-color:",!s&&a.Jv.background.hover,";}",!s&&o&&(0,n.iv)("background-color:",a.Jv.background.active,";position:relative;&::before{content:'';position:absolute;top:0;left:0;width:3px;height:100%;background-color:",a.Jv.action.primary["default"],";border-radius:0 ",a.E0[6]," ",a.E0[6]," 0;}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},label:(0,n.iv)(M.i.resetButton,";",M.i.text.ellipsis(1),";color:",a.Jv.text.title,";width:100%;height:100%;display:flex;align-items:center;gap:",a.W0[8],";margin:0 ",a.W0[12],";padding:",a.W0[6]," 0;text-align:left;line-height:",a.Nv[24],";word-break:break-all;cursor:pointer;&:hover,&:focus,&:active{background-color:transparent;color:",a.Jv.text.title,";}span{flex-shrink:0;",M.i.text.ellipsis(1)," width:100%;}"+(true?"":0),true?"":0),arrowUpDown:(0,n.iv)("color:",a.Jv.icon["default"],";display:flex;justify-content:center;align-items:center;margin-top:",a.W0[2],";"+(true?"":0),true?"":0),optionsContainer:true?{name:"1ivsou8",styles:"position:absolute;overflow:hidden auto;min-width:16px;max-width:calc(100% - 32px)"}:0,caretButton:function e(t){var r=t.isOpen,o=r===void 0?false:r;return(0,n.iv)(M.i.resetButton,";position:absolute;right:",a.W0[4],";display:flex;align-items:center;transition:transform 0.3s ease-in-out;color:",a.Jv.icon["default"],";border-radius:",a.E0[4],";padding:",a.W0[6],";height:100%;&:focus,&:active,&:hover{background:none;color:",a.Jv.icon["default"],";}&:focus-visible{outline:2px solid ",a.Jv.stroke.brand,";}",o&&Xe,";"+(true?"":0),true?"":0)},emptyState:(0,n.iv)(M.i.flexCenter(),";padding:",a.W0[8],";"+(true?"":0),true?"":0)};var nt=[{label:"English",value:"english"},{label:"简体中文",value:"simplified-chinese"},{label:"繁體中文",value:"traditional-chinese"},{label:"Español",value:"spanish"},{label:"Français",value:"french"},{label:"日本語",value:"japanese"},{label:"Deutsch",value:"german"},{label:"Português",value:"portuguese"},{label:"العربية",value:"arabic"},{label:"Русский",value:"russian"},{label:"Italiano",value:"italian"},{label:"한국어",value:"korean"},{label:"हिन्दी",value:"hindi"},{label:"Nederlands",value:"dutch"},{label:"Polski",value:"polish"},{label:"አማርኛ",value:"amharic"},{label:"Български",value:"bulgarian"},{label:"বাংলা",value:"bengali"},{label:"Čeština",value:"czech"},{label:"Dansk",value:"danish"},{label:"Ελληνικά",value:"greek"},{label:"Eesti",value:"estonian"},{label:"فارسی",value:"persian"},{label:"Filipino",value:"filipino"},{label:"Hrvatski",value:"croatian"},{label:"Magyar",value:"hungarian"},{label:"Bahasa Indonesia",value:"indonesian"},{label:"Lietuvių",value:"lithuanian"},{label:"Latviešu",value:"latvian"},{label:"Melayu",value:"malay"},{label:"Norsk",value:"norwegian"},{label:"Română",value:"romanian"},{label:"Slovenčina",value:"slovak"},{label:"Slovenščina",value:"slovenian"},{label:"Српски",value:"serbian"},{label:"Svenska",value:"swedish"},{label:"ภาษาไทย",value:"thai"},{label:"Türkçe",value:"turkish"},{label:"Українська",value:"ukrainian"},{label:"اردو",value:"urdu"},{label:"Tiếng Việt",value:"vietnamese"}];var ot=[{label:(0,o.__)("Formal","tutor"),value:"formal"},{label:(0,o.__)("Casual","tutor"),value:"casual"},{label:(0,o.__)("Professional","tutor"),value:"professional"},{label:(0,o.__)("Enthusiastic","tutor"),value:"enthusiastic"},{label:(0,o.__)("Informational","tutor"),value:"informational"},{label:(0,o.__)("Funny","tutor"),value:"funny"}];var it=[{label:(0,o.__)("Title","tutor"),value:"title"},{label:(0,o.__)("Essay","tutor"),value:"essay"},{label:(0,o.__)("Paragraph","tutor"),value:"paragraph"},{label:(0,o.__)("Outline","tutor"),value:"outline"}];function at(){at=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return at.apply(this,arguments)}var ut=function e(t){var r=t.form;return(0,n.tZ)("div",{css:lt.wrapper},(0,n.tZ)(X.Qr,{control:r.control,name:"characters",render:function e(t){return(0,n.tZ)(zn,at({},t,{isMagicAi:true,label:(0,o.__)("Character Limit","tutor"),type:"number"}))}}),(0,n.tZ)(X.Qr,{control:r.control,name:"language",render:function e(t){return(0,n.tZ)(Ke,at({},t,{isMagicAi:true,label:(0,o.__)("Language","tutor"),options:nt}))}}),(0,n.tZ)(X.Qr,{control:r.control,name:"tone",render:function e(t){return(0,n.tZ)(Ke,at({},t,{isMagicAi:true,options:ot,label:(0,o.__)("Tone","tutor")}))}}),(0,n.tZ)(X.Qr,{control:r.control,name:"format",render:function e(t){return(0,n.tZ)(Ke,at({},t,{isMagicAi:true,label:(0,o.__)("Format","tutor"),options:it}))}}))};var lt={wrapper:(0,n.iv)("display:grid;grid-template-columns:repeat(2, 1fr);gap:",a.W0[16],";"+(true?"":0),true?"":0)};var ct;function st(e,t){if(!t){t=e.slice(0)}return Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}function dt(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var ft=(0,c.forwardRef)((function(e,t){var r=e.width,o=r===void 0?"100%":r,i=e.height,a=i===void 0?16:i,u=e.animation,l=u===void 0?false:u,c=e.isMagicAi,s=c===void 0?false:c,d=e.isRound,f=d===void 0?false:d,p=e.animationDuration,v=p===void 0?1.6:p,b=e.className;return(0,n.tZ)("span",{ref:t,css:ht.skeleton(o,a,l,s,f,v),className:b})}));const pt=ft;var vt={wave:(0,n.F4)(ct||(ct=st(["\n    0% {\n      transform: translateX(-100%);\n    }\n    50% {\n      transform: translateX(0%);\n    }\n    100% {\n      transform: translateX(100%);\n    }\n  "])))};var bt=true?{name:"1q4m7z3",styles:"background:linear-gradient(89.17deg, #fef4ff 0.2%, #f9d3ff 50.09%, #fef4ff 96.31%)"}:0;var ht={skeleton:function e(t,r,o,i,u,l){return(0,n.iv)("display:block;width:",(0,Ie.hj)(t)?"".concat(t,"px"):t,";height:",(0,Ie.hj)(r)?"".concat(r,"px"):r,";border-radius:",a.E0[6],";background-color:",!i?"rgba(0, 0, 0, 0.11)":a.Jv.background.magicAi.skeleton,";position:relative;-webkit-mask-image:-webkit-radial-gradient(center, white, black);overflow:hidden;",u&&(0,n.iv)("border-radius:",a.E0.circle,";"+(true?"":0),true?"":0)," ",o&&(0,n.iv)(":after{content:'';background:linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.05), transparent);position:absolute;transform:translateX(-100%);inset:0;",i&&bt," animation:",l,"s linear 0.5s infinite normal none running ",vt.wave,";}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}};var gt=function e(){return(0,n.tZ)("div",{css:mt.container},(0,n.tZ)("div",{css:mt.wrapper},(0,n.tZ)(pt,{animation:true,isMagicAi:true,width:"20%",height:"12px"}),(0,n.tZ)(pt,{animation:true,isMagicAi:true,width:"100%",height:"12px"}),(0,n.tZ)(pt,{animation:true,isMagicAi:true,width:"100%",height:"12px"}),(0,n.tZ)(pt,{animation:true,isMagicAi:true,width:"40%",height:"12px"})),(0,n.tZ)("div",{css:mt.wrapper},(0,n.tZ)(pt,{animation:true,isMagicAi:true,width:"80%",height:"12px"}),(0,n.tZ)(pt,{animation:true,isMagicAi:true,width:"100%",height:"12px"}),(0,n.tZ)(pt,{animation:true,isMagicAi:true,width:"80%",height:"12px"})))};const yt=gt;var mt={wrapper:(0,n.iv)("display:flex;flex-direction:column;gap:",a.W0[8],";"+(true?"":0),true?"":0),container:(0,n.iv)("display:flex;flex-direction:column;gap:",a.W0[32],";"+(true?"":0),true?"":0)};var wt=r(8811);function Ct(e){"@babel/helpers - typeof";return Ct="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ct(e)}function xt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Zt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?xt(Object(r),!0).forEach((function(t){Ot(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):xt(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ot(e,t,r){t=_t(t);if(t in e){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true})}else{e[t]=r}return e}function _t(e){var t=kt(e,"string");return Ct(t)==="symbol"?t:String(t)}function kt(e,t){if(Ct(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==undefined){var n=r.call(e,t||"default");if(Ct(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function jt(e,t){return It(e)||Mt(e,t)||Et(e,t)||St()}function St(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Et(e,t){if(!e)return;if(typeof e==="string")return At(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor)r=e.constructor.name;if(r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return At(e,t)}function At(e,t){if(t==null||t>e.length)t=e.length;for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Mt(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function It(e){if(Array.isArray(e))return e}var Pt=function e(t){var r=(0,c.useState)(),n=jt(r,2),o=n[0],i=n[1];var a=(0,X.cI)(t);return Zt(Zt({},a),{},{submitError:o,setSubmitError:i})};var Dt=r(5332);var Tt=r(7855);var Wt=r(7837);var Jt=r(3639);function Lt(e){"@babel/helpers - typeof";return Lt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Lt(e)}function Nt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ft(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Nt(Object(r),!0).forEach((function(t){Bt(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Nt(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Bt(e,t,r){t=zt(t);if(t in e){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true})}else{e[t]=r}return e}function zt(e){var t=Rt(e,"string");return Lt(t)==="symbol"?t:String(t)}function Rt(e,t){if(Lt(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==undefined){var n=r.call(e,t||"default");if(Lt(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Qt=function e(t){return wpAjaxInstance.post(endpoints.GENERATE_AI_IMAGE,t)};var Ut=function e(){return useMutation({mutationFn:Qt})};var Ht=function e(t){return wpAjaxInstance.post(endpoints.MAGIC_FILL_AI_IMAGE,t).then((function(e){return e.data.data[0].b64_json}))};var Vt=function e(){var t=useToast(),r=t.showToast;return useMutation({mutationFn:Ht,onError:function e(t){r({type:"danger",message:convertToErrorMessage(t)})}})};var Gt=function e(t){return Wt.R.post(Jt.Z.MAGIC_TEXT_GENERATION,t)};var Yt=function e(){var t=(0,Tt.p)(),r=t.showToast;return(0,Dt.D)({mutationFn:Gt,onError:function e(t){r({type:"danger",message:(0,l.Mo)(t)})}})};var qt=function e(t){return Wt.R.post(Jt.Z.MAGIC_AI_MODIFY_CONTENT,t)};var $t=function e(){var t=(0,Tt.p)(),r=t.showToast;return(0,Dt.D)({mutationFn:qt,onError:function e(t){r({type:"danger",message:(0,l.Mo)(t)})}})};var Kt=function e(t){return wpAjaxInstance.post(endpoints.USE_AI_GENERATED_IMAGE,t)};var Xt=function e(){var t=useToast(),r=t.showToast;return useMutation({mutationFn:Kt,onError:function e(t){r({type:"danger",message:convertToErrorMessage(t)})}})};var er=function e(t){return wpAjaxInstance.post(endpoints.GENERATE_COURSE_CONTENT,t,{signal:t.signal})};var tr=function e(t){var r=useToast(),n=r.showToast;return useMutation({mutationKey:["GenerateCourseContent",t],mutationFn:er,onError:function e(t){n({type:"danger",message:convertToErrorMessage(t)})}})};var rr=function e(t){return wpAjaxInstance.post(endpoints.GENERATE_COURSE_CONTENT,t,{signal:t.signal})};var nr=function e(){var t=useToast(),r=t.showToast;return useMutation({mutationFn:rr,onError:function e(t){r({type:"danger",message:convertToErrorMessage(t)})}})};var or=function e(t){return wpAjaxInstance.post(endpoints.GENERATE_COURSE_TOPIC_CONTENT,t,{signal:t.signal})};var ir=function e(){var t=useToast(),r=t.showToast;return useMutation({mutationFn:or,onError:function e(t){r({type:"danger",message:convertToErrorMessage(t)})}})};var ar=function e(t){return wpAjaxInstance.post(endpoints.SAVE_AI_GENERATED_COURSE_CONTENT,t)};var ur=function e(){var t=useToast(),r=t.showToast;var n=useQueryClient();return useMutation({mutationFn:ar,onSuccess:function e(){n.invalidateQueries({queryKey:["CourseDetails"]})},onError:function e(t){r({type:"danger",message:convertToErrorMessage(t)})}})};var lr=function e(t){return wpAjaxInstance.post(endpoints.GENERATE_QUIZ_QUESTIONS,t,{signal:t.signal})};var cr=function e(){var t=useToast(),r=t.showToast;return useMutation({mutationFn:lr,onError:function e(t){r({type:"danger",message:convertToErrorMessage(t)})}})};var sr=function e(t){return Wt.R.post(Jt.Z.OPEN_AI_SAVE_SETTINGS,Ft({},t))};var dr=function e(){var t=(0,Tt.p)(),r=t.showToast;return(0,Dt.D)({mutationFn:sr,onSuccess:function e(t){r({type:"success",message:t.message})},onError:function e(t){r({type:"danger",message:(0,l.Mo)(t)})}})};var fr=r(7602);function pr(e){"@babel/helpers - typeof";return pr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},pr(e)}function vr(){vr=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return vr.apply(this,arguments)}function br(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */br=function t(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function e(t,r,n){return t[r]=n}}function c(e,t,r,o){var i=t&&t.prototype instanceof f?t:f,a=Object.create(i.prototype),u=new _(o||[]);return n(a,"_invoke",{value:C(e,r,u)}),a}function s(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var d={};function f(){}function p(){}function v(){}var b={};l(b,i,(function(){return this}));var h=Object.getPrototypeOf,g=h&&h(h(k([])));g&&g!==t&&r.call(g,i)&&(b=g);var y=v.prototype=f.prototype=Object.create(b);function m(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function o(n,i,a,u){var l=s(e[n],e,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==pr(d)&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){o("next",e,a,u)}),(function(e){o("throw",e,a,u)})):t.resolve(d).then((function(e){c.value=e,a(c)}),(function(e){return o("throw",e,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function e(r,n){function a(){return new t((function(e,t){o(r,n,e,t)}))}return i=i?i.then(a,a):a()}})}function C(e,t,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=x(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(e,t,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function x(e,t){var r=t.method,n=e.iterator[r];if(undefined===n)return t.delegate=null,"throw"===r&&e.iterator["return"]&&(t.method="return",t.arg=undefined,x(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,d;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=undefined),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function Z(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function _(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(Z,this),this.reset(!0)}function k(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,o=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=undefined,t.done=!0,t};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(y,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,l(e,u,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},m(w.prototype),l(w.prototype,a,(function(){return this})),e.AsyncIterator=w,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},m(y),l(y,u,"Generator"),l(y,i,(function(){return this})),l(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},e.values=k,_.prototype={constructor:_,reset:function e(t){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function e(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function e(t){if(this.done)throw t;var n=this;function o(e,r){return u.type="throw",u.arg=t,n.next=e,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function e(t,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=t,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function e(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),d},finish:function e(t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function e(t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===t){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function e(t,r,n){return this.delegate={iterator:k(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},e}function hr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function gr(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?hr(Object(r),!0).forEach((function(t){yr(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):hr(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function yr(e,t,r){t=mr(t);if(t in e){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true})}else{e[t]=r}return e}function mr(e){var t=wr(e,"string");return pr(t)==="symbol"?t:String(t)}function wr(e,t){if(pr(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==undefined){var n=r.call(e,t||"default");if(pr(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Cr(e,t,r,n,o,i,a){try{var u=e[i](a);var l=u.value}catch(e){r(e);return}if(u.done){t(l)}else{Promise.resolve(l).then(n,o)}}function xr(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function a(e){Cr(i,n,o,a,u,"next",e)}function u(e){Cr(i,n,o,a,u,"throw",e)}a(undefined)}))}}function Zr(e){return kr(e)||_r(e)||Er(e)||Or()}function Or(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _r(e){if(typeof Symbol!=="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function kr(e){if(Array.isArray(e))return Ar(e)}function jr(e,t){return Ir(e)||Mr(e,t)||Er(e,t)||Sr()}function Sr(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Er(e,t){if(!e)return;if(typeof e==="string")return Ar(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor)r=e.constructor.name;if(r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ar(e,t)}function Ar(e,t){if(t==null||t>e.length)t=e.length;for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Mr(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Ir(e){if(Array.isArray(e))return e}function Pr(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Dr=[(0,o.__)("Mastering Digital Marketing: A Complete Guide","tutor"),(0,o.__)("The Ultimate Photoshop Course for Beginners","tutor"),(0,o.__)("Python Programming: From Zero to Hero","tutor"),(0,o.__)("Creative Writing Essentials: Unlock Your Storytelling Potential","tutor"),(0,o.__)("The Complete Guide to Web Development with React","tutor"),(0,o.__)("Master Public Speaking: Deliver Powerful Presentations","tutor"),(0,o.__)("Excel for Business: From Basics to Advanced Analytics","tutor"),(0,o.__)("Fitness Fundamentals: Build Strength and Confidence","tutor"),(0,o.__)("Photography Made Simple: Capture Stunning Shots","tutor"),(0,o.__)("Financial Freedom: Learn the Basics of Investing","tutor")];var Tr=function e(t){var r=t.title,i=t.icon,u=t.closeModal,s=t.field,d=t.format,f=d===void 0?"essay":d,p=t.characters,v=p===void 0?250:p,b=t.is_html,h=b===void 0?false:b,g=t.fieldLabel,y=g===void 0?"":g,m=t.fieldPlaceholder,w=m===void 0?"":m;var C=Pt({defaultValues:{prompt:"",characters:v,language:"english",tone:"formal",format:f}});var x=Yt();var Z=$t();var O=(0,c.useState)([]),_=jr(O,2),k=_[0],A=_[1];var M=(0,c.useState)(0),I=jr(M,2),P=I[0],D=I[1];var T=(0,c.useState)(false),W=jr(T,2),J=W[0],L=W[1];var N=(0,c.useState)(null),F=jr(N,2),B=F[0],z=F[1];var R=(0,c.useRef)(null);var Q=(0,c.useRef)(null);var U=(0,c.useMemo)((function(){return k[P]}),[k,P]);var H=C.watch("prompt");function V(e){A((function(t){return[e].concat(Zr(t))}));D(0)}function G(e,t){return Y.apply(this,arguments)}function Y(){Y=xr(br().mark((function e(t,r){var n,o,i,a;return br().wrap((function e(u){while(1)switch(u.prev=u.next){case 0:if(!(k.length===0)){u.next=2;break}return u.abrupt("return");case 2:n=k[P];if(!(t==="translation"&&!!r)){u.next=9;break}u.next=6;return Z.mutateAsync({type:"translation",content:n,language:r,is_html:h});case 6:o=u.sent;if(o.data){V(o.data)}return u.abrupt("return");case 9:if(!(t==="change_tone"&&!!r)){u.next=15;break}u.next=12;return Z.mutateAsync({type:"change_tone",content:n,tone:r,is_html:h});case 12:i=u.sent;if(i.data){V(i.data)}return u.abrupt("return");case 15:u.next=17;return Z.mutateAsync({type:t,content:n,is_html:h});case 17:a=u.sent;if(a.data){V(a.data)}case 19:case"end":return u.stop()}}),e)})));return Y.apply(this,arguments)}(0,c.useEffect)((function(){C.setFocus("prompt")}),[]);return(0,n.tZ)(fr.Z,{onClose:u,title:r,icon:i,maxWidth:524},(0,n.tZ)("form",{onSubmit:C.handleSubmit(function(){var e=xr(br().mark((function e(t){var r;return br().wrap((function e(n){while(1)switch(n.prev=n.next){case 0:n.next=2;return x.mutateAsync(gr(gr({},t),{},{is_html:h}));case 2:r=n.sent;if(r.data){V(r.data)}case 4:case"end":return n.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())},(0,n.tZ)("div",{css:Jr.container},(0,n.tZ)("div",{css:Jr.fieldsWrapper},(0,n.tZ)(X.Qr,{control:C.control,name:"prompt",render:function e(t){return(0,n.tZ)(ye,vr({},t,{label:y||(0,o.__)("Craft Your Course Description","tutor"),placeholder:w||(0,o.__)("Provide a brief overview of your course topic, target audience, and key takeaways","tutor"),rows:4,isMagicAi:true}))}}),(0,n.tZ)("button",{type:"button",css:Jr.inspireButton,onClick:function e(){var t=Dr.length;var r=Math.floor(Math.random()*t);C.reset(gr(gr({},C.getValues()),{},{prompt:Dr[r]}))}},(0,n.tZ)(S.Z,{name:"bulbLine"}),(0,o.__)("Inspire Me","tutor"))),(0,n.tZ)(Oe.Z,{when:!x.isPending&&!Z.isPending,fallback:(0,n.tZ)(yt,null)},(0,n.tZ)(Oe.Z,{when:k.length>0,fallback:(0,n.tZ)(ut,{form:C})},(0,n.tZ)("div",null,(0,n.tZ)("div",{css:Jr.actionBar},(0,n.tZ)("div",{css:Jr.navigation},(0,n.tZ)(Oe.Z,{when:k.length>1},(0,n.tZ)(j.Z,{variant:"text",onClick:function e(){return D((function(e){return Math.max(0,e-1)}))},disabled:P===0},(0,n.tZ)(S.Z,{name:!E.dZ?"chevronLeft":"chevronRight",width:20,height:20})),(0,n.tZ)("div",{css:Jr.pageInfo},(0,n.tZ)("span",null,P+1)," / ",k.length),(0,n.tZ)(j.Z,{variant:"text",onClick:function e(){return D((function(e){return Math.min(k.length-1,e+1)}))},disabled:P===k.length-1},(0,n.tZ)(S.Z,{name:!E.dZ?"chevronRight":"chevronLeft",width:20,height:20})))),(0,n.tZ)(j.Z,{variant:"text",onClick:xr(br().mark((function e(){var t;return br().wrap((function e(r){while(1)switch(r.prev=r.next){case 0:if(!(k.length===0)){r.next=2;break}return r.abrupt("return");case 2:t=k[P];r.next=5;return(0,l.vQ)(t);case 5:L(true);setTimeout((function(){L(false)}),1500);case 7:case"end":return r.stop()}}),e)})))},(0,n.tZ)(Oe.Z,{when:J,fallback:(0,n.tZ)(S.Z,{name:"copy",width:20,height:20})},(0,n.tZ)(S.Z,{name:"checkFilled",width:20,height:20,style:(0,n.iv)("color:",a.Jv.text.success,"!important;"+(true?"":0),true?"":0)})))),(0,n.tZ)("div",{css:Jr.content,dangerouslySetInnerHTML:{__html:U}})),(0,n.tZ)("div",{css:Jr.otherActions},(0,n.tZ)(le,{variant:"outline",roundedFull:false,onClick:function e(){return G("rephrase")}},(0,o.__)("Rephrase","tutor")),(0,n.tZ)(le,{variant:"outline",roundedFull:false,onClick:function e(){return G("make_shorter")}},(0,o.__)("Make Shorter","tutor")),(0,n.tZ)(le,{variant:"outline",roundedFull:false,ref:R,onClick:function e(){return z("tone")}},(0,o.__)("Change Tone","tutor"),(0,n.tZ)(S.Z,{name:"chevronDown",width:16,height:16})),(0,n.tZ)(le,{variant:"outline",roundedFull:false,ref:Q,onClick:function e(){return z("translate")}},(0,o.__)("Translate to","tutor"),(0,n.tZ)(S.Z,{name:"chevronDown",width:16,height:16})),(0,n.tZ)(le,{variant:"outline",roundedFull:false,onClick:function e(){return G("write_as_bullets")}},(0,o.__)("Write as Bullets","tutor")),(0,n.tZ)(le,{variant:"outline",roundedFull:false,onClick:function e(){return G("make_longer")}},(0,o.__)("Make Longer","tutor")),(0,n.tZ)(le,{variant:"outline",roundedFull:false,onClick:function e(){return G("simplify_language")}},(0,o.__)("Simplify Language","tutor")))))),(0,n.tZ)(de.Z,{isOpen:B==="tone",triggerRef:R,closePopover:function e(){return z(null)},maxWidth:"160px",animationType:wt.ru.slideUp},(0,n.tZ)(xe,{options:ot,onChange:function(){var e=xr(br().mark((function e(t){return br().wrap((function e(r){while(1)switch(r.prev=r.next){case 0:z(null);r.next=3;return G("change_tone",t);case 3:case"end":return r.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()})),(0,n.tZ)(de.Z,{isOpen:B==="translate",triggerRef:Q,closePopover:function e(){return z(null)},maxWidth:"160px",animationType:wt.ru.slideUp},(0,n.tZ)(xe,{options:nt,onChange:function(){var e=xr(br().mark((function e(t){return br().wrap((function e(r){while(1)switch(r.prev=r.next){case 0:z(null);r.next=3;return G("translation",t);case 3:case"end":return r.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()})),(0,n.tZ)("div",{css:Jr.footer},(0,n.tZ)(Oe.Z,{when:k.length>0,fallback:(0,n.tZ)(le,{type:"submit",disabled:x.isPending||!H||Z.isPending},(0,n.tZ)(S.Z,{name:"magicWand",width:24,height:24}),(0,o.__)("Generate Now","tutor"))},(0,n.tZ)(le,{variant:"outline",type:"submit",disabled:x.isPending||!H||Z.isPending},(0,o.__)("Generate Again","tutor")),(0,n.tZ)(le,{variant:"primary",disabled:x.isPending||k.length===0||Z.isPending,onClick:function e(){s.onChange(k[P]);u()}},(0,o.__)("Use This","tutor"))))))};const Wr=Tr;var Jr={container:(0,n.iv)("padding:",a.W0[20],";display:flex;flex-direction:column;gap:",a.W0[16],";"+(true?"":0),true?"":0),fieldsWrapper:(0,n.iv)("position:relative;textarea{padding-bottom:",a.W0[40],"!important;}"+(true?"":0),true?"":0),footer:(0,n.iv)("padding:",a.W0[12]," ",a.W0[16],";display:flex;align-items:center;justify-content:end;gap:",a.W0[10],";box-shadow:0px 1px 0px 0px #e4e5e7 inset;button{width:fit-content;}"+(true?"":0),true?"":0),pageInfo:(0,n.iv)(u.c.caption(),";color:",a.Jv.text.hints,";&>span{font-weight:",a.Ue.medium,";color:",a.Jv.text.primary,";}"+(true?"":0),true?"":0),inspireButton:(0,n.iv)(M.i.resetButton,";",u.c.small(),";position:absolute;height:28px;bottom:",a.W0[12],";left:",a.W0[12],";border:1px solid ",a.Jv.stroke.brand,";border-radius:",a.E0[4],";display:flex;align-items:center;gap:",a.W0[4],";color:",a.Jv.text.brand,";padding-inline:",a.W0[12],";background-color:",a.Jv.background.white,";&:hover{background-color:",a.Jv.background.brand,";color:",a.Jv.text.white,";}&:focus-visible{outline:2px solid ",a.Jv.stroke.brand,";outline-offset:1px;}&:disabled{background-color:",a.Jv.background.disable,";color:",a.Jv.text.disable,";}"+(true?"":0),true?"":0),navigation:(0,n.iv)("margin-left:-",a.W0[8],";display:flex;align-items:center;"+(true?"":0),true?"":0),content:(0,n.iv)(u.c.caption(),";height:180px;overflow-y:auto;background-color:",a.Jv.background.magicAi["default"],";border-radius:",a.E0[6],";padding:",a.W0[6]," ",a.W0[12],";color:",a.Jv.text.magicAi,";"+(true?"":0),true?"":0),actionBar:true?{name:"bcffy2",styles:"display:flex;align-items:center;justify-content:space-between"}:0,otherActions:(0,n.iv)("display:flex;gap:",a.W0[10],";flex-wrap:wrap;&>button{width:fit-content;}"+(true?"":0),true?"":0)};var Lr=r(7145);var Nr=r(2749);var Fr=r(7363);var Br={title:(0,n.tZ)(Fr.Fragment,null,(0,o.__)("Upgrade to Tutor LMS Pro today and experience the power of ","tutor"),(0,n.tZ)("span",{css:M.i.aiGradientText},(0,o.__)("AI Studio","tutor"))),message:(0,o.__)("Upgrade your plan to access the AI feature","tutor"),featuresTitle:(0,o.__)("Don’t miss out on this game-changing feature!","tutor"),features:[(0,o.__)("Generate a complete course outline in seconds!","tutor"),(0,o.__)("Let the AI Studio create Quizzes on your behalf and give your brain a well-deserved break.","tutor"),(0,o.__)("Generate images, customize backgrounds, and even remove unwanted objects with ease.","tutor"),(0,o.__)("Say goodbye to typos and grammar errors with AI-powered copy editing.","tutor")],footer:(0,n.tZ)(j.Z,{onClick:function e(){return window.open(Nr.Z.TUTOR_PRICING_PAGE,"_blank","noopener")},icon:(0,n.tZ)(S.Z,{name:"crown",width:24,height:24})},(0,o.__)("Get Tutor LMS Pro","tutor"))};var zr=function e(t){var r=t.title,i=r===void 0?Br.title:r,a=t.message,u=a===void 0?Br.message:a,l=t.featuresTitle,c=l===void 0?Br.featuresTitle:l,s=t.features,d=s===void 0?Br.features:s,f=t.closeModal,p=t.image,v=t.image2x,b=t.footer,h=b===void 0?Br.footer:b;return(0,n.tZ)(fr.Z,{onClose:f,entireHeader:(0,n.tZ)("span",{css:Qr.message},u),maxWidth:496},(0,n.tZ)("div",{css:Qr.wrapper},(0,n.tZ)(Oe.Z,{when:i},(0,n.tZ)("h4",{css:Qr.title},i)),(0,n.tZ)(Oe.Z,{when:p},(0,n.tZ)("img",{css:Qr.image,src:p,alt:typeof i==="string"?i:(0,o.__)("Illustration"),srcSet:v?"".concat(p," ").concat(v," 2x"):undefined})),(0,n.tZ)(Oe.Z,{when:c},(0,n.tZ)("h6",{css:Qr.featuresTiTle},c)),(0,n.tZ)(Oe.Z,{when:d.length},(0,n.tZ)("div",{css:Qr.features},(0,n.tZ)(Ce.Z,{each:d},(function(e,t){return(0,n.tZ)("div",{key:t,css:Qr.feature},(0,n.tZ)(S.Z,{name:"materialCheck",width:20,height:20,style:Qr.checkIcon}),(0,n.tZ)("span",null,e))})))),(0,n.tZ)(Oe.Z,{when:h},h)))};const Rr=zr;var Qr={wrapper:(0,n.iv)("padding:0 ",a.W0[24]," ",a.W0[32]," ",a.W0[24],";",M.i.display.flex("column"),";gap:",a.W0[16],";"+(true?"":0),true?"":0),message:(0,n.iv)(u.c.small(),";color:",a.Jv.text.subdued,";padding-left:",a.W0[8],";padding-top:",a.W0[24],";padding-bottom:",a.W0[4],";"+(true?"":0),true?"":0),title:(0,n.iv)(u.c.heading6("medium"),";color:",a.Jv.text.primary,";text-wrap:pretty;"+(true?"":0),true?"":0),image:(0,n.iv)("height:270px;width:100%;object-fit:cover;object-position:center;border-radius:",a.E0[8],";"+(true?"":0),true?"":0),featuresTiTle:(0,n.iv)(u.c.body("medium"),";color:",a.Jv.text.primary,";text-wrap:pretty;"+(true?"":0),true?"":0),features:(0,n.iv)(M.i.display.flex("column"),";gap:",a.W0[4],";padding-right:",a.W0[48],";"+(true?"":0),true?"":0),feature:(0,n.iv)(M.i.display.flex(),";gap:",a.W0[12],";",u.c.small(),";color:",a.Jv.text.title,";span{text-wrap:pretty;}"+(true?"":0),true?"":0),checkIcon:(0,n.iv)("flex-shrink:0;color:",a.Jv.text.success,";"+(true?"":0),true?"":0)};var Ur={text:{warning:"#D47E00",success:"#D47E00",danger:"#f44337",info:"#D47E00",primary:"#D47E00"},icon:{warning:"#FAB000",success:"#FAB000",danger:"#f55e53",info:"#FAB000",primary:"#FAB000"},background:{warning:"#FBFAE9",success:"#FBFAE9",danger:"#fdd9d7",info:"#FBFAE9",primary:"#FBFAE9"}};var Hr=function e(t){var r=t.children,o=t.type,i=o===void 0?"warning":o,a=t.icon;return(0,n.tZ)("div",{css:Gr.wrapper({type:i})},(0,n.tZ)(Oe.Z,{when:a},(function(e){return(0,n.tZ)(S.Z,{style:Gr.icon({type:i}),name:e,height:24,width:24})})),(0,n.tZ)("span",null,r))};const Vr=Hr;var Gr={wrapper:function e(t){var r=t.type;return(0,n.iv)(u.c.caption(),";display:flex;align-items:start;padding:",a.W0[8]," ",a.W0[12],";width:100%;border-radius:",a.E0.card,";gap:",a.W0[4],";background-color:",Ur.background[r],";color:",Ur.text[r],";"+(true?"":0),true?"":0)},icon:function e(t){var r=t.type;return(0,n.iv)("color:",Ur.icon[r],";flex-shrink:0;"+(true?"":0),true?"":0)}};var Yr=r(8027);function qr(e,t){return tn(e)||en(e,t)||Kr(e,t)||$r()}function $r(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Kr(e,t){if(!e)return;if(typeof e==="string")return Xr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor)r=e.constructor.name;if(r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Xr(e,t)}function Xr(e,t){if(t==null||t>e.length)t=e.length;for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function en(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function tn(e){if(Array.isArray(e))return e}var rn=function e(){return{required:{value:true,message:(0,o.__)("This field is required","tutor")}}};var nn=function e(t){var r=t.maxValue,n=t.message;return{maxLength:{value:r,message:n||__("Max. value should be ".concat(r))}}};var on=function e(){return{validate:function e(t){if((t===null||t===void 0?void 0:t.amount)===undefined){return __("The field is required","tutor")}return undefined}}};var an=function e(t){if(!isValid(new Date(t||""))){return __("Invalid date entered!","tutor")}return undefined};var un=function e(t){return{validate:function e(r){if(r&&t<r.length){return __("Maximum ".concat(t," character supported"),"tutor")}return undefined}}};var ln=function e(t){if(!t){return undefined}var r=__("Invalid time entered!","tutor");var n=t.split(":"),o=qr(n,2),i=o[0],a=o[1];if(!i||!a){return r}var u=a.split(" "),l=qr(u,2),c=l[0],s=l[1];if(!c||!s){return r}if(i.length!==2||c.length!==2){return r}if(Number(i)<1||Number(i)>12){return r}if(Number(c)<0||Number(c)>59){return r}if(!["am","pm"].includes(s.toLowerCase())){return r}return undefined};var cn=r(7363);function sn(e){"@babel/helpers - typeof";return sn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},sn(e)}var dn,fn;function pn(){pn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return pn.apply(this,arguments)}function vn(){"use strict";/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */vn=function t(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},i=o.iterator||"@@iterator",a=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function l(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{l({},"")}catch(e){l=function e(t,r,n){return t[r]=n}}function c(e,t,r,o){var i=t&&t.prototype instanceof f?t:f,a=Object.create(i.prototype),u=new _(o||[]);return n(a,"_invoke",{value:C(e,r,u)}),a}function s(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=c;var d={};function f(){}function p(){}function v(){}var b={};l(b,i,(function(){return this}));var h=Object.getPrototypeOf,g=h&&h(h(k([])));g&&g!==t&&r.call(g,i)&&(b=g);var y=v.prototype=f.prototype=Object.create(b);function m(e){["next","throw","return"].forEach((function(t){l(e,t,(function(e){return this._invoke(t,e)}))}))}function w(e,t){function o(n,i,a,u){var l=s(e[n],e,i);if("throw"!==l.type){var c=l.arg,d=c.value;return d&&"object"==sn(d)&&r.call(d,"__await")?t.resolve(d.__await).then((function(e){o("next",e,a,u)}),(function(e){o("throw",e,a,u)})):t.resolve(d).then((function(e){c.value=e,a(c)}),(function(e){return o("throw",e,a,u)}))}u(l.arg)}var i;n(this,"_invoke",{value:function e(r,n){function a(){return new t((function(e,t){o(r,n,e,t)}))}return i=i?i.then(a,a):a()}})}function C(e,t,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return j()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var u=x(a,r);if(u){if(u===d)continue;return u}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var l=s(e,t,r);if("normal"===l.type){if(n=r.done?"completed":"suspendedYield",l.arg===d)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(n="completed",r.method="throw",r.arg=l.arg)}}}function x(e,t){var r=t.method,n=e.iterator[r];if(undefined===n)return t.delegate=null,"throw"===r&&e.iterator["return"]&&(t.method="return",t.arg=undefined,x(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),d;var o=s(n,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,d;var i=o.arg;return i?i.done?(t[e.resultName]=i.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=undefined),t.delegate=null,d):i:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,d)}function Z(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function O(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function _(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(Z,this),this.reset(!0)}function k(e){if(e){var t=e[i];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,o=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=undefined,t.done=!0,t};return o.next=o}}return{next:j}}function j(){return{value:undefined,done:!0}}return p.prototype=v,n(y,"constructor",{value:v,configurable:!0}),n(v,"constructor",{value:p,configurable:!0}),p.displayName=l(v,u,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===p||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,v):(e.__proto__=v,l(e,u,"GeneratorFunction")),e.prototype=Object.create(y),e},e.awrap=function(e){return{__await:e}},m(w.prototype),l(w.prototype,a,(function(){return this})),e.AsyncIterator=w,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new w(c(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},m(y),l(y,u,"Generator"),l(y,i,(function(){return this})),l(y,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},e.values=k,_.prototype={constructor:_,reset:function e(t){if(this.prev=0,this.next=0,this.sent=this._sent=undefined,this.done=!1,this.delegate=null,this.method="next",this.arg=undefined,this.tryEntries.forEach(O),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=undefined)},stop:function e(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function e(t){if(this.done)throw t;var n=this;function o(e,r){return u.type="throw",u.arg=t,n.next=e,r&&(n.method="next",n.arg=undefined),!!r}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function e(t,n){for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o];if(i.tryLoc<=this.prev&&r.call(i,"finallyLoc")&&this.prev<i.finallyLoc){var a=i;break}}a&&("break"===t||"continue"===t)&&a.tryLoc<=n&&n<=a.finallyLoc&&(a=null);var u=a?a.completion:{};return u.type=t,u.arg=n,a?(this.method="next",this.next=a.finallyLoc,d):this.complete(u)},complete:function e(t,r){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&r&&(this.next=r),d},finish:function e(t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),O(n),d}},catch:function e(t){for(var r=this.tryEntries.length-1;r>=0;--r){var n=this.tryEntries[r];if(n.tryLoc===t){var o=n.completion;if("throw"===o.type){var i=o.arg;O(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function e(t,r,n){return this.delegate={iterator:k(t),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=undefined),d}},e}function bn(e,t,r,n,o,i,a){try{var u=e[i](a);var l=u.value}catch(e){r(e);return}if(u.done){t(l)}else{Promise.resolve(l).then(n,o)}}function hn(e){return function(){var t=this,r=arguments;return new Promise((function(n,o){var i=e.apply(t,r);function a(e){bn(i,n,o,a,u,"next",e)}function u(e){bn(i,n,o,a,u,"throw",e)}a(undefined)}))}}var gn=((dn=Nr.y.settings)===null||dn===void 0?void 0:dn.chatgpt_enable)==="on";var yn=(fn=Nr.y.current_user.roles)===null||fn===void 0?void 0:fn.includes(E.er.ADMINISTRATOR);var mn=function e(t){var r=t.closeModal,i=t.image,a=t.image2x;var u=Pt({defaultValues:{openAIApiKey:"",enable_open_ai:gn},shouldFocusError:true});var l=dr();var s=function(){var e=hn(vn().mark((function e(t){var n;return vn().wrap((function e(o){while(1)switch(o.prev=o.next){case 0:o.next=2;return l.mutateAsync({chatgpt_api_key:t.openAIApiKey,chatgpt_enable:t.enable_open_ai?1:0});case 2:n=o.sent;if(n.status_code===200){r({action:"CONFIRM"});window.location.reload()}case 4:case"end":return o.stop()}}),e)})));return function t(r){return e.apply(this,arguments)}}();(0,c.useEffect)((function(){u.setFocus("openAIApiKey")}),[]);return(0,n.tZ)(fr.Z,{onClose:function e(){return r({action:"CLOSE"})},title:yn?(0,o.__)("Set OpenAI API key","tutor"):undefined,entireHeader:yn?undefined:(0,n.tZ)(cn.Fragment,null," "),maxWidth:560},(0,n.tZ)("div",{css:Cn.wrapper({isCurrentUserAdmin:yn})},(0,n.tZ)(Oe.Z,{when:yn,fallback:(0,n.tZ)(cn.Fragment,null,(0,n.tZ)("img",{css:Cn.image,src:i,srcSet:a?"".concat(i," 1x, ").concat(a," 2x"):"".concat(i," 1x"),alt:(0,o.__)("Connect API KEY","tutor")}),(0,n.tZ)("div",null,(0,n.tZ)("div",{css:Cn.message},(0,o.__)("API is not connected","tutor")),(0,n.tZ)("div",{css:Cn.title},(0,o.__)("Please, ask your Admin to connect the API with Tutor LMS Pro.","tutor"))))},(0,n.tZ)("form",{css:Cn.formWrapper,onSubmit:u.handleSubmit(s)},(0,n.tZ)("div",{css:Cn.infoText},(0,n.tZ)("div",null,(0,o.__)("Find your Secret API key in your ","tutor"),(0,n.tZ)("a",{href:Nr.Z.CHATGPT_PLATFORM_URL},(0,o.__)("OpenAI User settings","tutor")),(0,o.__)(" and paste it here to connect OpenAI with your Tutor LMS website.","tutor")),(0,n.tZ)(Vr,{type:"info",icon:"warning"},(0,o.__)("This page will reload after submit. Please save course information.","tutor"))),(0,n.tZ)(X.Qr,{name:"openAIApiKey",control:u.control,rules:rn(),render:function e(t){return(0,n.tZ)(zn,pn({},t,{type:"password",isPassword:true,label:(0,o.__)("OpenAI API key","tutor"),placeholder:(0,o.__)("Enter your OpenAI API key","tutor")}))}}),(0,n.tZ)(X.Qr,{name:"enable_open_ai",control:u.control,render:function e(t){return(0,n.tZ)(Yr.Z,pn({},t,{label:(0,o.__)("Enable OpenAI","tutor")}))}})),(0,n.tZ)("div",{css:Cn.formFooter},(0,n.tZ)(j.Z,{onClick:function e(){return r({action:"CLOSE"})},variant:"text",size:"small"},(0,o.__)("Cancel","tutor")),(0,n.tZ)(j.Z,{size:"small",onClick:u.handleSubmit(s),loading:l.isPending},(0,o.__)("Save","tutor"))))))};const wn=mn;var Cn={wrapper:function e(t){var r=t.isCurrentUserAdmin;return(0,n.iv)(M.i.display.flex("column"),";gap:",a.W0[20],";",!r&&(0,n.iv)("padding:",a.W0[24],";padding-top:",a.W0[6],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},formWrapper:(0,n.iv)(M.i.display.flex("column"),";gap:",a.W0[20],";padding:",a.W0[16]," ",a.W0[16]," 0 ",a.W0[16],";"+(true?"":0),true?"":0),infoText:(0,n.iv)(u.c.small(),";",M.i.display.flex("column"),";gap:",a.W0[8],";color:",a.Jv.text.subdued,";a{",M.i.resetButton," color:",a.Jv.text.brand,";}"+(true?"":0),true?"":0),formFooter:(0,n.iv)(M.i.display.flex(),";justify-content:flex-end;gap:",a.W0[16],";border-top:1px solid ",a.Jv.stroke.divider,";padding:",a.W0[16],";"+(true?"":0),true?"":0),image:(0,n.iv)("height:310px;width:100%;object-fit:cover;object-position:center;border-radius:",a.E0[8],";"+(true?"":0),true?"":0),message:(0,n.iv)(u.c.small(),";color:",a.Jv.text.subdued,";"+(true?"":0),true?"":0),title:(0,n.iv)(u.c.heading4("medium"),";color:",a.Jv.text.primary,";margin-top:",a.W0[4],";text-wrap:pretty;"+(true?"":0),true?"":0)};const xn=r.p+"images/6d34e8c6da0e2b4bfbd21a38bf7bbaf0-generate-text-2x.webp";const Zn=r.p+"images/1cc4846c27ec533c869242e997e1c783-generate-text.webp";var On=r(7363);function _n(e){"@babel/helpers - typeof";return _n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_n(e)}var kn;function jn(){jn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return jn.apply(this,arguments)}function Sn(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function En(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Sn(Object(r),!0).forEach((function(t){An(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Sn(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function An(e,t,r){t=Mn(t);if(t in e){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true})}else{e[t]=r}return e}function Mn(e){var t=In(e,"string");return _n(t)==="symbol"?t:String(t)}function In(e,t){if(_n(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==undefined){var n=r.call(e,t||"default");if(_n(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Pn(e,t){return Ln(e)||Jn(e,t)||Tn(e,t)||Dn()}function Dn(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Tn(e,t){if(!e)return;if(typeof e==="string")return Wn(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor)r=e.constructor.name;if(r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Wn(e,t)}function Wn(e,t){if(t==null||t>e.length)t=e.length;for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Jn(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Ln(e){if(Array.isArray(e))return e}var Nn=!!Nr.y.tutor_pro_url;var Fn=(kn=Nr.y.settings)===null||kn===void 0?void 0:kn.chatgpt_key_exist;var Bn=function e(t){var r;var i=t.label,a=t.type,u=a===void 0?"text":a,s=t.maxLimit,d=t.field,f=t.fieldState,p=t.disabled,v=t.readOnly,b=t.loading,g=t.placeholder,y=t.helpText,m=t.onChange,w=t.onKeyDown,C=t.isHidden,x=t.isClearable,Z=x===void 0?false:x,O=t.isSecondary,_=O===void 0?false:O,k=t.removeBorder,E=t.dataAttribute,A=t.isInlineLabel,M=A===void 0?false:A,I=t.isPassword,P=I===void 0?false:I,D=t.style,T=t.selectOnFocus,W=T===void 0?false:T,J=t.autoFocus,L=J===void 0?false:J,N=t.generateWithAi,F=N===void 0?false:N,B=t.isMagicAi,z=B===void 0?false:B,R=t.allowNegative,Q=R===void 0?false:R,U=t.onClickAiButton;var H=(0,c.useState)(u),V=Pn(H,2),G=V[0],Y=V[1];var q=(0,Lr.d)(),$=q.showModal;var K=(0,c.useRef)(null);var X=(r=d.value)!==null&&r!==void 0?r:"";var ee=undefined;if(G==="number"){X=(0,l.jv)("".concat(X),Q).replace(/(\..*)\./g,"$1")}if(s){ee={maxLimit:s,inputCharacter:X.toString().length}}var te=En({},(0,Ie.$K)(E)&&An({},E,true));var re=function e(){if(!Nn){$({component:Rr,props:{image:Zn,image2x:xn}})}else if(!Fn){$({component:wn,props:{image:Zn,image2x:xn}})}else{$({component:Wr,isMagicAi:true,props:{title:(0,o.__)("AI Studio","tutor"),icon:(0,n.tZ)(S.Z,{name:"magicAiColorize",width:24,height:24}),characters:120,field:d,fieldState:f,format:"title",is_html:false,fieldLabel:(0,o.__)("Create a Compelling Title","tutor"),fieldPlaceholder:(0,o.__)("Describe the main focus of your course in a few words","tutor")}});U===null||U===void 0?void 0:U()}};return(0,n.tZ)(h.Z,{label:i,field:d,fieldState:f,disabled:p,readOnly:v,loading:b,placeholder:g,helpText:y,isHidden:C,characterCount:ee,isSecondary:_,removeBorder:k,isInlineLabel:M,inputStyle:D,generateWithAi:F,onClickAiButton:re,isMagicAi:z},(function(e){return(0,n.tZ)(On.Fragment,null,(0,n.tZ)("div",{css:Rn.container(Z||P)},(0,n.tZ)("input",jn({},d,e,te,{type:G==="number"?"text":G,value:X,autoFocus:L,onChange:function e(t){var r=t.target.value;var n=G==="number"?(0,l.jv)(r):r;d.onChange(n);if(m){m(n)}},onClick:function e(t){t.stopPropagation()},onKeyDown:function e(t){t.stopPropagation();w===null||w===void 0?void 0:w(t.key)},autoComplete:"off",ref:function e(t){d.ref(t);K.current=t},onFocus:function e(){if(!W||!K.current){return}K.current.select()}})),(0,n.tZ)(Oe.Z,{when:P},(0,n.tZ)("div",{css:Rn.eyeButtonWrapper},(0,n.tZ)("button",{type:"button",css:Rn.eyeButton({type:G}),onClick:function e(){return Y((function(e){return e==="password"?"text":"password"}))}},(0,n.tZ)(S.Z,{name:"eye",height:24,width:24})))),(0,n.tZ)(Oe.Z,{when:Z&&!!d.value&&G!=="password"},(0,n.tZ)("div",{css:Rn.clearButton},(0,n.tZ)(j.Z,{variant:"text",onClick:function e(){return d.onChange("")}},(0,n.tZ)(S.Z,{name:"timesAlt"}))))))}))};const zn=(0,fe.v)(Bn);var Rn={container:function e(t){return(0,n.iv)("position:relative;display:flex;input{&.tutor-input-field{",t&&"padding-right: ".concat(a.W0[36],";"),";}}"+(true?"":0),true?"":0)},clearButton:(0,n.iv)("position:absolute;right:",a.W0[4],";top:",a.W0[4],";width:32px;height:32px;background:transparent;button{padding:",a.W0[8],";border-radius:",a.E0[2],";}"+(true?"":0),true?"":0),eyeButtonWrapper:(0,n.iv)("position:absolute;display:flex;right:",a.W0[4],";top:50%;transform:translateY(-50%);"+(true?"":0),true?"":0),eyeButton:function e(t){var r=t.type;return(0,n.iv)(M.i.resetButton," ",M.i.flexCenter()," color:",a.Jv.icon["default"],";padding:",a.W0[4],";border-radius:",a.E0[2],";background:transparent;",r!=="password"&&(0,n.iv)("color:",a.Jv.icon.brand,";"+(true?"":0),true?"":0)," &:focus,&:active,&:hover{background:none;color:",a.Jv.icon["default"],";}:focus-visible{outline:2px solid ",a.Jv.stroke.brand,";outline-offset:2px;}"+(true?"":0),true?"":0)}};var Qn=["css"];function Un(){Un=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return Un.apply(this,arguments)}function Hn(e,t){if(e==null)return{};var r=Vn(e,t);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++){n=i[o];if(t.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(e,n))continue;r[n]=e[n]}}return r}function Vn(e,t){if(e==null)return{};var r={};var n=Object.keys(e);var o,i;for(i=0;i<n.length;i++){o=n[i];if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}var Gn=function e(t){var r=t.label,o=t.content,i=t.contentPosition,a=i===void 0?"left":i,u=t.showVerticalBar,l=u===void 0?true:u,s=t.size,d=s===void 0?"regular":s,f=t.type,p=f===void 0?"text":f,v=t.field,b=t.fieldState,g=t.disabled,y=t.readOnly,m=t.loading,w=t.placeholder,C=t.helpText,x=t.onChange,Z=t.onKeyDown,O=t.isHidden,_=t.wrapperCss,k=t.contentCss,j=t.removeBorder,S=j===void 0?false:j,E=t.selectOnFocus,A=E===void 0?false:E;var M=(0,c.useRef)(null);return(0,n.tZ)(h.Z,{label:r,field:v,fieldState:b,disabled:g,readOnly:y,loading:m,placeholder:w,helpText:C,isHidden:O,removeBorder:S},(function(e){var t;var r=e.css,i=Hn(e,Qn);return(0,n.tZ)("div",{css:[qn.inputWrapper(!!b.error,S),_,true?"":0,true?"":0]},a==="left"&&(0,n.tZ)("div",{css:[qn.inputLeftContent(l,d),k,true?"":0,true?"":0]},o),(0,n.tZ)("input",Un({},v,i,{type:"text",value:(t=v.value)!==null&&t!==void 0?t:"",onChange:function e(t){var r=p==="number"?t.target.value.replace(/[^0-9.]/g,"").replace(/(\..*)\./g,"$1"):t.target.value;v.onChange(r);if(x){x(r)}},onKeyDown:function e(t){return Z===null||Z===void 0?void 0:Z(t.key)},css:[r,qn.input(a,l,d),true?"":0,true?"":0],autoComplete:"off",ref:function e(t){v.ref(t);M.current=t},onFocus:function e(){if(!A||!M.current){return}M.current.select()},"data-input":true})),a==="right"&&(0,n.tZ)("div",{css:[qn.inputRightContent(l,d),k,true?"":0,true?"":0]},o))}))};const Yn=(0,fe.v)(Gn);var qn={inputWrapper:function e(t,r){return(0,n.iv)("display:flex;align-items:center;",!r&&(0,n.iv)("border:1px solid ",a.Jv.stroke["default"],";border-radius:",a.E0[6],";box-shadow:",a.AF.input,";background-color:",a.Jv.background.white,";"+(true?"":0),true?"":0)," ",t&&(0,n.iv)("border-color:",a.Jv.stroke.danger,";background-color:",a.Jv.background.status.errorFail,";"+(true?"":0),true?"":0),";&:focus-within{",M.i.inputFocus,";",t&&(0,n.iv)("border-color:",a.Jv.stroke.danger,";"+(true?"":0),true?"":0),";}"+(true?"":0),true?"":0)},input:function e(t,r,o){return(0,n.iv)("&[data-input]{",u.c.body(),";border:none;box-shadow:none;background-color:transparent;padding-",t,":0;",r&&(0,n.iv)("padding-",t,":",a.W0[10],";"+(true?"":0),true?"":0),";",o==="large"&&(0,n.iv)("font-size:",a.JB[24],";font-weight:",a.Ue.medium,";height:34px;",r&&(0,n.iv)("padding-",t,":",a.W0[12],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)," &:focus{box-shadow:none;outline:none;}}"+(true?"":0),true?"":0)},inputLeftContent:function e(t,r){return(0,n.iv)(u.c.small()," ",M.i.flexCenter()," height:40px;min-width:48px;color:",a.Jv.icon.subdued,";padding-inline:",a.W0[12],";",r==="large"&&(0,n.iv)(u.c.body(),";"+(true?"":0),true?"":0)," ",t&&(0,n.iv)("border-right:1px solid ",a.Jv.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},inputRightContent:function e(t,r){return(0,n.iv)(u.c.small()," ",M.i.flexCenter()," height:40px;min-width:48px;color:",a.Jv.icon.subdued,";padding-inline:",a.W0[12],";",r==="large"&&(0,n.iv)(u.c.body(),";"+(true?"":0),true?"":0)," ",t&&(0,n.iv)("border-left:1px solid ",a.Jv.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}};var $n=r(7363);function Kn(e){"@babel/helpers - typeof";return Kn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Kn(e)}var Xn=["css"];function eo(e,t,r){t=to(t);if(t in e){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true})}else{e[t]=r}return e}function to(e){var t=ro(e,"string");return Kn(t)==="symbol"?t:String(t)}function ro(e,t){if(Kn(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==undefined){var n=r.call(e,t||"default");if(Kn(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function no(){no=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return no.apply(this,arguments)}function oo(e,t){if(e==null)return{};var r=io(e,t);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++){n=i[o];if(t.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(e,n))continue;r[n]=e[n]}}return r}function io(e,t){if(e==null)return{};var r={};var n=Object.keys(e);var o,i;for(i=0;i<n.length;i++){o=n[i];if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}function ao(e,t){return fo(e)||so(e,t)||lo(e,t)||uo()}function uo(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function lo(e,t){if(!e)return;if(typeof e==="string")return co(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor)r=e.constructor.name;if(r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return co(e,t)}function co(e,t){if(t==null||t>e.length)t=e.length;for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function so(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function fo(e){if(Array.isArray(e))return e}function po(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var vo=function e(t){var r;var o=t.field,i=t.fieldState,a=t.content,u=t.contentPosition,l=u===void 0?"left":u,s=t.showVerticalBar,d=s===void 0?true:s,f=t.type,p=f===void 0?"text":f,v=t.size,b=v===void 0?"regular":v,g=t.label,y=t.placeholder,m=y===void 0?"":y,w=t.disabled,C=t.readOnly,x=t.loading,Z=t.helpText,O=t.removeOptionsMinWidth,_=O===void 0?true:O,k=t.onChange,j=t.presetOptions,M=j===void 0?[]:j,I=t.selectOnFocus,P=I===void 0?false:I,D=t.wrapperCss,T=t.contentCss,W=t.removeBorder,J=W===void 0?false:W;var L=(r=o.value)!==null&&r!==void 0?r:"";var N=(0,c.useRef)(null);var F=(0,c.useState)(false),B=ao(F,2),z=B[0],R=B[1];var Q=(0,A.l)({isOpen:z,isDropdown:true}),U=Q.triggerRef,H=Q.triggerWidth,V=Q.position,G=Q.popoverRef;return(0,n.tZ)(h.Z,{fieldState:i,field:o,label:g,disabled:w,readOnly:C,loading:x,helpText:Z,removeBorder:J,placeholder:m},(function(e){var t;var r=e.css,u=oo(e,Xn);return(0,n.tZ)($n.Fragment,null,(0,n.tZ)("div",{css:[go.inputWrapper(!!i.error,J),D,true?"":0,true?"":0],ref:U},a&&l==="left"&&(0,n.tZ)("div",{css:[go.inputLeftContent(d,b),T,true?"":0,true?"":0]},a),(0,n.tZ)("input",no({},u,{css:[r,go.input(l,d,b),true?"":0,true?"":0],onClick:function e(){return R(true)},autoComplete:"off",readOnly:C,ref:function e(t){o.ref(t);N.current=t},onFocus:function e(){if(!P||!N.current){return}N.current.select()},value:L,onChange:function e(t){var r=p==="number"?t.target.value.replace(/[^0-9.]/g,"").replace(/(\..*)\./g,"$1"):t.target.value;o.onChange(r);if(k){k(r)}},"data-input":true})),a&&l==="right"&&(0,n.tZ)("div",{css:go.inputRightContent(d,b)},a)),(0,n.tZ)(A.h,{isOpen:z,onClickOutside:function e(){return R(false)},onEscape:function e(){return R(false)}},(0,n.tZ)("div",{css:[go.optionsWrapper,(t={},eo(t,E.dZ?"right":"left",V.left),eo(t,"top",V.top),eo(t,"maxWidth",H),t),true?"":0,true?"":0],ref:G},(0,n.tZ)("ul",{css:[go.options(_),true?"":0,true?"":0]},M.map((function(e){return(0,n.tZ)("li",{key:String(e.value),css:go.optionItem({isSelected:e.value===o.value})},(0,n.tZ)("button",{type:"button",css:go.label,onClick:function t(){o.onChange(e.value);k===null||k===void 0?void 0:k(e.value);R(false)}},(0,n.tZ)(Oe.Z,{when:e.icon},(0,n.tZ)(S.Z,{name:e.icon,width:32,height:32})),(0,n.tZ)("span",null,e.label)))}))))))}))};const bo=vo;var ho=true?{name:"16gsvie",styles:"min-width:200px"}:0;var go={mainWrapper:true?{name:"1d3w5wq",styles:"width:100%"}:0,inputWrapper:function e(t,r){return(0,n.iv)("display:flex;align-items:center;",!r&&(0,n.iv)("border:1px solid ",a.Jv.stroke["default"],";border-radius:",a.E0[6],";box-shadow:",a.AF.input,";background-color:",a.Jv.background.white,";"+(true?"":0),true?"":0)," ",t&&(0,n.iv)("border-color:",a.Jv.stroke.danger,";background-color:",a.Jv.background.status.errorFail,";"+(true?"":0),true?"":0),";&:focus-within{",M.i.inputFocus,";",t&&(0,n.iv)("border-color:",a.Jv.stroke.danger,";"+(true?"":0),true?"":0),";}"+(true?"":0),true?"":0)},input:function e(t,r,o){return(0,n.iv)("&[data-input]{",u.c.body(),";border:none;box-shadow:none;background-color:transparent;padding-",t,":0;",r&&(0,n.iv)("padding-",t,":",a.W0[10],";"+(true?"":0),true?"":0),";",o==="large"&&(0,n.iv)("font-size:",a.JB[24],";font-weight:",a.Ue.medium,";height:34px;",r&&(0,n.iv)("padding-",t,":",a.W0[12],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)," &:focus{box-shadow:none;outline:none;}}"+(true?"":0),true?"":0)},label:(0,n.iv)(M.i.resetButton,";width:100%;height:100%;display:flex;align-items:center;gap:",a.W0[8],";margin:0 ",a.W0[12],";padding:",a.W0[6]," 0;text-align:left;line-height:",a.Nv[24],";word-break:break-all;cursor:pointer;span{flex-shrink:0;}"+(true?"":0),true?"":0),optionsWrapper:true?{name:"1n0kzcr",styles:"position:absolute;width:100%"}:0,options:function e(t){return(0,n.iv)("z-index:",a.W5.dropdown,";background-color:",a.Jv.background.white,";list-style-type:none;box-shadow:",a.AF.popover,";padding:",a.W0[4]," 0;margin:0;max-height:500px;border-radius:",a.E0[6],";",M.i.overflowYAuto,";",!t&&ho,";"+(true?"":0),true?"":0)},optionItem:function e(t){var r=t.isSelected,o=r===void 0?false:r;return(0,n.iv)(u.c.body(),";min-height:36px;height:100%;width:100%;display:flex;align-items:center;transition:background-color 0.3s ease-in-out;cursor:pointer;&:hover{background-color:",a.Jv.background.hover,";}",o&&(0,n.iv)("background-color:",a.Jv.background.active,";position:relative;&::before{content:'';position:absolute;top:0;left:0;width:3px;height:100%;background-color:",a.Jv.action.primary["default"],";border-radius:0 ",a.E0[6]," ",a.E0[6]," 0;}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},inputLeftContent:function e(t,r){return(0,n.iv)(u.c.small()," ",M.i.flexCenter()," height:40px;min-width:48px;color:",a.Jv.icon.subdued,";padding-inline:",a.W0[12],";",r==="large"&&(0,n.iv)(u.c.body(),";"+(true?"":0),true?"":0)," ",t&&(0,n.iv)("border-right:1px solid ",a.Jv.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},inputRightContent:function e(t,r){return(0,n.iv)(u.c.small()," ",M.i.flexCenter()," height:40px;min-width:48px;color:",a.Jv.icon.subdued,";padding-inline:",a.W0[12],";",r==="large"&&(0,n.iv)(u.c.body(),";"+(true?"":0),true?"":0)," ",t&&(0,n.iv)("border-left:1px solid ",a.Jv.stroke["default"],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)}};var yo=s().forwardRef((function(e,t){var r=e.name,o=e.checked,i=e.readOnly,a=e.disabled,u=a===void 0?false:a,c=e.labelCss,s=e.inputCss,d=e.label,f=e.icon,p=e.value,v=e.onChange,b=e.onBlur;var h=(0,l.x0)();return(0,n.tZ)("label",{htmlFor:h,css:[mo.container(u),c,true?"":0,true?"":0]},(0,n.tZ)("input",{ref:t,id:h,name:r,type:"radio",checked:o,readOnly:i,value:p,disabled:u,onChange:v,onBlur:b,css:[mo.radio(d),true?"":0,true?"":0]}),(0,n.tZ)("span",null),f,d)}));var mo={container:function e(t){return(0,n.iv)(u.c.caption(),";display:flex;align-items:center;cursor:pointer;user-select:none;",t&&(0,n.iv)("color:",a.Jv.text.disable,";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},radio:function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:"";return(0,n.iv)("position:absolute;opacity:0;height:0;width:0;cursor:pointer;&+span{position:relative;cursor:pointer;height:18px;width:18px;background-color:",a.Jv.background.white,";border:2px solid ",a.Jv.stroke["default"],";border-radius:100%;",t&&(0,n.iv)("margin-right:",a.W0[10],";"+(true?"":0),true?"":0),";}&+span::before{content:'';position:absolute;left:3px;top:3px;background-color:",a.Jv.background.white,";width:8px;height:8px;border-radius:100%;}&:checked+span{border-color:",a.Jv.action.primary["default"],";}&:checked+span::before{background-color:",a.Jv.action.primary["default"],";}&:focus-visible{&+span{outline:2px solid ",a.Jv.stroke.brand,";outline-offset:1px;}}"+(true?"":0),true?"":0)}};const wo=yo;var Co=["css"];function xo(){xo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return xo.apply(this,arguments)}function Zo(e,t){if(e==null)return{};var r=Oo(e,t);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++){n=i[o];if(t.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(e,n))continue;r[n]=e[n]}}return r}function Oo(e,t){if(e==null)return{};var r={};var n=Object.keys(e);var o,i;for(i=0;i<n.length;i++){o=n[i];if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}var _o=function e(t){var r=t.field,o=t.fieldState,i=t.label,a=t.options,u=a===void 0?[]:a,l=t.disabled,c=t.wrapperCss,s=t.onSelect,d=t.onSelectRender;return(0,n.tZ)(h.Z,{field:r,fieldState:o,label:i,disabled:l},(function(e){var t=e.css,o=Zo(e,Co);return(0,n.tZ)("div",{css:c},u.map((function(e,i){return(0,n.tZ)("div",{key:i},(0,n.tZ)(wo,xo({},o,{inputCss:t,value:e.value,label:e.label,disabled:e.disabled||l,labelCss:e.labelCss,checked:r.value===e.value,onChange:function t(){r.onChange(e.value);if(s){s(e)}}})),d&&r.value===e.value&&d(e),e.legend&&(0,n.tZ)("span",{css:jo.radioLegend},e.legend))})))}))};const ko=_o;var jo={radioLegend:(0,n.iv)("margin-left:",a.W0[28],";",u.c.body(),";color:",a.Jv.text.subdued,";"+(true?"":0),true?"":0)};var So=r(91);var Eo=r(7662);var Ao=r(7573);function Mo(e){"@babel/helpers - typeof";return Mo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Mo(e)}var Io=["css"];function Po(e,t,r){t=Do(t);if(t in e){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true})}else{e[t]=r}return e}function Do(e){var t=To(e,"string");return Mo(t)==="symbol"?t:String(t)}function To(e,t){if(Mo(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==undefined){var n=r.call(e,t||"default");if(Mo(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Wo(){Wo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return Wo.apply(this,arguments)}function Jo(e,t){if(e==null)return{};var r=Lo(e,t);var n,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++){n=i[o];if(t.indexOf(n)>=0)continue;if(!Object.prototype.propertyIsEnumerable.call(e,n))continue;r[n]=e[n]}}return r}function Lo(e,t){if(e==null)return{};var r={};var n=Object.keys(e);var o,i;for(i=0;i<n.length;i++){o=n[i];if(t.indexOf(o)>=0)continue;r[o]=e[o]}return r}function No(e,t){return Qo(e)||Ro(e,t)||Bo(e,t)||Fo()}function Fo(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Bo(e,t){if(!e)return;if(typeof e==="string")return zo(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor)r=e.constructor.name;if(r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return zo(e,t)}function zo(e,t){if(t==null||t>e.length)t=e.length;for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Ro(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Qo(e){if(Array.isArray(e))return e}function Uo(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var Ho=function e(t){var r=t.label,o=t.field,i=t.fieldState,a=t.interval,u=a===void 0?30:a,l=t.disabled,s=t.loading,d=t.placeholder,f=t.helpText,p=t.isClearable,v=p===void 0?true:p;var b=(0,c.useState)(false),g=No(b,2),y=g[0],m=g[1];var w=(0,c.useRef)(null);var C=(0,c.useMemo)((function(){var e=(0,So.Z)((0,Eo.Z)(new Date,0),0);var t=(0,So.Z)((0,Eo.Z)(new Date,23),59);var r=(0,Ao.Z)({start:e,end:t},{step:u});return r.map((function(e){return(0,_.Z)(e,E.E_.hoursMinutes)}))}),[u]);var x=(0,A.l)({isOpen:y,isDropdown:true}),Z=x.triggerRef,O=x.triggerWidth,k=x.position,M=x.popoverRef;var I=Me({options:C.map((function(e){return{label:e,value:e}})),isOpen:y,selectedValue:o.value,onSelect:function e(t){o.onChange(t.value);m(false)},onClose:function e(){return m(false)}}),P=I.activeIndex,D=I.setActiveIndex;(0,c.useEffect)((function(){if(y&&P>=0&&w.current){w.current.scrollIntoView({block:"nearest",behavior:"smooth"})}}),[y,P]);return(0,n.tZ)(h.Z,{label:r,field:o,fieldState:i,disabled:l,loading:s,placeholder:d,helpText:f},(function(e){var t,r;var i=e.css,a=Jo(e,Io);return(0,n.tZ)("div",null,(0,n.tZ)("div",{css:Go.wrapper,ref:Z},(0,n.tZ)("input",Wo({},a,{ref:o.ref,css:[i,Go.input,true?"":0,true?"":0],type:"text",onClick:function e(t){t.stopPropagation();m((function(e){return!e}))},onKeyDown:function e(t){if(t.key==="Enter"){t.preventDefault();m((function(e){return!e}))}if(t.key==="Tab"){m(false)}},value:(t=o.value)!==null&&t!==void 0?t:"",onChange:function e(t){var r=t.target.value;o.onChange(r)},autoComplete:"off","data-input":true})),(0,n.tZ)(S.Z,{name:"clock",width:32,height:32,style:Go.icon}),v&&o.value&&(0,n.tZ)(j.Z,{variant:"text",buttonCss:Go.clearButton,onClick:function e(){return o.onChange("")}},(0,n.tZ)(S.Z,{name:"times",width:12,height:12}))),(0,n.tZ)(A.h,{isOpen:y,onClickOutside:function e(){return m(false)},onEscape:function e(){return m(false)}},(0,n.tZ)("div",{css:[Go.popover,(r={},Po(r,E.dZ?"right":"left",k.left),Po(r,"top",k.top),Po(r,"maxWidth",O),r),true?"":0,true?"":0],ref:M},(0,n.tZ)("ul",{css:Go.list},C.map((function(e,t){return(0,n.tZ)("li",{key:t,css:Go.listItem,ref:P===t?w:null,"data-active":P===t},(0,n.tZ)("button",{type:"button",css:Go.itemButton,onClick:function t(){o.onChange(e);m(false)},onMouseOver:function e(){return D(t)},onMouseLeave:function e(){t!==P&&D(-1)},onFocus:function e(){return D(t)}},e))}))))))}))};const Vo=Ho;var Go={wrapper:true?{name:"1wo2jxd",styles:"position:relative;&:hover,&:focus-within{&>button{opacity:1;}}"}:0,input:(0,n.iv)("&[data-input]{padding-left:",a.W0[40],";}"+(true?"":0),true?"":0),icon:(0,n.iv)("position:absolute;top:50%;left:",a.W0[8],";transform:translateY(-50%);color:",a.Jv.icon["default"],";"+(true?"":0),true?"":0),popover:(0,n.iv)("position:absolute;width:100%;background-color:",a.Jv.background.white,";box-shadow:",a.AF.popover,";height:380px;overflow-y:auto;border-radius:",a.E0[6],";"+(true?"":0),true?"":0),list:true?{name:"v5al3",styles:"list-style:none;padding:0;margin:0"}:0,listItem:(0,n.iv)("width:100%;height:40px;cursor:pointer;display:flex;align-items:center;transition:background-color 0.3s ease-in-out;&[data-active='true']{background-color:",a.Jv.background.hover,";}:hover{background-color:",a.Jv.background.hover,";}"+(true?"":0),true?"":0),itemButton:(0,n.iv)(M.i.resetButton,";",u.c.body(),";margin:",a.W0[4]," ",a.W0[12],";width:100%;height:100%;&:focus,&:active,&:hover{background:none;color:",a.Jv.text.primary,";}"+(true?"":0),true?"":0),clearButton:(0,n.iv)("position:absolute;top:50%;right:",a.W0[4],";transform:translateY(-50%);width:32px;height:32px;",M.i.flexCenter(),";opacity:0;transition:background-color 0.3s ease-in-out,opacity 0.3s ease-in-out;border-radius:",a.E0[2],";:hover{background-color:",a.Jv.background.hover,";}"+(true?"":0),true?"":0)};const Yo=r.p+"images/4d4615923a6630682b98f437e34c40a0-course-placeholder.png";function qo(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}function $o(e){var t=e.image,r=e.title,o=e.subTitle,i=e.handleDeleteClick;return(0,n.tZ)("div",{css:Ko.selectedItem},(0,n.tZ)("div",{css:Ko.selectedThumb},(0,n.tZ)("img",{src:t||Yo,css:Ko.thumbnail,alt:"course item"})),(0,n.tZ)("div",{css:Ko.selectedContent},(0,n.tZ)("div",{css:Ko.selectedTitle},r),(0,n.tZ)("div",{css:Ko.selectedSubTitle},o)),(0,n.tZ)("div",null,(0,n.tZ)(j.Z,{variant:"text",onClick:i},(0,n.tZ)(S.Z,{name:"delete",width:24,height:24}))))}var Ko={selectedItem:(0,n.iv)("padding:",a.W0[12],";display:flex;align-items:center;gap:",a.W0[16],";&:not(:last-child){border-bottom:1px solid ",a.Jv.stroke.divider,";}"+(true?"":0),true?"":0),selectedContent:true?{name:"1d3w5wq",styles:"width:100%"}:0,selectedTitle:(0,n.iv)(u.c.small(),";color:",a.Jv.text.primary,";margin-bottom:",a.W0[4],";"+(true?"":0),true?"":0),selectedSubTitle:(0,n.iv)(u.c.small(),";color:",a.Jv.text.hints,";"+(true?"":0),true?"":0),selectedThumb:true?{name:"128tros",styles:"height:48px"}:0,thumbnail:(0,n.iv)("width:48px;height:48px;border-radius:",a.E0[4],";object-fit:cover;"+(true?"":0),true?"":0)};function Xo(e){"@babel/helpers - typeof";return Xo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Xo(e)}function ei(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function ti(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ei(Object(r),!0).forEach((function(t){ri(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ei(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ri(e,t,r){t=ni(t);if(t in e){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true})}else{e[t]=r}return e}function ni(e){var t=oi(e,"string");return Xo(t)==="symbol"?t:String(t)}function oi(e,t){if(Xo(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==undefined){var n=r.call(e,t||"default");if(Xo(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function ii(e,t){return si(e)||ci(e,t)||ui(e,t)||ai()}function ai(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function ui(e,t){if(!e)return;if(typeof e==="string")return li(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor)r=e.constructor.name;if(r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return li(e,t)}function li(e,t){if(t==null||t>e.length)t=e.length;for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ci(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function si(e){if(Array.isArray(e))return e}var di=function e(){var t=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{},r=t.limit,n=r===void 0?E.gK:r;var o=(0,c.useState)({page:1,sortProperty:"",sortDirection:undefined,filter:{}}),i=ii(o,2),a=i[0],u=i[1];var l=a;var s=n*Math.max(0,l.page-1);var d=(0,c.useCallback)((function(e){u((function(t){return ti(ti({},t),e)}))}),[u]);var f=function e(t){return d({page:t})};var p=(0,c.useCallback)((function(e){return d({page:1,filter:e})}),[d]);var v=function e(t){var r={};if(t!==l.sortProperty){r={sortDirection:"asc",sortProperty:t}}else{r={sortDirection:l.sortDirection==="asc"?"desc":"asc",sortProperty:t}}d(r)};return{pageInfo:l,onPageChange:f,onColumnSort:v,offset:s,itemsPerPage:n,onFilterItems:p}};function fi(e,t){return gi(e)||hi(e,t)||vi(e,t)||pi()}function pi(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function vi(e,t){if(!e)return;if(typeof e==="string")return bi(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor)r=e.constructor.name;if(r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return bi(e,t)}function bi(e,t){if(t==null||t>e.length)t=e.length;for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function hi(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function gi(e){if(Array.isArray(e))return e}var yi=function e(t){var r=t.currentPage,i=t.onPageChange,a=t.totalItems,u=t.itemsPerPage;var l=Math.max(Math.ceil(a/u),1);var s=(0,c.useState)(""),d=fi(s,2),f=d[0],p=d[1];(0,c.useEffect)((function(){p(r.toString())}),[r]);var v=function e(t){if(t<1||t>l){return}i(t)};return(0,n.tZ)("div",{css:wi.wrapper},(0,n.tZ)("div",{css:wi.pageStatus},(0,o.__)("Page","tutor"),(0,n.tZ)("span",null,(0,n.tZ)("input",{type:"text",css:wi.paginationInput,value:f,onChange:function e(t){var r=t.currentTarget.value;var n=r.replace(/[^0-9]/g,"");var o=Number(n);if(o>0&&o<=l){p(n);i(o)}else if(!n){p(n)}},autoComplete:"off"})),(0,o.__)("of","tutor")," ",(0,n.tZ)("span",null,l)),(0,n.tZ)("div",{css:wi.pageController},(0,n.tZ)("button",{type:"button",css:wi.paginationButton,onClick:function e(){return v(r-1)},disabled:r===1},(0,n.tZ)(S.Z,{name:!E.dZ?"chevronLeft":"chevronRight",width:32,height:32})),(0,n.tZ)("button",{type:"button",css:wi.paginationButton,onClick:function e(){return v(r+1)},disabled:r===l},(0,n.tZ)(S.Z,{name:!E.dZ?"chevronRight":"chevronLeft",width:32,height:32}))))};const mi=yi;var wi={wrapper:(0,n.iv)("display:flex;justify-content:end;align-items:center;flex-wrap:wrap;gap:",a.W0[8],";height:36px;"+(true?"":0),true?"":0),pageStatus:(0,n.iv)(u.c.body()," color:",a.Jv.text.title,";min-width:100px;"+(true?"":0),true?"":0),paginationInput:(0,n.iv)("outline:0;border:1px solid ",a.Jv.stroke["default"],";border-radius:",a.E0[6],";margin:0 ",a.W0[8],";color:",a.Jv.text.subdued,";padding:8px 12px;width:72px;&::-webkit-outer-spin-button,&::-webkit-inner-spin-button{-webkit-appearance:none;margin:",a.W0[0],";}&[type='number']{-moz-appearance:textfield;}"+(true?"":0),true?"":0),pageController:(0,n.iv)("gap:",a.W0[8],";display:flex;justify-content:center;align-items:center;height:100%;"+(true?"":0),true?"":0),paginationButton:(0,n.iv)(M.i.resetButton,";background:",a.Jv.background.white,";color:",a.Jv.icon["default"],";border-radius:",a.E0[6],";height:32px;width:32px;display:grid;place-items:center;transition:background-color 0.2s ease-in-out,color 0.3s ease-in-out;svg{color:",a.Jv.icon["default"],";}&:hover{background:",a.Jv.background["default"],";&>svg{color:",a.Jv.icon.brand,";}}&:disabled{background:",a.Jv.background.white,";&>svg{color:",a.Jv.icon.disable["default"],";}}"+(true?"":0),true?"":0)};function Ci(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var xi={bodyRowSelected:a.Jv.background.active,bodyRowHover:a.Jv.background.hover};var Zi=true?{name:"1azakc",styles:"text-align:center"}:0;var Oi=function e(t){var r=t.columns,o=t.data,i=t.entireHeader,a=i===void 0?null:i,u=t.headerHeight,c=u===void 0?60:u,s=t.noHeader,d=s===void 0?false:s,f=t.isStriped,p=f===void 0?false:f,v=t.isRounded,b=v===void 0?false:v,h=t.stripedBySelectedIndex,g=h===void 0?[]:h,y=t.colors,m=y===void 0?{}:y,w=t.isBordered,C=w===void 0?true:w,x=t.loading,Z=x===void 0?false:x,O=t.itemsPerPage,_=O===void 0?1:O,k=t.querySortProperty,j=t.querySortDirection,E=j===void 0?"asc":j,A=t.onSortClick,M=t.renderInLastRow,I=t.rowStyle;var P=function e(t,o){return(0,n.tZ)("tr",{key:t,css:[ji.tableRow({isBordered:C,isStriped:p}),ji.bodyTr({colors:m,isSelected:g.includes(t),isRounded:b}),I,true?"":0,true?"":0]},r.map((function(e,t){return(0,n.tZ)("td",{key:t,css:[ji.td,{width:e.width},true?"":0,true?"":0]},o(e))})))};var D=function e(t){var r=null;var o=t.sortProperty;if(!o){return t.Header}if(t.sortProperty===k){if(E==="asc"){r=(0,n.tZ)(S.Z,{name:"chevronDown"})}else{r=(0,n.tZ)(S.Z,{name:"chevronUp"})}}return(0,n.tZ)("button",{type:"button",css:ji.headerWithIcon,onClick:function e(){return A===null||A===void 0?void 0:A(o)}},t.Header,r&&r)};var T=function e(){if(a){return(0,n.tZ)("th",{css:ji.th,colSpan:r.length},a)}return r.map((function(e,t){if(e.Header!==null){return(0,n.tZ)("th",{key:t,css:[ji.th,{width:e.width},true?"":0,true?"":0],colSpan:e.headerColSpan},D(e))}}))};var W=function e(){if(Z){return(0,l.w6)(_).map((function(e){return P(e,(function(){return(0,n.tZ)(pt,{animation:true,height:20,width:"".concat((0,l.sZ)(40,80),"%")})}))}))}if(!o.length){return(0,n.tZ)("tr",{css:ji.tableRow({isBordered:false,isStriped:false})},(0,n.tZ)("td",{colSpan:r.length,css:[ji.td,Zi,true?"":0,true?"":0]},"No Data!"))}var t=o.map((function(e,t){return P(t,(function(r){return"Cell"in r?r.Cell(e,t):r.accessor(e,t)}))}));if(M){M=(0,n.tZ)("tr",{key:t.length},(0,n.tZ)("td",{css:ji.td},M));t.push(M)}return t};return(0,n.tZ)("div",{css:ji.tableContainer({isRounded:b})},(0,n.tZ)("table",{css:ji.table},!d&&(0,n.tZ)("thead",null,(0,n.tZ)("tr",{css:[ji.tableRow({isBordered:C,isStriped:p}),{height:c},true?"":0,true?"":0]},T())),(0,n.tZ)("tbody",null,W())))};const _i=Oi;var ki=true?{name:"1hr9znz",styles:":last-of-type{border-bottom:none;}"}:0;var ji={tableContainer:function e(t){var r=t.isRounded;return(0,n.iv)("display:block;width:100%;overflow-x:auto;",r&&(0,n.iv)("border:1px solid ",a.Jv.stroke.divider,";border-radius:",a.E0[6],";"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},headerWithIcon:(0,n.iv)(M.i.resetButton,";",u.c.body(),";color:",a.Jv.text.subdued,";display:flex;gap:",a.W0[4],";align-items:center;"+(true?"":0),true?"":0),table:true?{name:"1k58b2x",styles:"width:100%;border-collapse:collapse;border:none"}:0,tableRow:function e(t){var r=t.isBordered,o=t.isStriped;return(0,n.iv)(r&&(0,n.iv)("border-bottom:1px solid ",a.Jv.stroke.divider,";"+(true?"":0),true?"":0)," ",o&&(0,n.iv)("&:nth-of-type(even){background-color:",a.Jv.background.active,";}"+(true?"":0),true?"":0),";"+(true?"":0),true?"":0)},th:(0,n.iv)(u.c.body(),";background-color:",a.Jv.background.white,";color:",a.Jv.text.primary,";padding:0 ",a.W0[16],";border:none;"+(true?"":0),true?"":0),bodyTr:function e(t){var r=t.colors,o=t.isSelected,i=t.isRounded;var a=r.bodyRowDefault,u=r.bodyRowSelectedHover,l=r.bodyRowHover,c=l===void 0?xi.bodyRowHover:l,s=r.bodyRowSelected,d=s===void 0?xi.bodyRowSelected:s;return(0,n.iv)(a&&(0,n.iv)("background-color:",a,";"+(true?"":0),true?"":0)," &:hover{background-color:",o&&u?u:c,";}",o&&(0,n.iv)("background-color:",d,";"+(true?"":0),true?"":0)," ",i&&ki,";"+(true?"":0),true?"":0)},td:(0,n.iv)(u.c.body(),";padding:",a.W0[16],";border:none;"+(true?"":0),true?"":0)};var Si=r(368);var Ei=r(4139);function Ai(e){"@babel/helpers - typeof";return Ai="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ai(e)}function Mi(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Ii(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Mi(Object(r),!0).forEach((function(t){Pi(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Mi(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Pi(e,t,r){t=Di(t);if(t in e){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true})}else{e[t]=r}return e}function Di(e){var t=Ti(e,"string");return Ai(t)==="symbol"?t:String(t)}function Ti(e,t){if(Ai(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==undefined){var n=r.call(e,t||"default");if(Ai(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Wi=function e(t){return Wt.R.get(Jt.Z.COUPON_APPLIES_TO,{params:Ii({},t)})};var Ji=function e(t){return(0,Si.a)({queryKey:["CourseCategory",t],placeholderData:Ei.Wk,queryFn:function e(){return Wi(t).then((function(e){return e.data}))}})};function Li(e,t){return Ri(e)||zi(e,t)||Fi(e,t)||Ni()}function Ni(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Fi(e,t){if(!e)return;if(typeof e==="string")return Bi(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor)r=e.constructor.name;if(r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Bi(e,t)}function Bi(e,t){if(t==null||t>e.length)t=e.length;for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function zi(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Ri(e){if(Array.isArray(e))return e}var Qi=function e(t){var r=arguments.length>1&&arguments[1]!==undefined?arguments[1]:300;var n=(0,c.useState)(t),o=Li(n,2),i=o[0],a=o[1];(0,c.useEffect)((function(){var e=setTimeout((function(){a(t)}),r);return function(){clearTimeout(e)}}),[t,r]);return i};function Ui(e){"@babel/helpers - typeof";return Ui="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ui(e)}function Hi(){Hi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return Hi.apply(this,arguments)}function Vi(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Gi(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Vi(Object(r),!0).forEach((function(t){Yi(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Vi(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Yi(e,t,r){t=qi(t);if(t in e){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true})}else{e[t]=r}return e}function qi(e){var t=$i(e,"string");return Ui(t)==="symbol"?t:String(t)}function $i(e,t){if(Ui(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==undefined){var n=r.call(e,t||"default");if(Ui(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}var Ki=function e(t){var r=t.onFilterItems;var i=Pt({defaultValues:{search:""}});var a=Qi(i.watch("search"));(0,c.useEffect)((function(){r(Gi({},a.length>0&&{search:a}))}),[r,a]);return(0,n.tZ)(X.Qr,{control:i.control,name:"search",render:function e(t){return(0,n.tZ)(Yn,Hi({},t,{content:(0,n.tZ)(S.Z,{name:"search",width:24,height:24}),placeholder:(0,o.__)("Search...","tutor"),showVerticalBar:false}))}})};const Xi=Ki;var ea=r(7363);function ta(e){return ia(e)||oa(e)||na(e)||ra()}function ra(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function na(e,t){if(!e)return;if(typeof e==="string")return aa(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor)r=e.constructor.name;if(r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return aa(e,t)}function oa(e){if(typeof Symbol!=="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function ia(e){if(Array.isArray(e))return aa(e)}function aa(e,t){if(t==null||t>e.length)t=e.length;for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ua(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var la=function e(t){var r,i,a,u,l;var c=t.form;var s=(r=c.watch("categories"))!==null&&r!==void 0?r:[];var d=di(),f=d.pageInfo,p=d.onPageChange,v=d.itemsPerPage,h=d.offset,g=d.onFilterItems;var y=Ji({applies_to:"specific_category",offset:h,limit:v,filter:f.filter});var m=(i=(a=y.data)===null||a===void 0?void 0:a.results)!==null&&i!==void 0?i:[];function w(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;var t=s.map((function(e){return e.id}));var r=m.map((function(e){return e.id}));if(e){var n=m.filter((function(e){return!t.includes(e.id)}));c.setValue("categories",[].concat(ta(s),ta(n)));return}var o=s.filter((function(e){return!r.includes(e.id)}));c.setValue("categories",o)}function C(){return m.every((function(e){return s.map((function(e){return e.id})).includes(e.id)}))}var x=[{Header:(u=y.data)!==null&&u!==void 0&&u.results.length?(0,n.tZ)(b,{onChange:w,checked:y.isLoading||y.isRefetching?false:C(),label:(0,o.__)("Category","tutor")}):(0,o.__)("Category","tutor"),Cell:function e(t){return(0,n.tZ)("div",{css:sa.checkboxWrapper},(0,n.tZ)(b,{onChange:function e(){var r=s.filter((function(e){return e.id!==t.id}));var n=(r===null||r===void 0?void 0:r.length)===s.length;if(n){c.setValue("categories",[].concat(ta(r),[t]))}else{c.setValue("categories",r)}},checked:s.map((function(e){return e.id})).includes(t.id)}),(0,n.tZ)("img",{src:t.image||Yo,css:sa.thumbnail,alt:(0,o.__)("category item","tutor")}),(0,n.tZ)("div",{css:sa.courseItem},(0,n.tZ)("div",null,t.title),(0,n.tZ)("p",null,"".concat(t.total_courses," ").concat((0,o.__)("Courses","tutor")))))},width:720}];if(y.isLoading){return(0,n.tZ)(te.g4,null)}if(!y.data){return(0,n.tZ)("div",{css:sa.errorMessage},(0,o.__)("Something went wrong","tutor"))}return(0,n.tZ)(ea.Fragment,null,(0,n.tZ)("div",{css:sa.tableActions},(0,n.tZ)(Xi,{onFilterItems:g})),(0,n.tZ)("div",{css:sa.tableWrapper},(0,n.tZ)(_i,{columns:x,data:(l=y.data.results)!==null&&l!==void 0?l:[],itemsPerPage:v,loading:y.isFetching||y.isRefetching})),(0,n.tZ)("div",{css:sa.paginatorWrapper},(0,n.tZ)(mi,{currentPage:f.page,onPageChange:p,totalItems:y.data.total_items,itemsPerPage:v})))};const ca=la;var sa={tableActions:(0,n.iv)("padding:",a.W0[20],";"+(true?"":0),true?"":0),tableWrapper:true?{name:"1uijx3y",styles:"max-height:calc(100vh - 350px);overflow:auto"}:0,paginatorWrapper:(0,n.iv)("margin:",a.W0[20]," ",a.W0[16],";"+(true?"":0),true?"":0),checkboxWrapper:(0,n.iv)("display:flex;align-items:center;gap:",a.W0[12],";"+(true?"":0),true?"":0),courseItem:(0,n.iv)(u.c.caption(),";margin-left:",a.W0[4],";"+(true?"":0),true?"":0),thumbnail:(0,n.iv)("width:48px;height:48px;border-radius:",a.E0[4],";"+(true?"":0),true?"":0),errorMessage:true?{name:"1tw8cl2",styles:"height:100px;display:flex;align-items:center;justify-content:center"}:0};var da=r(7363);function fa(e){return ha(e)||ba(e)||va(e)||pa()}function pa(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function va(e,t){if(!e)return;if(typeof e==="string")return ga(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor)r=e.constructor.name;if(r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ga(e,t)}function ba(e){if(typeof Symbol!=="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function ha(e){if(Array.isArray(e))return ga(e)}function ga(e,t){if(t==null||t>e.length)t=e.length;for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function ya(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}var ma=function e(t){var r,i,a,u;var l=t.type,c=t.form;var s=c.watch(l)||[];var d=di(),f=d.pageInfo,p=d.onPageChange,v=d.itemsPerPage,h=d.offset,g=d.onFilterItems;var y=Ji({applies_to:l==="courses"?"specific_courses":"specific_bundles",offset:h,limit:v,filter:f.filter});var m=(r=(i=y.data)===null||i===void 0?void 0:i.results)!==null&&r!==void 0?r:[];function w(){var e=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;var t=s.map((function(e){return e.id}));var r=m.map((function(e){return e.id}));if(e){var n=m.filter((function(e){return!t.includes(e.id)}));c.setValue(l,[].concat(fa(s),fa(n)));return}var o=s.filter((function(e){return!r.includes(e.id)}));c.setValue(l,o)}function C(){return m.every((function(e){return s.map((function(e){return e.id})).includes(e.id)}))}var x=[{Header:(a=y.data)!==null&&a!==void 0&&a.results.length?(0,n.tZ)(b,{onChange:w,checked:y.isLoading||y.isRefetching?false:C(),label:l==="courses"?(0,o.__)("Courses","tutor"):(0,o.__)("Bundles","tutor"),labelCss:Ca.checkboxLabel}):"#",Cell:function e(t){return(0,n.tZ)("div",{css:Ca.checkboxWrapper},(0,n.tZ)(b,{onChange:function e(){var r=s.filter((function(e){return e.id!==t.id}));var n=(r===null||r===void 0?void 0:r.length)===s.length;if(n){c.setValue(l,[].concat(fa(r),[t]))}else{c.setValue(l,r)}},checked:s.map((function(e){return e.id})).includes(t.id)}),(0,n.tZ)("img",{src:t.image||Yo,css:Ca.thumbnail,alt:(0,o.__)("course item","tutor")}),(0,n.tZ)("div",{css:Ca.courseItem},(0,n.tZ)("div",null,t.title),(0,n.tZ)("p",null,t.author)))}},{Header:(0,o.__)("Price","tutor"),Cell:function e(t){return(0,n.tZ)("div",{css:Ca.price},t.plan_start_price?(0,n.tZ)("span",{css:Ca.startingFrom},(0,o.sprintf)((0,o.__)("Starting from %s","tutor"),t.plan_start_price)):(0,n.tZ)(da.Fragment,null,(0,n.tZ)("span",null,t.sale_price?t.sale_price:t.regular_price),t.sale_price&&(0,n.tZ)("span",{css:Ca.discountPrice},t.regular_price)))}}];if(y.isLoading){return(0,n.tZ)(te.g4,null)}if(!y.data){return(0,n.tZ)("div",{css:Ca.errorMessage},(0,o.__)("Something went wrong","tutor"))}return(0,n.tZ)(da.Fragment,null,(0,n.tZ)("div",{css:Ca.tableActions},(0,n.tZ)(Xi,{onFilterItems:g})),(0,n.tZ)("div",{css:Ca.tableWrapper},(0,n.tZ)(_i,{columns:x,data:(u=y.data.results)!==null&&u!==void 0?u:[],itemsPerPage:v,loading:y.isFetching||y.isRefetching})),(0,n.tZ)("div",{css:Ca.paginatorWrapper},(0,n.tZ)(mi,{currentPage:f.page,onPageChange:p,totalItems:y.data.total_items,itemsPerPage:v})))};const wa=ma;var Ca={tableActions:(0,n.iv)("padding:",a.W0[20],";"+(true?"":0),true?"":0),tableWrapper:true?{name:"1uijx3y",styles:"max-height:calc(100vh - 350px);overflow:auto"}:0,paginatorWrapper:(0,n.iv)("margin:",a.W0[20]," ",a.W0[16],";"+(true?"":0),true?"":0),checkboxWrapper:(0,n.iv)("display:flex;align-items:center;gap:",a.W0[12],";"+(true?"":0),true?"":0),courseItem:(0,n.iv)(u.c.caption(),";margin-left:",a.W0[4],";"+(true?"":0),true?"":0),thumbnail:(0,n.iv)("width:48px;height:48px;border-radius:",a.E0[4],";"+(true?"":0),true?"":0),checkboxLabel:(0,n.iv)(u.c.body(),";color:",a.Jv.text.primary,";"+(true?"":0),true?"":0),price:(0,n.iv)("display:flex;gap:",a.W0[4],";justify-content:end;"+(true?"":0),true?"":0),discountPrice:(0,n.iv)("text-decoration:line-through;color:",a.Jv.text.subdued,";"+(true?"":0),true?"":0),errorMessage:true?{name:"1tw8cl2",styles:"height:100px;display:flex;align-items:center;justify-content:center"}:0,startingFrom:(0,n.iv)("color:",a.Jv.text.hints,";"+(true?"":0),true?"":0)};function xa(e){var t=e.title,r=e.closeModal,i=e.actions,a=e.form,u=e.type;var l=Pt({defaultValues:a.getValues()});var c=l.watch(u)||[];function s(){a.setValue(u,c,{shouldDirty:true});r({action:"CONFIRM"})}return(0,n.tZ)(fr.Z,{onClose:function e(){return r({action:"CLOSE"})},title:c.length?(0,o.sprintf)((0,o.__)("%d Selected","tutor"),c.length):t,actions:i,maxWidth:720},(0,n.tZ)(Oe.Z,{when:u==="categories",fallback:(0,n.tZ)(wa,{form:l,type:u==="bundles"?"bundles":"courses"})},(0,n.tZ)(ca,{form:l})),(0,n.tZ)("div",{css:Oa.footer},(0,n.tZ)(j.Z,{size:"small",variant:"text",onClick:function e(){return r({action:"CLOSE"})}},(0,o.__)("Cancel","tutor")),(0,n.tZ)(j.Z,{size:"small",variant:"primary",onClick:s},(0,o.__)("Apply","tutor"))))}const Za=xa;var Oa={footer:(0,n.iv)("box-shadow:0px 1px 0px 0px #e4e5e7 inset;height:56px;display:flex;align-items:center;justify-content:end;gap:",a.W0[16],";padding-inline:",a.W0[16],";"+(true?"":0),true?"":0)};var _a=r(7363);function ka(e){var t=e.form;var r=(0,Lr.d)(),i=r.showModal;var a=t.watch("categories");return(0,n.tZ)(_a.Fragment,null,(0,n.tZ)(Oe.Z,{when:a.length},(0,n.tZ)("div",{css:ja.categoriesWrapper},(0,n.tZ)(Ce.Z,{each:a},(function(e){return(0,n.tZ)($o,{title:e.title,subTitle:(0,o.sprintf)((0,o.__)("%s Courses","tutor-pro"),e.total_courses),image:e.image,handleDeleteClick:function r(){t.setValue("categories",a.filter((function(t){return t.id!==e.id})),{shouldDirty:true})}})})))),(0,n.tZ)(j.Z,{variant:"tertiary",isOutlined:true,buttonCss:ja.addCategoriesButton,icon:(0,n.tZ)(S.Z,{name:"plusSquareBrand",width:24,height:25}),onClick:function e(){i({component:Za,props:{title:(0,o.__)("Selected items","tutor-pro"),type:"categories",form:t},closeOnOutsideClick:true,depthIndex:9999})}},(0,o.__)("Add Categories","tutor-pro")))}var ja={categoriesWrapper:(0,n.iv)("background-color:",a.Jv.background.white,";border:1px solid ",a.Jv.stroke.divider,";border-radius:",a.E0[6],";"+(true?"":0),true?"":0),addCategoriesButton:(0,n.iv)("width:fit-content;background-color:",a.Jv.background.white,";color:",a.Jv.text.brand,";svg,:active svg{color:",a.Jv.text.brand,"!important;}"+(true?"":0),true?"":0)};var Sa=r(4705);var Ea=r(4285);var Aa={tick_circle:'<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16.2806 9.21937C16.3504 9.28903 16.4057 9.37175 16.4434 9.46279C16.4812 9.55384 16.5006 9.65144 16.5006 9.75C16.5006 9.84856 16.4812 9.94616 16.4434 10.0372C16.4057 10.1283 16.3504 10.211 16.2806 10.2806L11.0306 15.5306C10.961 15.6004 10.8783 15.6557 10.7872 15.6934C10.6962 15.7312 10.5986 15.7506 10.5 15.7506C10.4014 15.7506 10.3038 15.7312 10.2128 15.6934C10.1218 15.6557 10.039 15.6004 9.96938 15.5306L7.71938 13.2806C7.57865 13.1399 7.49959 12.949 7.49959 12.75C7.49959 12.551 7.57865 12.3601 7.71938 12.2194C7.86011 12.0786 8.05098 11.9996 8.25 11.9996C8.44903 11.9996 8.6399 12.0786 8.78063 12.2194L10.5 13.9397L15.2194 9.21937C15.289 9.14964 15.3718 9.09432 15.4628 9.05658C15.5538 9.01884 15.6514 8.99941 15.75 8.99941C15.8486 8.99941 15.9462 9.01884 16.0372 9.05658C16.1283 9.09432 16.211 9.14964 16.2806 9.21937ZM21.75 12C21.75 13.9284 21.1782 15.8134 20.1068 17.4168C19.0355 19.0202 17.5127 20.2699 15.7312 21.0078C13.9496 21.7458 11.9892 21.9389 10.0979 21.5627C8.20656 21.1865 6.46928 20.2579 5.10571 18.8943C3.74215 17.5307 2.81355 15.7934 2.43735 13.9021C2.06114 12.0108 2.25422 10.0504 2.99218 8.26884C3.73013 6.48726 4.97982 4.96451 6.58319 3.89317C8.18657 2.82183 10.0716 2.25 12 2.25C14.585 2.25273 17.0634 3.28084 18.8913 5.10872C20.7192 6.93661 21.7473 9.41498 21.75 12ZM20.25 12C20.25 10.3683 19.7661 8.77325 18.8596 7.41655C17.9531 6.05984 16.6646 5.00242 15.1571 4.37799C13.6497 3.75357 11.9909 3.59019 10.3905 3.90852C8.79017 4.22685 7.32016 5.01259 6.16637 6.16637C5.01259 7.32015 4.22685 8.79016 3.90853 10.3905C3.5902 11.9908 3.75358 13.6496 4.378 15.1571C5.00242 16.6646 6.05984 17.9531 7.41655 18.8596C8.77326 19.7661 10.3683 20.25 12 20.25C14.1873 20.2475 16.2843 19.3775 17.8309 17.8309C19.3775 16.2843 20.2475 14.1873 20.25 12Z" fill="currentColor"/></svg>',cross_circle:'<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M15.5306 9.53063L13.0603 12L15.5306 14.4694C15.6003 14.5391 15.6556 14.6218 15.6933 14.7128C15.731 14.8039 15.7504 14.9015 15.7504 15C15.7504 15.0985 15.731 15.1961 15.6933 15.2872C15.6556 15.3782 15.6003 15.4609 15.5306 15.5306C15.4609 15.6003 15.3782 15.6556 15.2872 15.6933C15.1961 15.731 15.0986 15.7504 15 15.7504C14.9015 15.7504 14.8039 15.731 14.7128 15.6933C14.6218 15.6556 14.5391 15.6003 14.4694 15.5306L12 13.0603L9.53063 15.5306C9.46095 15.6003 9.37822 15.6556 9.28718 15.6933C9.19613 15.731 9.09855 15.7504 9 15.7504C8.90146 15.7504 8.80388 15.731 8.71283 15.6933C8.62179 15.6556 8.53906 15.6003 8.46938 15.5306C8.3997 15.4609 8.34442 15.3782 8.30671 15.2872C8.269 15.1961 8.24959 15.0985 8.24959 15C8.24959 14.9015 8.269 14.8039 8.30671 14.7128C8.34442 14.6218 8.3997 14.5391 8.46938 14.4694L10.9397 12L8.46938 9.53063C8.32865 9.38989 8.24959 9.19902 8.24959 9C8.24959 8.80098 8.32865 8.61011 8.46938 8.46937C8.61011 8.32864 8.80098 8.24958 9 8.24958C9.19903 8.24958 9.3899 8.32864 9.53063 8.46937L12 10.9397L14.4694 8.46937C14.5391 8.39969 14.6218 8.34442 14.7128 8.3067C14.8039 8.26899 14.9015 8.24958 15 8.24958C15.0986 8.24958 15.1961 8.26899 15.2872 8.3067C15.3782 8.34442 15.4609 8.39969 15.5306 8.46937C15.6003 8.53906 15.6556 8.62178 15.6933 8.71283C15.731 8.80387 15.7504 8.90145 15.7504 9C15.7504 9.09855 15.731 9.19613 15.6933 9.28717C15.6556 9.37822 15.6003 9.46094 15.5306 9.53063ZM21.75 12C21.75 13.9284 21.1782 15.8134 20.1068 17.4168C19.0355 19.0202 17.5127 20.2699 15.7312 21.0078C13.9496 21.7458 11.9892 21.9389 10.0979 21.5627C8.20656 21.1865 6.46928 20.2579 5.10571 18.8943C3.74215 17.5307 2.81355 15.7934 2.43735 13.9021C2.06114 12.0108 2.25422 10.0504 2.99218 8.26884C3.73013 6.48726 4.97982 4.96451 6.58319 3.89317C8.18657 2.82183 10.0716 2.25 12 2.25C14.585 2.25273 17.0634 3.28084 18.8913 5.10872C20.7192 6.93661 21.7473 9.41498 21.75 12ZM20.25 12C20.25 10.3683 19.7661 8.77325 18.8596 7.41655C17.9531 6.05984 16.6646 5.00242 15.1571 4.37799C13.6497 3.75357 11.9909 3.59019 10.3905 3.90852C8.79017 4.22685 7.32016 5.01259 6.16637 6.16637C5.01259 7.32015 4.22685 8.79016 3.90853 10.3905C3.5902 11.9908 3.75358 13.6496 4.378 15.1571C5.00242 16.6646 6.05984 17.9531 7.41655 18.8596C8.77326 19.7661 10.3683 20.25 12 20.25C14.1873 20.2475 16.2843 19.3775 17.8309 17.8309C19.3775 16.2843 20.2475 14.1873 20.25 12Z" fill="currentColor"/></svg>',tick:'<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M5 14.5C5 14.5 6.5 14.5 8.5 18C8.5 18 14.059 8.833 19 7" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>',cross:'<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M5.47032 5.46934C5.61094 5.32889 5.80157 5.25 6.00032 5.25C6.19907 5.25 6.38969 5.32889 6.53032 5.46934L18.5303 17.4693C18.604 17.538 18.6631 17.6208 18.7041 17.7128C18.7451 17.8048 18.7671 17.9041 18.7689 18.0048C18.7707 18.1055 18.7522 18.2055 18.7144 18.2989C18.6767 18.3923 18.6206 18.4772 18.5494 18.5484C18.4781 18.6196 18.3933 18.6757 18.2999 18.7135C18.2065 18.7512 18.1065 18.7697 18.0058 18.7679C17.9051 18.7662 17.8058 18.7441 17.7138 18.7031C17.6218 18.6621 17.539 18.603 17.4703 18.5293L5.47032 6.52934C5.32987 6.38871 5.25098 6.19809 5.25098 5.99934C5.25098 5.80059 5.32987 5.60997 5.47032 5.46934Z" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M18.5298 5.46934C18.6703 5.60997 18.7492 5.80059 18.7492 5.99934C18.7492 6.19809 18.6703 6.38871 18.5298 6.52934L6.52985 18.5293C6.38767 18.6618 6.19963 18.7339 6.00532 18.7305C5.81102 18.7271 5.62564 18.6484 5.48822 18.511C5.35081 18.3735 5.2721 18.1882 5.26867 17.9939C5.26524 17.7996 5.33737 17.6115 5.46985 17.4693L17.4698 5.46934C17.6105 5.32889 17.8011 5.25 17.9998 5.25C18.1986 5.25 18.3892 5.32889 18.5298 5.46934Z" fill="currentColor"/></svg>',plus_square:'<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H19.5C19.8978 21 20.2794 20.842 20.5607 20.5607C20.842 20.2794 21 19.8978 21 19.5V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM19.5 19.5H4.5V4.5H19.5V19.5ZM16.5 12C16.5 12.1989 16.421 12.3897 16.2803 12.5303C16.1397 12.671 15.9489 12.75 15.75 12.75H12.75V15.75C12.75 15.9489 12.671 16.1397 12.5303 16.2803C12.3897 16.421 12.1989 16.5 12 16.5C11.8011 16.5 11.6103 16.421 11.4697 16.2803C11.329 16.1397 11.25 15.9489 11.25 15.75V12.75H8.25C8.05109 12.75 7.86032 12.671 7.71967 12.5303C7.57902 12.3897 7.5 12.1989 7.5 12C7.5 11.8011 7.57902 11.6103 7.71967 11.4697C7.86032 11.329 8.05109 11.25 8.25 11.25H11.25V8.25C11.25 8.05109 11.329 7.86032 11.4697 7.71967C11.6103 7.57902 11.8011 7.5 12 7.5C12.1989 7.5 12.3897 7.57902 12.5303 7.71967C12.671 7.86032 12.75 8.05109 12.75 8.25V11.25H15.75C15.9489 11.25 16.1397 11.329 16.2803 11.4697C16.421 11.6103 16.5 11.8011 16.5 12Z" fill="currentColor"/></svg>',minus_square:'<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H19.5C19.8978 21 20.2794 20.842 20.5607 20.5607C20.842 20.2794 21 19.8978 21 19.5V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM19.5 19.5H4.5V4.5H19.5V19.5ZM16.5 12C16.5 12.1989 16.421 12.3897 16.2803 12.5303C16.1397 12.671 15.9489 12.75 15.75 12.75H8.25C8.05109 12.75 7.86032 12.671 7.71967 12.5303C7.57902 12.3897 7.5 12.1989 7.5 12C7.5 11.8011 7.57902 11.6103 7.71967 11.4697C7.86032 11.329 8.05109 11.25 8.25 11.25H15.75C15.9489 11.25 16.1397 11.329 16.2803 11.4697C16.421 11.6103 16.5 11.8011 16.5 12Z" fill="currentColor"/></svg>',plus_circle:'<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2.25C10.0716 2.25 8.18657 2.82183 6.58319 3.89317C4.97982 4.96451 3.73013 6.48726 2.99218 8.26884C2.25422 10.0504 2.06114 12.0108 2.43735 13.9021C2.81355 15.7934 3.74215 17.5307 5.10571 18.8943C6.46928 20.2579 8.20656 21.1865 10.0979 21.5627C11.9892 21.9389 13.9496 21.7458 15.7312 21.0078C17.5127 20.2699 19.0355 19.0202 20.1068 17.4168C21.1782 15.8134 21.75 13.9284 21.75 12C21.7473 9.41498 20.7192 6.93661 18.8913 5.10872C17.0634 3.28084 14.585 2.25273 12 2.25ZM12 20.25C10.3683 20.25 8.77326 19.7661 7.41655 18.8596C6.05984 17.9531 5.00242 16.6646 4.378 15.1571C3.75358 13.6496 3.5902 11.9908 3.90853 10.3905C4.22685 8.79016 5.01259 7.32015 6.16637 6.16637C7.32016 5.01259 8.79017 4.22685 10.3905 3.90852C11.9909 3.59019 13.6497 3.75357 15.1571 4.37799C16.6646 5.00242 17.9531 6.05984 18.8596 7.41655C19.7661 8.77325 20.25 10.3683 20.25 12C20.2475 14.1873 19.3775 16.2843 17.8309 17.8309C16.2843 19.3775 14.1873 20.2475 12 20.25ZM16.5 12C16.5 12.1989 16.421 12.3897 16.2803 12.5303C16.1397 12.671 15.9489 12.75 15.75 12.75H12.75V15.75C12.75 15.9489 12.671 16.1397 12.5303 16.2803C12.3897 16.421 12.1989 16.5 12 16.5C11.8011 16.5 11.6103 16.421 11.4697 16.2803C11.329 16.1397 11.25 15.9489 11.25 15.75V12.75H8.25C8.05109 12.75 7.86033 12.671 7.71967 12.5303C7.57902 12.3897 7.5 12.1989 7.5 12C7.5 11.8011 7.57902 11.6103 7.71967 11.4697C7.86033 11.329 8.05109 11.25 8.25 11.25H11.25V8.25C11.25 8.05109 11.329 7.86032 11.4697 7.71967C11.6103 7.57902 11.8011 7.5 12 7.5C12.1989 7.5 12.3897 7.57902 12.5303 7.71967C12.671 7.86032 12.75 8.05109 12.75 8.25V11.25H15.75C15.9489 11.25 16.1397 11.329 16.2803 11.4697C16.421 11.6103 16.5 11.8011 16.5 12Z" fill="currentColor"/></svg>',minus_circle:'<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M16.5 12C16.5 12.1989 16.421 12.3897 16.2803 12.5303C16.1397 12.671 15.9489 12.75 15.75 12.75H8.25C8.05109 12.75 7.86033 12.671 7.71967 12.5303C7.57902 12.3897 7.5 12.1989 7.5 12C7.5 11.8011 7.57902 11.6103 7.71967 11.4697C7.86033 11.329 8.05109 11.25 8.25 11.25H15.75C15.9489 11.25 16.1397 11.329 16.2803 11.4697C16.421 11.6103 16.5 11.8011 16.5 12ZM21.75 12C21.75 13.9284 21.1782 15.8134 20.1068 17.4168C19.0355 19.0202 17.5127 20.2699 15.7312 21.0078C13.9496 21.7458 11.9892 21.9389 10.0979 21.5627C8.20656 21.1865 6.46928 20.2579 5.10571 18.8943C3.74215 17.5307 2.81355 15.7934 2.43735 13.9021C2.06114 12.0108 2.25422 10.0504 2.99218 8.26884C3.73013 6.48726 4.97982 4.96451 6.58319 3.89317C8.18657 2.82183 10.0716 2.25 12 2.25C14.585 2.25273 17.0634 3.28084 18.8913 5.10872C20.7192 6.93661 21.7473 9.41498 21.75 12ZM20.25 12C20.25 10.3683 19.7661 8.77325 18.8596 7.41655C17.9531 6.05984 16.6646 5.00242 15.1571 4.37799C13.6497 3.75357 11.9909 3.59019 10.3905 3.90852C8.79017 4.22685 7.32016 5.01259 6.16637 6.16637C5.01259 7.32015 4.22685 8.79016 3.90853 10.3905C3.5902 11.9908 3.75358 13.6496 4.378 15.1571C5.00242 16.6646 6.05984 17.9531 7.41655 18.8596C8.77326 19.7661 10.3683 20.25 12 20.25C14.1873 20.2475 16.2843 19.3775 17.8309 17.8309C19.3775 16.2843 20.2475 14.1873 20.25 12Z" fill="currentColor"/></svg>',tick_circle_fill:'<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2.25C10.0716 2.25 8.18657 2.82183 6.58319 3.89317C4.97982 4.96451 3.73013 6.48726 2.99218 8.26884C2.25422 10.0504 2.06114 12.0108 2.43735 13.9021C2.81355 15.7934 3.74215 17.5307 5.10571 18.8943C6.46928 20.2579 8.20656 21.1865 10.0979 21.5627C11.9892 21.9389 13.9496 21.7458 15.7312 21.0078C17.5127 20.2699 19.0355 19.0202 20.1068 17.4168C21.1782 15.8134 21.75 13.9284 21.75 12C21.7473 9.41498 20.7192 6.93661 18.8913 5.10872C17.0634 3.28084 14.585 2.25273 12 2.25ZM16.2806 10.2806L11.0306 15.5306C10.961 15.6004 10.8783 15.6557 10.7872 15.6934C10.6962 15.7312 10.5986 15.7506 10.5 15.7506C10.4014 15.7506 10.3038 15.7312 10.2128 15.6934C10.1218 15.6557 10.039 15.6004 9.96938 15.5306L7.71938 13.2806C7.57865 13.1399 7.49959 12.949 7.49959 12.75C7.49959 12.551 7.57865 12.3601 7.71938 12.2194C7.86011 12.0786 8.05098 11.9996 8.25 11.9996C8.44903 11.9996 8.6399 12.0786 8.78063 12.2194L10.5 13.9397L15.2194 9.21937C15.2891 9.14969 15.3718 9.09442 15.4628 9.0567C15.5539 9.01899 15.6515 8.99958 15.75 8.99958C15.8486 8.99958 15.9461 9.01899 16.0372 9.0567C16.1282 9.09442 16.2109 9.14969 16.2806 9.21937C16.3503 9.28906 16.4056 9.37178 16.4433 9.46283C16.481 9.55387 16.5004 9.65145 16.5004 9.75C16.5004 9.84855 16.481 9.94613 16.4433 10.0372C16.4056 10.1282 16.3503 10.2109 16.2806 10.2806Z" fill="currentColor"/></svg>',cross_circle_fill:'<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2.25C10.0716 2.25 8.18657 2.82183 6.58319 3.89317C4.97982 4.96451 3.73013 6.48726 2.99218 8.26884C2.25422 10.0504 2.06114 12.0108 2.43735 13.9021C2.81355 15.7934 3.74215 17.5307 5.10571 18.8943C6.46928 20.2579 8.20656 21.1865 10.0979 21.5627C11.9892 21.9389 13.9496 21.7458 15.7312 21.0078C17.5127 20.2699 19.0355 19.0202 20.1068 17.4168C21.1782 15.8134 21.75 13.9284 21.75 12C21.7473 9.41498 20.7192 6.93661 18.8913 5.10872C17.0634 3.28084 14.585 2.25273 12 2.25ZM15.5306 14.4694C15.6003 14.5391 15.6556 14.6218 15.6933 14.7128C15.731 14.8039 15.7504 14.9015 15.7504 15C15.7504 15.0985 15.731 15.1961 15.6933 15.2872C15.6556 15.3782 15.6003 15.4609 15.5306 15.5306C15.4609 15.6003 15.3782 15.6556 15.2872 15.6933C15.1961 15.731 15.0986 15.7504 15 15.7504C14.9015 15.7504 14.8039 15.731 14.7128 15.6933C14.6218 15.6556 14.5391 15.6003 14.4694 15.5306L12 13.0603L9.53063 15.5306C9.46095 15.6003 9.37822 15.6556 9.28718 15.6933C9.19613 15.731 9.09855 15.7504 9 15.7504C8.90146 15.7504 8.80388 15.731 8.71283 15.6933C8.62179 15.6556 8.53906 15.6003 8.46938 15.5306C8.3997 15.4609 8.34442 15.3782 8.30671 15.2872C8.269 15.1961 8.24959 15.0985 8.24959 15C8.24959 14.9015 8.269 14.8039 8.30671 14.7128C8.34442 14.6218 8.3997 14.5391 8.46938 14.4694L10.9397 12L8.46938 9.53063C8.32865 9.38989 8.24959 9.19902 8.24959 9C8.24959 8.80098 8.32865 8.61011 8.46938 8.46937C8.61011 8.32864 8.80098 8.24958 9 8.24958C9.19903 8.24958 9.3899 8.32864 9.53063 8.46937L12 10.9397L14.4694 8.46937C14.5391 8.39969 14.6218 8.34442 14.7128 8.3067C14.8039 8.26899 14.9015 8.24958 15 8.24958C15.0986 8.24958 15.1961 8.26899 15.2872 8.3067C15.3782 8.34442 15.4609 8.39969 15.5306 8.46937C15.6003 8.53906 15.6556 8.62178 15.6933 8.71283C15.731 8.80387 15.7504 8.90145 15.7504 9C15.7504 9.09855 15.731 9.19613 15.6933 9.28717C15.6556 9.37822 15.6003 9.46094 15.5306 9.53063L13.0603 12L15.5306 14.4694Z" fill="currentColor"/></svg>',plus_circle_fill:'<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2.25C10.0716 2.25 8.18657 2.82183 6.58319 3.89317C4.97982 4.96451 3.73013 6.48726 2.99218 8.26884C2.25422 10.0504 2.06114 12.0108 2.43735 13.9021C2.81355 15.7934 3.74215 17.5307 5.10571 18.8943C6.46928 20.2579 8.20656 21.1865 10.0979 21.5627C11.9892 21.9389 13.9496 21.7458 15.7312 21.0078C17.5127 20.2699 19.0355 19.0202 20.1068 17.4168C21.1782 15.8134 21.75 13.9284 21.75 12C21.7468 9.41513 20.7185 6.93705 18.8907 5.10927C17.063 3.28149 14.5849 2.25323 12 2.25ZM15.75 12.75H12.75V15.75C12.75 15.9489 12.671 16.1397 12.5303 16.2803C12.3897 16.421 12.1989 16.5 12 16.5C11.8011 16.5 11.6103 16.421 11.4697 16.2803C11.329 16.1397 11.25 15.9489 11.25 15.75V12.75H8.25C8.05109 12.75 7.86033 12.671 7.71967 12.5303C7.57902 12.3897 7.5 12.1989 7.5 12C7.5 11.8011 7.57902 11.6103 7.71967 11.4697C7.86033 11.329 8.05109 11.25 8.25 11.25H11.25V8.25C11.25 8.05109 11.329 7.86032 11.4697 7.71967C11.6103 7.57902 11.8011 7.5 12 7.5C12.1989 7.5 12.3897 7.57902 12.5303 7.71967C12.671 7.86032 12.75 8.05109 12.75 8.25V11.25H15.75C15.9489 11.25 16.1397 11.329 16.2803 11.4697C16.421 11.6103 16.5 11.8011 16.5 12C16.5 12.1989 16.421 12.3897 16.2803 12.5303C16.1397 12.671 15.9489 12.75 15.75 12.75Z" fill="currentColor"/></svg>',minus_circle_fill:'<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2.25C10.0716 2.25 8.18657 2.82183 6.58319 3.89317C4.97982 4.96451 3.73013 6.48726 2.99218 8.26884C2.25422 10.0504 2.06114 12.0108 2.43735 13.9021C2.81355 15.7934 3.74215 17.5307 5.10571 18.8943C6.46928 20.2579 8.20656 21.1865 10.0979 21.5627C11.9892 21.9389 13.9496 21.7458 15.7312 21.0078C17.5127 20.2699 19.0355 19.0202 20.1068 17.4168C21.1782 15.8134 21.75 13.9284 21.75 12C21.7473 9.41498 20.7192 6.93661 18.8913 5.10872C17.0634 3.28084 14.585 2.25273 12 2.25ZM15.75 12.75H8.25C8.05109 12.75 7.86033 12.671 7.71967 12.5303C7.57902 12.3897 7.5 12.1989 7.5 12C7.5 11.8011 7.57902 11.6103 7.71967 11.4697C7.86033 11.329 8.05109 11.25 8.25 11.25H15.75C15.9489 11.25 16.1397 11.329 16.2803 11.4697C16.421 11.6103 16.5 11.8011 16.5 12C16.5 12.1989 16.421 12.3897 16.2803 12.5303C16.1397 12.671 15.9489 12.75 15.75 12.75Z" fill="currentColor"/></svg>',plus_square_fill:'<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H19.5C19.8978 21 20.2794 20.842 20.5607 20.5607C20.842 20.2794 21 19.8978 21 19.5V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM15.75 12.75H12.75V15.75C12.75 15.9489 12.671 16.1397 12.5303 16.2803C12.3897 16.421 12.1989 16.5 12 16.5C11.8011 16.5 11.6103 16.421 11.4697 16.2803C11.329 16.1397 11.25 15.9489 11.25 15.75V12.75H8.25C8.05109 12.75 7.86032 12.671 7.71967 12.5303C7.57902 12.3897 7.5 12.1989 7.5 12C7.5 11.8011 7.57902 11.6103 7.71967 11.4697C7.86032 11.329 8.05109 11.25 8.25 11.25H11.25V8.25C11.25 8.05109 11.329 7.86032 11.4697 7.71967C11.6103 7.57902 11.8011 7.5 12 7.5C12.1989 7.5 12.3897 7.57902 12.5303 7.71967C12.671 7.86032 12.75 8.05109 12.75 8.25V11.25H15.75C15.9489 11.25 16.1397 11.329 16.2803 11.4697C16.421 11.6103 16.5 11.8011 16.5 12C16.5 12.1989 16.421 12.3897 16.2803 12.5303C16.1397 12.671 15.9489 12.75 15.75 12.75Z" fill="currentColor"/></svg>',minus_square_fill:'<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M19.5 3H4.5C4.10218 3 3.72064 3.15804 3.43934 3.43934C3.15804 3.72064 3 4.10218 3 4.5V19.5C3 19.8978 3.15804 20.2794 3.43934 20.5607C3.72064 20.842 4.10218 21 4.5 21H19.5C19.8978 21 20.2794 20.842 20.5607 20.5607C20.842 20.2794 21 19.8978 21 19.5V4.5C21 4.10218 20.842 3.72064 20.5607 3.43934C20.2794 3.15804 19.8978 3 19.5 3ZM15.75 12.75H8.25C8.05109 12.75 7.86032 12.671 7.71967 12.5303C7.57902 12.3897 7.5 12.1989 7.5 12C7.5 11.8011 7.57902 11.6103 7.71967 11.4697C7.86032 11.329 8.05109 11.25 8.25 11.25H15.75C15.9489 11.25 16.1397 11.329 16.2803 11.4697C16.421 11.6103 16.5 11.8011 16.5 12C16.5 12.1989 16.421 12.3897 16.2803 12.5303C16.1397 12.671 15.9489 12.75 15.75 12.75Z" fill="currentColor"/></svg>'};var Ma=r(5587);var Ia=r(7363);function Pa(e){"@babel/helpers - typeof";return Pa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Pa(e)}function Da(){Da=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return Da.apply(this,arguments)}function Ta(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Wa(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ta(Object(r),!0).forEach((function(t){Ja(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ta(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Ja(e,t,r){t=La(t);if(t in e){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true})}else{e[t]=r}return e}function La(e){var t=Na(e,"string");return Pa(t)==="symbol"?t:String(t)}function Na(e,t){if(Pa(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==undefined){var n=r.call(e,t||"default");if(Pa(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function Fa(e,t){return Ua(e)||Qa(e,t)||za(e,t)||Ba()}function Ba(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function za(e,t){if(!e)return;if(typeof e==="string")return Ra(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor)r=e.constructor.name;if(r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Ra(e,t)}function Ra(e,t){if(t==null||t>e.length)t=e.length;for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function Qa(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==t);l=!0);}catch(e){c=!0,o=e}finally{try{if(!l&&null!=r["return"]&&(a=r["return"](),Object(a)!==a))return}finally{if(c)throw o}}return u}}function Ua(e){if(Array.isArray(e))return e}function Ha(){return"You have tried to stringify object returned from `css` function. It isn't supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop)."}function Va(e){var t=e.id,r=e.field,i=e.fieldState,a=e.handleDeleteClick;var u=(0,c.useState)(false),l=Fa(u,2),s=l[0],d=l[1];var f=(0,A.l)({isOpen:s}),p=f.triggerRef,v=f.position,b=f.popoverRef;var g=(0,Ma.nB)({id:t,animateLayoutChanges:Sa.h}),y=g.attributes,m=g.listeners,w=g.setNodeRef,C=g.transform,x=g.transition,Z=g.isDragging;var O={transform:Ea.ux.Transform.toString(C?Wa(Wa({},C),{},{scaleX:1,scaleY:1}):null),transition:x,zIndex:Z?1:0};function _(e){r.onChange(Wa(Wa({},r.value),{},{icon:e}))}function k(e){r.onChange(Wa(Wa({},r.value),{},{content:e}))}return(0,n.tZ)("div",{ref:w,style:O},(0,n.tZ)(h.Z,{field:r,fieldState:i,inputStyle:Ga.input},(function(e){var t;return(0,n.tZ)(Ia.Fragment,null,(0,n.tZ)("div",{css:Ga.featureItem},(0,n.tZ)("button",Da({type:"button"},y,m,{css:Ga.dragButton}),(0,n.tZ)(S.Z,{name:"dragVertical",width:24,height:24})),(0,n.tZ)("button",{ref:p,type:"button",css:Ga.iconSelector,onClick:function e(){return d(!s)},dangerouslySetInnerHTML:{__html:Aa[r.value.icon]}}),(0,n.tZ)("input",Da({},e,{value:r.value.content,onChange:function e(t){return k(t.target.value)}})),(0,n.tZ)("button",{css:Ga.deleteButton,type:"button",onClick:a,"data-delete-button":true},(0,n.tZ)(S.Z,{name:"delete",width:24,height:24}))),(0,n.tZ)(A.h,{isOpen:s,onClickOutside:function e(){d(false)},onEscape:function e(){d(false)}},(0,n.tZ)("div",{ref:b,css:[Ga.popoverWrapper,(t={},Ja(t,E.dZ?"right":"left",v.left),Ja(t,"top",v.top),Ja(t,"maxWidth",208),t),true?"":0,true?"":0]},(0,n.tZ)("div",{css:Ga.popoverHeader},(0,n.tZ)("label",null,(0,o.__)("Icons","tutor-pro")),(0,n.tZ)(j.Z,{variant:"text",onClick:function e(){return d(false)}},(0,n.tZ)(S.Z,{name:"cross",width:24,height:24}))),(0,n.tZ)("div",{css:Ga.popoverContent},(0,n.tZ)(Ce.Z,{each:Object.getOwnPropertyNames(Aa)},(function(e){return(0,n.tZ)("button",{css:Ga.popoverContentButton,type:"button",onClick:function t(){_(e);d(false)},dangerouslySetInnerHTML:{__html:Aa[e]}})}))))))})))}var Ga={featureItem:true?{name:"ia8y9z",styles:"position:relative;display:flex;&:hover{button[data-delete-button]{opacity:1;}}"}:0,input:(0,n.iv)("&.tutor-input-field{border-top-left-radius:0;border-bottom-left-radius:0;padding:",a.W0[4]," ",a.W0[36]," ",a.W0[4]," ",a.W0[8],";&:focus{border-radius:",a.E0[6],";}}"+(true?"":0),true?"":0),iconSelector:(0,n.iv)("height:40px;display:flex;align-items:center;background-color:",a.Jv.background.white,";color:",a.Jv.icon.hover,";border:1px solid ",a.Jv.stroke["default"],";border-right:none;border-top-left-radius:",a.E0[6],";border-bottom-left-radius:",a.E0[6],";cursor:pointer;transition:background-color 0.25s;:hover{background-color:",a.Jv.background.hover,";}:focus-visible{border-radius:",a.E0[4],";outline:2px solid ",a.Jv.stroke.brand,";outline-offset:2px;z-index:1;}"+(true?"":0),true?"":0),dragButton:(0,n.iv)("display:flex;align-items:center;padding:0;color:",a.Jv.icon["default"],";background:transparent;border:none;cursor:grab;:focus-visible{border-radius:",a.E0[4],";outline:2px solid ",a.Jv.stroke.brand,";}"+(true?"":0),true?"":0),deleteButton:(0,n.iv)("display:flex;position:absolute;right:",a.W0[12],";top:",a.W0[8],";padding:0;color:",a.Jv.icon["default"],";background:transparent;border:none;cursor:pointer;opacity:0;transition:opacity 0.25s;:focus-visible{border-radius:",a.E0[2],";outline:2px solid ",a.Jv.stroke.brand,";outline-offset:2px;opacity:1;}",a.Uo.mobile,"{opacity:1;}"+(true?"":0),true?"":0),popoverWrapper:(0,n.iv)("position:absolute;width:100%;z-index:",a.W5.dropdown,";background-color:",a.Jv.background.white,";box-shadow:",a.AF.popover,";border-radius:",a.E0[6],";max-height:300px;overflow-y:auto;"+(true?"":0),true?"":0),popoverHeader:(0,n.iv)("display:flex;justify-content:space-between;align-items:center;border-bottom:1px solid ",a.Jv.stroke.divider,";padding:",a.W0[8],";label{",u.c.caption("medium"),";color:",a.Jv.text.title,";}button{padding:0px;}"+(true?"":0),true?"":0),popoverContent:(0,n.iv)("display:flex;flex-wrap:wrap;gap:",a.W0[8],";padding:",a.W0[12],";"+(true?"":0),true?"":0),popoverContentButton:(0,n.iv)("display:flex;background-color:",a.Jv.background["default"],";color:",a.Jv.icon.hover,";border:none;border-radius:",a.E0[4],";padding:",a.W0[8],";cursor:pointer;transition:background-color 0.25s,box-shadow 0.25s;:hover{background-color:",a.Jv.background.hover,";box-shadow:inset 0px 0px 0px 1px ",a.Jv.action.primary.hover,";}:focus-visible{border-radius:",a.E0[6],";outline:2px solid ",a.Jv.stroke.brand,";outline-offset:2px;z-index:1;}"+(true?"":0),true?"":0)};var Ya=r(9752);var qa=r(2339);function $a(){$a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return $a.apply(this,arguments)}function Ka(){var e=(0,i.Gc)();var t=(0,i.Dq)({control:e.control,name:"features"}),r=t.fields,a=t.append,u=t.remove,c=t.move;var s=(0,Ya.Dy)((0,Ya.VT)(Ya.we),(0,Ya.VT)(Ya.Lg,{coordinateGetter:Ma.is}));return(0,n.tZ)("div",{css:Xa.wrapper},(0,n.tZ)("div",{css:Xa.header},(0,n.tZ)("label",null,(0,o.__)("Features","tutor-pro")),(0,n.tZ)(j.Z,{variant:"text",onClick:function e(){return a({id:(0,l.x0)(),icon:"tick_circle_fill",content:""})}},(0,n.tZ)(S.Z,{name:"plus",width:24,height:24}))),(0,n.tZ)(Oe.Z,{when:r.length>0},(0,n.tZ)("div",{css:Xa.features},(0,n.tZ)(Ya.LB,{sensors:s,modifiers:[qa.F4],onDragEnd:function e(t){var n=t.active,o=t.over;if(!o){return}if(n.id!==o.id){var i=r.findIndex((function(e){return e.id===n.id}));var a=r.findIndex((function(e){return e.id===o.id}));c(i,a)}}},(0,n.tZ)(Ma.Fo,{items:r,strategy:Ma.qw},(0,n.tZ)(Ce.Z,{each:r},(function(t,r){return(0,n.tZ)(i.Qr,{key:t.id,control:e.control,name:"features.".concat(r),rules:{validate:function e(t){return!!(t!==null&&t!==void 0&&t.content)||(0,o.__)("Content is required","tutor-pro")}},render:function e(o){return(0,n.tZ)(Va,$a({id:t.id},o,{handleDeleteClick:function e(){return u(r)}}))}})})))))))}var Xa={wrapper:(0,n.iv)("background-color:",a.Jv.background.white,";border:1px solid ",a.Jv.stroke.divider,";border-radius:",a.E0[6],";padding:",a.W0[12]," ",a.W0[16],";"+(true?"":0),true?"":0),header:(0,n.iv)("display:flex;align-items:center;justify-content:space-between;label{",u.c.caption(),";color:",a.Jv.text.title,";}button{color:",a.Jv.icon["default"],";border:1px solid ",a.Jv.stroke["default"],";border-radius:",a.E0[4],";padding:3px;}"+(true?"":0),true?"":0),features:(0,n.iv)("display:flex;flex-direction:column;gap:",a.W0[8],";padding:",a.W0[12]," 0 ",a.W0[8],";"+(true?"":0),true?"":0)};function eu(e){"@babel/helpers - typeof";return eu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},eu(e)}function tu(){tu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r){if(Object.prototype.hasOwnProperty.call(r,n)){e[n]=r[n]}}}return e};return tu.apply(this,arguments)}function ru(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function nu(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ru(Object(r),!0).forEach((function(t){ou(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ru(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function ou(e,t,r){t=iu(t);if(t in e){Object.defineProperty(e,t,{value:r,enumerable:true,configurable:true,writable:true})}else{e[t]=r}return e}function iu(e){var t=au(e,"string");return eu(t)==="symbol"?t:String(t)}function au(e,t){if(eu(e)!=="object"||e===null)return e;var r=e[Symbol.toPrimitive];if(r!==undefined){var n=r.call(e,t||"default");if(eu(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function uu(e){return du(e)||su(e)||cu(e)||lu()}function lu(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function cu(e,t){if(!e)return;if(typeof e==="string")return fu(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor)r=e.constructor.name;if(r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return fu(e,t)}function su(e){if(typeof Symbol!=="undefined"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function du(e){if(Array.isArray(e))return fu(e)}function fu(e,t){if(t==null||t>e.length)t=e.length;for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var pu=Nr.y.tutor_currency;function vu(){var e=(0,i.Gc)();var t=e.watch("charge_enrollment_fee");var r=e.watch("offer_sale_price");var a=e.watch("regular_price");var u=!!e.watch("schedule_sale_price");var l=!!e.watch("is_featured");var c=!!e.watch("enable_trial");var s=[3,6,9,12];var d=[].concat(uu(s.map((function(e){return{label:(0,o.sprintf)((0,o.__)("%s times","tutor-pro"),e.toString()),value:String(e)}}))),[{label:(0,o.__)("Until cancelled","tutor-pro"),value:"Until cancelled"}]);var f=e.watch("plan_type");var p=[{label:(0,o.__)("Full Site","tutor-pro"),value:"full_site"},{label:(0,o.__)("Specific Categories","tutor-pro"),value:"category"}];return(0,n.tZ)("div",{css:bu.container},(0,n.tZ)(i.Qr,{control:e.control,name:"plan_name",rules:nu(nu({},rn()),{},{maxLength:{value:100,message:(0,o.__)("Plan name should be less than 100 characters","tutor-pro")}}),render:function e(t){return(0,n.tZ)(zn,tu({},t,{label:(0,o.__)("Title","tutor-pro"),placeholder:(0,o.__)("e.g., Silver Membership","tutor-pro")}))}}),(0,n.tZ)(i.Qr,{control:e.control,name:"short_description",rules:nu(nu({},rn()),{},{maxLength:{value:200,message:(0,o.__)("Short description should be less than 200 characters","tutor-pro")}}),render:function e(t){return(0,n.tZ)(zn,tu({},t,{label:(0,o.__)("Short Description","tutor-pro"),placeholder:(0,o.__)("e.g., Perfect for beginners looking for weekly classes","tutor-pro")}))}}),(0,n.tZ)("div",{css:bu.inputGroup({numberOfColumn:4})},(0,n.tZ)(i.Qr,{control:e.control,name:"regular_price",rules:nu(nu({},rn()),{},{validate:function e(t){if(Number(t)<1){return(0,o.__)("This value must be equal to or greater than 1","tutor-pro")}return true}}),render:function e(t){return(0,n.tZ)(Yn,tu({},t,{label:(0,o.__)("Price","tutor-pro"),content:(pu===null||pu===void 0?void 0:pu.symbol)||"$",placeholder:(0,o.__)("Plan price","tutor-pro"),selectOnFocus:true,contentCss:M.i.inputCurrencyStyle,type:"number"}))}}),(0,n.tZ)(i.Qr,{control:e.control,name:"recurring_value",rules:nu(nu({},rn()),{},{validate:function e(t){if(Number(t)<1){return(0,o.__)("This value must be equal to or greater than 1","tutor-pro")}if(Number(t)%1!==0){return(0,o.__)("This value can not be fractional","tutor-pro")}return true}}),render:function e(t){return(0,n.tZ)(zn,tu({},t,{label:(0,o.__)("Billing Interval","tutor-pro"),placeholder:(0,o.__)("12","tutor-pro"),selectOnFocus:true,type:"number",onChange:function e(r){var n=String(r).includes(".");if(n){t.field.onChange(String(r).replace(".",""))}}}))}}),(0,n.tZ)(i.Qr,{control:e.control,name:"recurring_interval",render:function e(t){return(0,n.tZ)(Ke,tu({},t,{label:E.iM.isAboveMobile?(0,n.tZ)("div",null," "):(0,o.__)("Recurring Options","tutor-pro"),options:[{label:(0,o.__)("Day(s)","tutor-pro"),value:"day"},{label:(0,o.__)("Week(s)","tutor-pro"),value:"week"},{label:(0,o.__)("Month(s)","tutor-pro"),value:"month"},{label:(0,o.__)("Year(s)","tutor-pro"),value:"year"}],removeOptionsMinWidth:true}))}}),(0,n.tZ)(i.Qr,{control:e.control,name:"recurring_limit",rules:nu(nu({},rn()),{},{validate:function e(t){if(t==="Until cancelled"){return true}if(Number(t)<=0){return(0,o.__)("Renew plan must be greater than 0","tutor-pro")}return true}}),render:function e(t){return(0,n.tZ)(bo,tu({},t,{label:(0,o.__)("Billing Cycles","tutor-pro"),placeholder:(0,o.__)("Select or type times to renewing the plan","tutor-pro"),content:t.field.value!=="Until cancelled"&&(0,o.__)("Times","tutor-pro"),contentPosition:"right",type:"number",presetOptions:d,selectOnFocus:true}))}})),(0,n.tZ)(i.Qr,{control:e.control,name:"plan_type",render:function e(t){return(0,n.tZ)(ko,tu({},t,{label:(0,o.__)("Membership Type","tutor-pro"),options:p,wrapperCss:bu.planTypeWrapper}))}}),(0,n.tZ)(Oe.Z,{when:f==="category"},(0,n.tZ)(ka,{form:e})),(0,n.tZ)(Ka,null),(0,n.tZ)(i.Qr,{control:e.control,name:"charge_enrollment_fee",render:function e(t){return(0,n.tZ)(x,tu({},t,{label:(0,o.__)("Charge enrollment fee","tutor-pro")}))}}),(0,n.tZ)(Oe.Z,{when:t},(0,n.tZ)(i.Qr,{control:e.control,name:"enrollment_fee",rules:nu(nu({},rn()),{},{validate:function e(t){if(Number(t)<=0){return(0,o.__)("Enrollment fee must be greater than 0","tutor-pro")}return true}}),render:function e(t){return(0,n.tZ)(Yn,tu({},t,{label:(0,o.__)("Enrollment Fee","tutor-pro"),content:(pu===null||pu===void 0?void 0:pu.symbol)||"$",placeholder:(0,o.__)("Enter enrollment fee","tutor-pro"),selectOnFocus:true,contentCss:M.i.inputCurrencyStyle,type:"number"}))}})),(0,n.tZ)(i.Qr,{control:e.control,name:"enable_trial",render:function e(t){return(0,n.tZ)(x,tu({},t,{label:(0,o.__)("Offer a trial period","tutor-pro")}))}}),(0,n.tZ)(Oe.Z,{when:c},(0,n.tZ)("div",{css:bu.inputGroup({numberOfColumn:2})},(0,n.tZ)(i.Qr,{control:e.control,name:"trial_value",rules:nu(nu({},rn()),{},{validate:function e(t){if(Number(t)<=0){return(0,o.__)("Trial duration must be greater than 0","tutor-pro")}if(Number(t)%1!==0){return(0,o.__)("Trial duration can not be fractional","tutor-pro")}return true}}),render:function e(t){return(0,n.tZ)(Yn,tu({},t,{label:(0,o.__)("Length of Trial","tutor-pro"),placeholder:(0,o.__)("Enter trial duration","tutor-pro"),selectOnFocus:true,type:"number",contentPosition:"right",showVerticalBar:false,content:Number(t.field.value)>1?(0,o.__)("Days","tutor-pro"):(0,o.__)("Day","tutor-pro"),onChange:function e(r){var n=String(r).includes(".");if(n){t.field.onChange(String(r).replace(".",""))}}}))}}),(0,n.tZ)(i.Qr,{control:e.control,name:"trial_fee",render:function e(t){return(0,n.tZ)(Yn,tu({},t,{label:(0,o.__)("Price","tutor-pro"),placeholder:(0,o.__)("Price","tutor-pro"),contentPosition:Number(t.field.value)>0?"left":"right",content:Number(t.field.value)>0?(pu===null||pu===void 0?void 0:pu.symbol)||"$":"Free",selectOnFocus:true,contentCss:Number(t.field.value)>0?M.i.inputCurrencyStyle:undefined,showVerticalBar:Number(t.field.value)>0,type:"number"}))}}))),(0,n.tZ)(i.Qr,{control:e.control,name:"do_not_provide_certificate",render:function e(t){return(0,n.tZ)(x,tu({},t,{label:(0,o.__)("Do not provide certificate","tutor-pro")}))}}),(0,n.tZ)(i.Qr,{control:e.control,name:"is_featured",render:function e(t){return(0,n.tZ)(x,tu({},t,{label:(0,o.__)("Mark as featured","tutor-pro")}))}}),(0,n.tZ)(Oe.Z,{when:l},(0,n.tZ)(i.Qr,{control:e.control,name:"featured_text",rules:{maxLength:{value:100,message:(0,o.__)("Feature text should be less than 100 characters","tutor-pro")}},render:function e(t){return(0,n.tZ)(zn,tu({},t,{label:(0,o.__)("Feature Text","tutor-pro")}))}})),(0,n.tZ)("div",{css:bu.salePriceWrapper},(0,n.tZ)("div",null,(0,n.tZ)(i.Qr,{control:e.control,name:"offer_sale_price",render:function e(t){return(0,n.tZ)(Yr.Z,tu({},t,{label:(0,o.__)("Offer sale price","tutor-pro")}))}})),(0,n.tZ)(Oe.Z,{when:r},(0,n.tZ)("div",{css:bu.salePriceInputs},(0,n.tZ)(i.Qr,{control:e.control,name:"sale_price",rules:nu(nu({},rn()),{},{validate:function e(t){if(t&&a&&Number(t)>=Number(a)){return(0,o.__)("Sale price should be less than regular price","tutor-pro")}if(t&&a&&Number(t)<=0){return(0,o.__)("Sale price should be greater than 0","tutor-pro")}return undefined}}),render:function e(t){return(0,n.tZ)(Yn,tu({},t,{type:"number",label:(0,o.__)("Sale Price","tutor-pro"),content:(pu===null||pu===void 0?void 0:pu.symbol)||"$",selectOnFocus:true,contentCss:M.i.inputCurrencyStyle}))}}),(0,n.tZ)(i.Qr,{control:e.control,name:"schedule_sale_price",render:function e(t){return(0,n.tZ)(x,tu({},t,{label:(0,o.__)("Schedule the sale price","tutor-pro")}))}}),(0,n.tZ)(Oe.Z,{when:u},(0,n.tZ)("div",{css:bu.datetimeWrapper},(0,n.tZ)("label",null,(0,o.__)("Sale Starts From","tutor-pro")),(0,n.tZ)("div",{css:M.i.dateAndTimeWrapper},(0,n.tZ)(i.Qr,{name:"sale_price_from_date",control:e.control,rules:{required:(0,o.__)("Schedule date is required","tutor-pro")},render:function e(t){return(0,n.tZ)($,tu({},t,{isClearable:false,placeholder:"yyyy-mm-dd",disabledBefore:(new Date).toISOString()}))}}),(0,n.tZ)(i.Qr,{name:"sale_price_from_time",control:e.control,rules:{required:(0,o.__)("Schedule time is required","tutor-pro")},render:function e(t){return(0,n.tZ)(Vo,tu({},t,{interval:60,isClearable:false,placeholder:"hh:mm A"}))}}))),(0,n.tZ)("div",{css:bu.datetimeWrapper},(0,n.tZ)("label",null,(0,o.__)("Sale Ends To","tutor-pro")),(0,n.tZ)("div",{css:M.i.dateAndTimeWrapper},(0,n.tZ)(i.Qr,{name:"sale_price_to_date",control:e.control,rules:{required:(0,o.__)("Schedule date is required","tutor-pro"),validate:{checkEndDate:function t(r){var n=e.watch("sale_price_from_date");var i=r;if(n&&i){return new Date(n)>new Date(i)?(0,o.__)("Sales End date should be greater than start date","tutor-pro"):undefined}return undefined}},deps:["sale_price_from_date"]},render:function t(r){return(0,n.tZ)($,tu({},r,{isClearable:false,placeholder:"yyyy-mm-dd",disabledBefore:e.watch("sale_price_from_date")||undefined}))}}),(0,n.tZ)(i.Qr,{name:"sale_price_to_time",control:e.control,rules:{required:(0,o.__)("Schedule time is required","tutor-pro"),validate:{checkEndTime:function t(r){var n=e.watch("sale_price_from_date");var i=e.watch("sale_price_from_time");var a=e.watch("sale_price_to_date");var u=r;if(n&&a&&i&&u){return new Date("".concat(n," ").concat(i))>new Date("".concat(a," ").concat(u))?(0,o.__)("Sales End time should be greater than start time","tutor-pro"):undefined}return undefined}},deps:["sale_price_from_date","sale_price_from_time","sale_price_to_date"]},render:function e(t){return(0,n.tZ)(Vo,tu({},t,{interval:60,isClearable:false,placeholder:"hh:mm A"}))}}))))))))}var bu={container:(0,n.iv)("width:100%;max-width:640px;margin:0 auto;border:1px solid ",a.Jv.stroke["default"],";border-radius:",a.E0.card,";padding:",a.W0[16],";display:flex;flex-direction:column;gap:",a.W0[12],";"+(true?"":0),true?"":0),salePriceWrapper:(0,n.iv)("background-color:",a.Jv.background.white,";display:flex;flex-direction:column;gap:",a.W0[20],";padding:",a.W0[12],";border:1px solid ",a.Jv.stroke["default"],";border-radius:",a.E0.card,";"+(true?"":0),true?"":0),salePriceInputs:(0,n.iv)("display:flex;flex-direction:column;gap:",a.W0[8],";"+(true?"":0),true?"":0),inputGroup:function e(t){var r=t.numberOfColumn,o=r===void 0?4:r;return(0,n.iv)("display:grid;grid-template-columns:",o===4?"1fr 0.7fr 1fr 1fr":"repeat(".concat(o,", 1fr)"),";align-items:start;gap:",a.W0[8],";",a.Uo.mobile,"{grid-template-columns:1fr;}"+(true?"":0),true?"":0)},datetimeWrapper:(0,n.iv)("label{",u.c.caption(),";color:",a.Jv.text.title,";}"+(true?"":0),true?"":0),planTypeWrapper:(0,n.iv)("display:flex;gap:",a.W0[8],";"+(true?"":0),true?"":0)}}}]);